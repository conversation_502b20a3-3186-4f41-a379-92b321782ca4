#!/usr/bin/env python3
"""
Day 2 Feature Test Suite
Test Chess, Spectator Betting, and Enhanced Features
"""
import asyncio
import socketio
import requests
import json
from datetime import datetime

# Create clients for testing
alice = socketio.AsyncClient()  # Chess player 1
bob = socketio.AsyncClient()    # Chess player 2
charlie = socketio.AsyncClient() # RPS player 1
diana = socketio.AsyncClient()   # RPS player 2
eve = socketio.AsyncClient()     # Spectator/Bettor
frank = socketio.AsyncClient()   # Spectator/Bettor

# Test state
chess_game_id = None
rps_game_id = None
test_results = []

def log_result(test_name: str, success: bool, details: str = ""):
    """Log test result."""
    status = "✅ PASS" if success else "❌ FAIL"
    test_results.append(f"{status} {test_name}: {details}")
    print(f"{status} {test_name}: {details}")

# Event handlers for Alice (Chess White)
@alice.event
async def connect():
    log_result("Alice Connection", True, "Connected to server")

@alice.event
async def login_success(data):
    log_result("Alice Login", True, f"Logged in as {data['username']}")

@alice.event
async def joined_chess_game(data):
    log_result("Alice Chess Join", True, f"Joined as {data['color']} player")

@alice.event
async def chess_game_started(data):
    log_result("Chess Game Start", True, f"Game started: {data['message']}")

@alice.event
async def chess_move_made(data):
    log_result("Chess Move Broadcast", True, f"Move broadcasted to all players")

# Event handlers for Bob (Chess Black)
@bob.event
async def connect():
    log_result("Bob Connection", True, "Connected to server")

@bob.event
async def login_success(data):
    log_result("Bob Login", True, f"Logged in as {data['username']}")

@bob.event
async def joined_chess_game(data):
    log_result("Bob Chess Join", True, f"Joined as {data['color']} player")

# Event handlers for Eve (Spectator/Bettor)
@eve.event
async def connect():
    log_result("Eve Connection", True, "Connected to server")

@eve.event
async def login_success(data):
    log_result("Eve Login", True, f"Logged in with balance: ${data['user_data']['balance']}")

@eve.event
async def joined_as_spectator(data):
    log_result("Eve Spectator Join", True, f"Joined as spectator with betting options")

@eve.event
async def bet_confirmed(data):
    log_result("Eve Bet Placement", True, f"Bet placed: ${data['bet']['amount']} at {data['bet']['odds']}x odds")

@eve.event
async def bet_placed(data):
    log_result("Bet Broadcast", True, f"Bet placement broadcasted to all spectators")

async def test_day2_features():
    """Test all Day 2 features."""
    print("🚀 Testing BetBet Day 2 Features")
    print("=" * 50)
    
    try:
        # 1. Test API endpoints
        print("\n1️⃣ Testing Enhanced API...")
        
        # Test server status
        response = requests.get('http://localhost:8003/')
        if response.status_code == 200:
            data = response.json()
            log_result("Enhanced API", True, f"Version {data['version']} with {len(data['features'])} features")
        else:
            log_result("Enhanced API", False, f"Status code: {response.status_code}")
        
        # Create chess game
        response = requests.post('http://localhost:8003/api/games/create', json={
            'type': 'chess',
            'player1_id': 'alice_gamer',
            'wager': 300
        })
        if response.status_code == 200:
            global chess_game_id
            chess_game_id = response.json()['game_id']
            log_result("Chess Game Creation", True, f"Created chess game: {chess_game_id}")
        else:
            log_result("Chess Game Creation", False, f"Failed to create chess game")
            return
        
        # Create RPS game
        response = requests.post('http://localhost:8003/api/games/create', json={
            'type': 'rock_paper_scissors',
            'wager': 150,
            'rounds': 3
        })
        if response.status_code == 200:
            global rps_game_id
            rps_game_id = response.json()['game_id']
            log_result("RPS Game Creation", True, f"Created RPS game: {rps_game_id}")
        else:
            log_result("RPS Game Creation", False, f"Failed to create RPS game")
        
        # 2. Test WebSocket connections
        print("\n2️⃣ Testing WebSocket Connections...")
        
        await alice.connect('http://localhost:8003')
        await bob.connect('http://localhost:8003')
        await eve.connect('http://localhost:8003')
        
        await asyncio.sleep(1)
        
        # 3. Test logins
        print("\n3️⃣ Testing Enhanced Logins...")
        
        await alice.emit('login', {'username': 'alice_gamer'})
        await bob.emit('login', {'username': 'bob_player'})
        await eve.emit('login', {'username': 'eve_spectator'})
        
        await asyncio.sleep(2)
        
        # 4. Test chess game flow
        print("\n4️⃣ Testing Chess Game Flow...")
        
        # Alice joins chess game (already white player)
        await alice.emit('join_game', {
            'game_id': chess_game_id,
            'role': 'player'
        })
        
        await asyncio.sleep(1)
        
        # Bob joins as black player
        await bob.emit('join_game', {
            'game_id': chess_game_id,
            'role': 'player'
        })
        
        await asyncio.sleep(2)
        
        # Eve joins as spectator
        await eve.emit('join_game', {
            'game_id': chess_game_id,
            'role': 'spectator'
        })
        
        await asyncio.sleep(1)
        
        # 5. Test chess moves
        print("\n5️⃣ Testing Chess Moves...")
        
        # Alice makes opening move (pawn e2 to e4)
        await alice.emit('make_chess_move', {
            'game_id': chess_game_id,
            'from': [6, 4],  # e2
            'to': [4, 4]     # e4
        })
        
        await asyncio.sleep(1)
        
        # Bob responds (pawn e7 to e5)
        await bob.emit('make_chess_move', {
            'game_id': chess_game_id,
            'from': [1, 4],  # e7
            'to': [3, 4]     # e5
        })
        
        await asyncio.sleep(2)
        
        # 6. Test betting system
        print("\n6️⃣ Testing Spectator Betting...")
        
        # Get betting options
        response = requests.get(f'http://localhost:8003/api/games/{chess_game_id}/betting')
        if response.status_code == 200:
            betting_data = response.json()
            log_result("Betting Options", True, f"Available bets: {list(betting_data['available_bets'].keys())}")
            
            # Place a bet on game winner
            if 'game_winner' in betting_data['available_bets']:
                await eve.emit('place_spectator_bet', {
                    'game_id': chess_game_id,
                    'bet_type': 'game_winner',
                    'selection': 'alice_gamer',
                    'amount': 50
                })
                
                await asyncio.sleep(1)
        else:
            log_result("Betting Options", False, "Failed to get betting options")
        
        # 7. Test multiple game rooms
        print("\n7️⃣ Testing Multiple Game Rooms...")
        
        # Check that both games exist
        response = requests.get('http://localhost:8003/api/games')
        if response.status_code == 200:
            games_data = response.json()
            chess_games = [g for g in games_data['games'] if g['type'] == 'chess']
            rps_games = [g for g in games_data['games'] if g['type'] == 'rock_paper_scissors']
            
            log_result("Multiple Game Types", True, f"Chess: {len(chess_games)}, RPS: {len(rps_games)}")
        else:
            log_result("Multiple Game Types", False, "Failed to get games list")
        
        # 8. Test user betting history
        print("\n8️⃣ Testing User Betting History...")
        
        response = requests.get(f'http://localhost:8003/api/users/eve_spectator/bets')
        if response.status_code == 200:
            bet_data = response.json()
            log_result("User Betting History", True, f"Bets: {len(bet_data['bets'])}, Balance: ${bet_data['balance']}")
        else:
            log_result("User Betting History", False, "Failed to get betting history")
        
        await asyncio.sleep(2)
        
        print("\n✅ Day 2 Feature Testing Complete!")
        
    except Exception as e:
        log_result("Day 2 Testing", False, f"Exception: {e}")
    
    finally:
        # Cleanup
        await alice.disconnect()
        await bob.disconnect()
        await eve.disconnect()

async def test_tournament_system():
    """Test tournament system (basic)."""
    print("\n\n🏆 Testing Tournament System")
    print("=" * 30)
    
    try:
        # Import tournament manager
        import sys
        import os
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        
        from app.games.tournament_engine import TournamentManager
        
        # Create tournament manager
        tournament_manager = TournamentManager()
        
        # Create tournament
        tournament_id = tournament_manager.create_tournament(
            name="Chess Championship",
            game_type="chess",
            max_players=4,
            entry_fee=100.0
        )
        
        log_result("Tournament Creation", True, f"Created tournament: {tournament_id}")
        
        # Register players
        players = ["alice_gamer", "bob_player", "charlie_pro", "diana_chess"]
        for player in players:
            success = tournament_manager.register_player(tournament_id, player)
            if success:
                log_result(f"Player Registration", True, f"{player} registered")
            else:
                log_result(f"Player Registration", False, f"{player} failed to register")
        
        # Start tournament
        success = tournament_manager.start_tournament(tournament_id)
        if success:
            log_result("Tournament Start", True, "Tournament started with bracket")
            
            # Get tournament summary
            summary = tournament_manager.get_tournament_summary(tournament_id)
            if summary:
                log_result("Tournament Bracket", True, f"Rounds: {len(summary['bracket'])}")
        else:
            log_result("Tournament Start", False, "Failed to start tournament")
        
    except Exception as e:
        log_result("Tournament System", False, f"Exception: {e}")

def print_test_summary():
    """Print test summary."""
    print("\n" + "=" * 60)
    print("🎯 DAY 2 TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for result in test_results if "✅ PASS" in result)
    failed = sum(1 for result in test_results if "❌ FAIL" in result)
    
    print(f"Total Tests: {len(test_results)}")
    print(f"Passed: {passed}")
    print(f"Failed: {failed}")
    print(f"Success Rate: {passed/len(test_results)*100:.1f}%")
    
    print("\nDetailed Results:")
    for result in test_results:
        print(f"  {result}")
    
    if failed == 0:
        print("\n🎉 ALL TESTS PASSED! Day 2 implementation is successful!")
    else:
        print(f"\n⚠️  {failed} tests failed. Review implementation.")

if __name__ == "__main__":
    print("🧪 BetBet Day 2 Feature Test Suite")
    print("Testing: Chess, Spectator Betting, Multiple Rooms, Tournaments")
    
    # Run main tests
    asyncio.run(test_day2_features())
    
    # Run tournament tests
    asyncio.run(test_tournament_system())
    
    # Print summary
    print_test_summary()
