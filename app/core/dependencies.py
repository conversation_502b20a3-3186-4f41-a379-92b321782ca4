from fastapi import Depends, HTTPException, status, Request
from fastapi.security import OA<PERSON>2P<PERSON>wordBearer
from jose import JW<PERSON>rror
from sqlalchemy.orm import Session
from app.database.db import get_db
from app.models.user import User
from app.core.security import decode_token
from typing import Optional

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")
oauth2_scheme_optional = OAuth2PasswordBearer(tokenUrl="token", auto_error=False)

async def get_current_user(
    token: str = Depends(oauth2_scheme),
    db: Session = Depends(get_db)
) -> User:
    """Get current user from JWT token"""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )

    try:
        payload = decode_token(token)
        user_id: str = payload.get("sub")
        if user_id is None:
            raise credentials_exception
    except JWTError:
        raise credentials_exception

    user = db.query(User).filter(User.id == user_id).first()
    if user is None:
        raise credentials_exception

    return user

async def get_current_user_optional(
    token: Optional[str] = Depends(oauth2_scheme_optional),
    db: Session = Depends(get_db)
) -> Optional[User]:
    """Get current user from JWT token, return None if not authenticated"""
    if not token:
        return None

    try:
        payload = decode_token(token)
        user_id: str = payload.get("sub")
        if user_id is None:
            return None
    except JWTError:
        return None

    user = db.query(User).filter(User.id == user_id).first()
    return user

async def verify_websocket_token(token: str) -> Optional[User]:
    """Verify token for WebSocket connections."""
    try:
        # Get a database session
        db = next(get_db())

        # Verify token
        payload = decode_token(token)
        user_id = payload.get("sub")
        if not user_id:
            return None

        # Get user from database
        user = db.query(User).filter(User.id == user_id).first()
        if not user or not user.is_active:
            return None

        return user
    except Exception as e:
        print(f"WebSocket token verification failed: {e}")
        return None
    finally:
        db.close()