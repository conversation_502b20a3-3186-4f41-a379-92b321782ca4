"""WebSocket configuration and connection management."""
import socketio

# Create Socket.IO server without Redis (use in-memory manager)
sio = socketio.AsyncServer(
    async_mode='asgi',
    cors_allowed_origins="*",  # Configure properly for production
    logger=True,
    engineio_logger=True,
    # Use in-memory manager instead of Redis
    client_manager=socketio.AsyncManager()
)

from typing import Dict, Set, Optional
import json
import logging
from datetime import datetime

# Configure logging
logger = logging.getLogger(__name__)


class ConnectionManager:
    """Manages WebSocket connections and game rooms."""

    def __init__(self):
        # Maps session ID to user ID
        self.user_sessions: Dict[str, str] = {}
        # Maps user ID to set of session IDs (multiple connections)
        self.active_connections: Dict[str, Set[str]] = {}
        # Maps game ID to set of session IDs
        self.game_rooms: Dict[str, Set[str]] = {}
        # Maps session ID to game ID
        self.user_games: Dict[str, str] = {}

    async def connect(self, sid: str, user_id: str) -> None:
        """Register a new connection."""
        self.user_sessions[sid] = user_id

        if user_id not in self.active_connections:
            self.active_connections[user_id] = set()
        self.active_connections[user_id].add(sid)

        logger.info(f"User {user_id} connected with session {sid}")

    async def disconnect(self, sid: str) -> None:
        """Clean up a disconnected session."""
        user_id = self.user_sessions.get(sid)
        if not user_id:
            return

        # Remove from active connections
        self.active_connections[user_id].discard(sid)
        if not self.active_connections[user_id]:
            del self.active_connections[user_id]

        # Remove from any game rooms
        game_id = self.user_games.get(sid)
        if game_id:
            await self.leave_game_room(sid, game_id)

        # Clean up session
        del self.user_sessions[sid]

        logger.info(f"User {user_id} disconnected session {sid}")

    async def join_game_room(self, sid: str, game_id: str) -> None:
        """Add a session to a game room."""
        # Leave any previous game
        if sid in self.user_games:
            await self.leave_game_room(sid, self.user_games[sid])

        # Join new game
        if game_id not in self.game_rooms:
            self.game_rooms[game_id] = set()

        self.game_rooms[game_id].add(sid)
        self.user_games[sid] = game_id

        # Join Socket.IO room
        await sio.enter_room(sid, f"game_{game_id}")

        user_id = self.user_sessions.get(sid)
        logger.info(f"User {user_id} joined game {game_id}")

    async def leave_game_room(self, sid: str, game_id: str) -> None:
        """Remove a session from a game room."""
        if game_id in self.game_rooms:
            self.game_rooms[game_id].discard(sid)
            if not self.game_rooms[game_id]:
                del self.game_rooms[game_id]

        if sid in self.user_games:
            del self.user_games[sid]

        # Leave Socket.IO room
        await sio.leave_room(sid, f"game_{game_id}")

        user_id = self.user_sessions.get(sid)
        logger.info(f"User {user_id} left game {game_id}")

    def get_user_id(self, sid: str) -> Optional[str]:
        """Get user ID for a session."""
        return self.user_sessions.get(sid)

    def get_game_players(self, game_id: str) -> Set[str]:
        """Get all session IDs in a game."""
        return self.game_rooms.get(game_id, set())

    def is_user_in_game(self, user_id: str, game_id: str) -> bool:
        """Check if a user is in a specific game."""
        user_sids = self.active_connections.get(user_id, set())
        game_sids = self.game_rooms.get(game_id, set())
        return bool(user_sids & game_sids)


# Global connection manager instance
manager = ConnectionManager()


# Utility functions for game state broadcasting
async def broadcast_game_update(game_id: str, data: dict, exclude_sid: Optional[str] = None):
    """Broadcast game update to all players in a game."""
    await sio.emit(
        'game_update',
        data,
        room=f"game_{game_id}",
        skip_sid=exclude_sid
    )


async def broadcast_to_spectators(game_id: str, event: str, data: dict):
    """Broadcast to spectators watching a game."""
    await sio.emit(
        event,
        data,
        room=f"spectate_{game_id}"
    )


async def send_to_user(user_id: str, event: str, data: dict):
    """Send event to all sessions of a specific user."""
    sids = manager.active_connections.get(user_id, set())
    for sid in sids:
        await sio.emit(event, data, room=sid)


async def send_error(sid: str, error_message: str, error_code: Optional[str] = None):
    """Send error message to a specific session."""
    await sio.emit('error', {
        'message': error_message,
        'code': error_code,
        'timestamp': datetime.utcnow().isoformat()
    }, room=sid)