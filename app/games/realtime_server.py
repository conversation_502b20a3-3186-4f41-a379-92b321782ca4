#!/usr/bin/env python3
"""
Enhanced Real-time Gaming Server for BetBet Platform
Day 2: Chess + RPS + Spectator Betting + Multiple Rooms
"""
#!/usr/bin/env python3
import sys
import os

# Add the project root to Python path
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
import socketio
import uvicorn
import json
import uuid
from datetime import datetime
from typing import Dict, Any, List, Optional

# Import our game engines
try:
    from app.games.chess_engine import ChessGame, GameStatus as ChessStatus
    from app.games.betting_engine import BettingEngine, BetType
except ImportError as e:
    print(f"Import error: {e}")
    print("Current working directory:", os.getcwd())
    print("Python path:", sys.path)
    # Fallback imports
    from chess_engine import Chess<PERSON>ame, GameStatus as ChessStatus
    from betting_engine import BettingEngine, BetType

# Create FastAPI app
app = FastAPI(
    title="BetBet Enhanced Gaming Platform",
    version="2.0.0",
    description="Real-time multiplayer gaming with chess, RPS, and spectator betting"
)

# Add CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Create Socket.IO server
sio = socketio.AsyncServer(
    async_mode='asgi',
    cors_allowed_origins="*",
    logger=False,
    engineio_logger=False
)

# Global game state
active_games: Dict[str, Dict[str, Any]] = {}
chess_games: Dict[str, ChessGame] = {}
connected_users: Dict[str, str] = {}  # sid -> user_id
user_sessions: Dict[str, str] = {}    # user_id -> sid
betting_engine = BettingEngine()

# Demo users with enhanced balances
demo_users = {
    "alice_gamer": {"balance": 2000.0, "level": 8, "games_played": 45, "wins": 28},
    "bob_player": {"balance": 1500.0, "level": 6, "games_played": 32, "wins": 18},
    "charlie_pro": {"balance": 3000.0, "level": 12, "games_played": 78, "wins": 52},
    "diana_chess": {"balance": 2500.0, "level": 15, "games_played": 89, "wins": 67},
    "eve_spectator": {"balance": 1000.0, "level": 3, "games_played": 12, "wins": 5},
    "frank_bettor": {"balance": 5000.0, "level": 10, "games_played": 56, "wins": 34}
}

# Initialize betting engine with demo balances
for user_id, user_data in demo_users.items():
    betting_engine.set_user_balance(user_id, user_data["balance"])

# API Routes
@app.get("/")
async def root():
    return {
        "message": "🎮 BetBet Enhanced Gaming Platform",
        "version": "2.0.0",
        "status": "running",
        "features": [
            "real-time-chess",
            "real-time-rps",
            "spectator-betting",
            "multiple-game-rooms",
            "live-odds",
            "tournament-ready"
        ],
        "stats": {
            "active_games": len(active_games),
            "connected_users": len(connected_users),
            "chess_games": len(chess_games),
            "total_betting_pools": len(betting_engine.betting_pools)
        }
    }

@app.get("/health")
async def health():
    return {
        "status": "healthy",
        "websocket": "socket.io",
        "active_connections": len(connected_users),
        "active_games": len(active_games),
        "chess_games": len(chess_games),
        "betting_pools": len(betting_engine.betting_pools)
    }

@app.get("/api/games")
async def get_games():
    """Get all active games."""
    games = []

    # Add RPS games
    for game_id, game_state in active_games.items():
        if game_state.get("type") == "rock_paper_scissors":
            games.append({
                "id": game_id,
                "type": "rock_paper_scissors",
                "status": game_state.get("status"),
                "players": [{"username": p["username"], "ready": p["ready"]}
                           for p in game_state.get("players", {}).values()],
                "spectators": len(game_state.get("spectators", [])),
                "round": game_state.get("state", {}).get("round", 1),
                "scores": game_state.get("state", {}).get("scores", {}),
                "wager": game_state.get("wager", 0),
                "created_at": game_state.get("created_at")
            })

    # Add Chess games
    for game_id, chess_game in chess_games.items():
        game_state = active_games.get(game_id, {})
        games.append({
            "id": game_id,
            "type": "chess",
            "status": chess_game.status.value,
            "players": [
                {"username": chess_game.white_player, "color": "white"},
                {"username": chess_game.black_player, "color": "black"} if chess_game.black_player else None
            ],
            "spectators": len(game_state.get("spectators", [])),
            "move_count": len(chess_game.move_history),
            "current_turn": chess_game.current_turn.value,
            "captured": chess_game.captured_pieces,
            "created_at": game_state.get("created_at")
        })

    return {"games": games, "count": len(games)}

@app.post("/api/games/create")
async def create_game(game_data: dict):
    """Create a new game."""
    game_type = game_data.get("type", "rock_paper_scissors")
    game_id = str(uuid.uuid4())[:8]

    if game_type == "chess":
        # Create chess game
        chess_game = ChessGame(game_id, game_data.get("player1_id", "demo_player"))
        chess_games[game_id] = chess_game

        # Create general game state
        active_games[game_id] = {
            "id": game_id,
            "type": "chess",
            "status": "waiting",
            "spectators": [],
            "wager": game_data.get("wager", 100),
            "created_at": datetime.utcnow().isoformat()
        }
    else:
        # Create RPS game
        active_games[game_id] = {
            "id": game_id,
            "type": "rock_paper_scissors",
            "status": "waiting",
            "players": {},
            "spectators": [],
            "wager": game_data.get("wager", 50),
            "state": {
                "round": 1,
                "max_rounds": game_data.get("rounds", 3),
                "scores": {},
                "moves": {},
                "history": []
            },
            "created_at": datetime.utcnow().isoformat()
        }

    # Create betting pool
    betting_engine.create_betting_pool(game_id)

    return {"success": True, "game_id": game_id, "type": game_type}

@app.get("/api/games/{game_id}/betting")
async def get_betting_options(game_id: str):
    """Get available betting options for a game."""
    if game_id in active_games:
        game_state = active_games[game_id]
        if game_id in chess_games:
            # Add chess-specific state
            chess_game = chess_games[game_id]
            game_state = {**game_state, **chess_game.get_game_state()}

        return betting_engine.get_available_bets(game_id, game_state)

    raise HTTPException(status_code=404, detail="Game not found")

@app.post("/api/bets/place")
async def place_bet(bet_data: dict):
    """Place a spectator bet."""
    try:
        user_id = bet_data.get("user_id")
        game_id = bet_data.get("game_id")
        bet_type = BetType(bet_data.get("bet_type"))
        selection = bet_data.get("selection")
        amount = float(bet_data.get("amount"))

        # Get current game state
        game_state = active_games.get(game_id, {})
        if game_id in chess_games:
            chess_game = chess_games[game_id]
            game_state = {**game_state, **chess_game.get_game_state()}

        result = betting_engine.place_bet(user_id, game_id, bet_type, selection, amount, game_state)

        if result["success"]:
            # Broadcast bet placement to spectators
            await sio.emit('bet_placed', {
                "bet": result["bet"],
                "pool_summary": result["pool_summary"]
            }, room=f"game_{game_id}")

        return result

    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@app.get("/api/users/{user_id}/bets")
async def get_user_bets(user_id: str, game_id: str = None):
    """Get user's betting history."""
    return {
        "bets": betting_engine.get_user_bets(user_id, game_id),
        "balance": betting_engine.get_user_balance(user_id)
    }

# WebSocket Events
@sio.event
async def connect(sid, environ, auth=None):
    """Handle new connection."""
    print(f"🔗 Client connected: {sid}")
    await sio.emit('connected', {
        'message': '🎮 Connected to BetBet Enhanced Gaming!',
        'sid': sid,
        'features': ['chess', 'rps', 'betting', 'tournaments'],
        'active_games': len(active_games),
        'demo_users': list(demo_users.keys())
    }, room=sid)
    return True

@sio.event
async def disconnect(sid):
    """Handle disconnection."""
    print(f"❌ Client disconnected: {sid}")

    if sid in connected_users:
        username = connected_users[sid]
        del connected_users[sid]
        if username in user_sessions:
            del user_sessions[username]

        # Handle game disconnections
        await handle_user_disconnect(username)

@sio.event
async def login(sid, data):
    """Enhanced login with user stats."""
    username = data.get('username')
    if username in demo_users:
        connected_users[sid] = username
        user_sessions[username] = sid

        user_data = demo_users[username]
        balance = betting_engine.get_user_balance(username)

        await sio.emit('login_success', {
            'username': username,
            'user_data': {**user_data, 'balance': balance},
            'message': f'Welcome back, {username}!',
            'stats': {
                'level': user_data['level'],
                'games_played': user_data['games_played'],
                'wins': user_data['wins'],
                'win_rate': round(user_data['wins'] / user_data['games_played'] * 100, 1)
            }
        }, room=sid)

        return {'success': True}
    else:
        await sio.emit('login_error', {
            'message': 'Invalid username. Available: ' + ', '.join(demo_users.keys())
        }, room=sid)
        return {'success': False}

@sio.event
async def join_game(sid, data):
    """Join a game as player or spectator."""
    game_id = data.get('game_id')
    role = data.get('role', 'player')

    if game_id not in active_games:
        await sio.emit('error', {'message': 'Game not found'}, room=sid)
        return {'success': False}

    username = connected_users.get(sid)
    if not username:
        await sio.emit('error', {'message': 'Please login first'}, room=sid)
        return {'success': False}

    game_state = active_games[game_id]
    game_type = game_state.get("type")

    if role == 'spectator':
        return await join_as_spectator(sid, game_id, username, game_state)
    else:
        if game_type == "chess":
            return await join_chess_game(sid, game_id, username)
        else:
            return await join_rps_game(sid, game_id, username, game_state)

async def join_as_spectator(sid, game_id, username, game_state):
    """Join as spectator with betting options."""
    if sid not in game_state["spectators"]:
        game_state["spectators"].append(sid)

    await sio.enter_room(sid, f"game_{game_id}")

    # Get current game state for spectator
    spectator_data = {
        'game_id': game_id,
        'game_type': game_state.get("type"),
        'game_state': game_state.get("state", {}),
        'players': []
    }

    if game_id in chess_games:
        chess_game = chess_games[game_id]
        spectator_data.update({
            'chess_state': chess_game.get_game_state(),
            'players': [chess_game.white_player, chess_game.black_player]
        })
    else:
        spectator_data['players'] = [p["username"] for p in game_state.get("players", {}).values()]

    # Get betting options
    betting_options = betting_engine.get_available_bets(game_id, {**game_state, **(chess_games[game_id].get_game_state() if game_id in chess_games else {})})
    spectator_data['betting_options'] = betting_options
    spectator_data['user_balance'] = betting_engine.get_user_balance(username)

    await sio.emit('joined_as_spectator', spectator_data, room=sid)

    await sio.emit('spectator_joined', {
        'username': username,
        'spectator_count': len(game_state["spectators"])
    }, room=f"game_{game_id}", skip_sid=sid)

    return {'success': True}

async def join_chess_game(sid, game_id, username):
    """Join a chess game as player."""
    chess_game = chess_games[game_id]
    game_state = active_games[game_id]

    if chess_game.black_player is None and username != chess_game.white_player:
        # Join as black player
        chess_game.add_player(username)
        game_state["status"] = "in_progress"

        await sio.enter_room(sid, f"game_{game_id}")
        await sio.emit('joined_chess_game', {
            'game_id': game_id,
            'color': 'black',
            'opponent': chess_game.white_player,
            'game_state': chess_game.get_game_state()
        }, room=sid)

        # Notify game start
        await sio.emit('chess_game_started', {
            'message': '♟️ Chess game started!',
            'white_player': chess_game.white_player,
            'black_player': chess_game.black_player,
            'current_turn': chess_game.current_turn.value
        }, room=f"game_{game_id}")

        return {'success': True}

    elif username == chess_game.white_player:
        # Rejoin as white player
        await sio.enter_room(sid, f"game_{game_id}")
        await sio.emit('joined_chess_game', {
            'game_id': game_id,
            'color': 'white',
            'opponent': chess_game.black_player,
            'game_state': chess_game.get_game_state()
        }, room=sid)
        return {'success': True}

    else:
        await sio.emit('error', {'message': 'Chess game is full or you are already in it'}, room=sid)
        return {'success': False}

async def join_rps_game(sid, game_id, username, game_state):
    """Join RPS game (existing logic from Day 1)."""
    if len(game_state["players"]) >= 2:
        await sio.emit('error', {'message': 'Game is full'}, room=sid)
        return {'success': False}

    if username in [p["username"] for p in game_state["players"].values()]:
        await sio.emit('error', {'message': 'Already in this game'}, room=sid)
        return {'success': False}

    # Add player
    player_num = len(game_state["players"]) + 1
    game_state["players"][username] = {
        'sid': sid,
        'username': username,
        'player_num': player_num,
        'ready': True
    }

    game_state["state"]["scores"][username] = 0

    await sio.enter_room(sid, f"game_{game_id}")
    await sio.emit('joined_game', {
        'game_id': game_id,
        'player_num': player_num,
        'username': username,
        'wager': game_state["wager"]
    }, room=sid)

    await sio.emit('player_joined', {
        'username': username,
        'player_num': player_num,
        'players_count': len(game_state["players"])
    }, room=f"game_{game_id}", skip_sid=sid)

    # Start game if 2 players
    if len(game_state["players"]) == 2:
        game_state["status"] = "in_progress"
        await sio.emit('game_started', {
            'message': '🎮 RPS Game started! Round 1 - Make your moves!',
            'round': 1,
            'max_rounds': game_state["state"]["max_rounds"]
        }, room=f"game_{game_id}")

    return {'success': True}

@sio.event
async def make_chess_move(sid, data):
    """Handle chess move."""
    game_id = data.get('game_id')
    from_pos = tuple(data.get('from'))  # [row, col]
    to_pos = tuple(data.get('to'))      # [row, col]

    if game_id not in chess_games:
        await sio.emit('error', {'message': 'Chess game not found'}, room=sid)
        return {'success': False}

    username = connected_users.get(sid)
    chess_game = chess_games[game_id]

    # Make the move
    result = chess_game.make_move(from_pos, to_pos, username)

    if result["success"]:
        # Broadcast move to all players and spectators
        await sio.emit('chess_move_made', {
            'game_id': game_id,
            'move': result["move"],
            'board': result["board"],
            'current_turn': result["current_turn"],
            'status': result["status"],
            'captured': result["captured"]
        }, room=f"game_{game_id}")

        # Handle betting settlements for move-based bets
        if result["move"]["captured"]:
            # Settle "first capture" bets if this was the first capture
            total_captured = sum(len(pieces) for pieces in result["captured"].values())
            if total_captured == 1:
                payouts = betting_engine.settle_bets(game_id, BetType.FIRST_CAPTURE, username)
                if payouts:
                    await sio.emit('bets_settled', {
                        'bet_type': 'first_capture',
                        'winner': username,
                        'payouts': payouts
                    }, room=f"game_{game_id}")

        # Check for game end
        if result["status"] in ["checkmate", "stalemate", "draw"]:
            winner = None
            if result["status"] == "checkmate":
                # Determine winner (opposite of current turn since turn switched)
                winner = chess_game.white_player if chess_game.current_turn.value == "black" else chess_game.black_player

            # Settle game winner bets
            if winner:
                payouts = betting_engine.settle_game_bets(game_id, winner)
                if payouts:
                    await sio.emit('bets_settled', {
                        'bet_type': 'game_winner',
                        'winner': winner,
                        'payouts': payouts
                    }, room=f"game_{game_id}")

            await sio.emit('chess_game_ended', {
                'game_id': game_id,
                'status': result["status"],
                'winner': winner,
                'final_state': chess_game.get_game_state()
            }, room=f"game_{game_id}")

    else:
        await sio.emit('chess_move_error', {
            'error': result["error"]
        }, room=sid)

    return result

@sio.event
async def make_rps_move(sid, data):
    """Handle RPS move (enhanced from Day 1)."""
    game_id = data.get('game_id')
    move = data.get('move')  # 'rock', 'paper', 'scissors'

    if game_id not in active_games:
        await sio.emit('error', {'message': 'Game not found'}, room=sid)
        return {'success': False}

    username = connected_users.get(sid)
    game_state = active_games[game_id]

    if username not in game_state["players"]:
        await sio.emit('error', {'message': 'Not a player in this game'}, room=sid)
        return {'success': False}

    # Record move
    current_round = game_state["state"]["round"]
    if str(current_round) not in game_state["state"]["moves"]:
        game_state["state"]["moves"][str(current_round)] = {}

    game_state["state"]["moves"][str(current_round)][username] = move

    await sio.emit('move_recorded', {
        'message': f'Move recorded: {move}',
        'waiting_for': 'opponent'
    }, room=sid)

    # Check if both players moved
    round_moves = game_state["state"]["moves"][str(current_round)]
    players = list(game_state["players"].keys())

    if len(round_moves) == 2:
        # Determine winner
        result = determine_rps_winner(round_moves, players)

        # Update scores
        if result["winner"]:
            game_state["state"]["scores"][result["winner"]] += 1

        # Settle round winner bets
        round_winner = result["winner"] if result["winner"] else "tie"
        payouts = betting_engine.settle_round_bets(game_id, round_winner)

        # Add to history
        game_state["state"]["history"].append({
            "round": current_round,
            "moves": round_moves,
            "result": result
        })

        # Broadcast round result
        await sio.emit('round_complete', {
            'round': current_round,
            'moves': round_moves,
            'result': result,
            'scores': game_state["state"]["scores"],
            'message': f'Round {current_round}: {result["message"]}',
            'bet_payouts': payouts
        }, room=f"game_{game_id}")

        # Check game end
        max_rounds = game_state["state"]["max_rounds"]
        if current_round >= max_rounds:
            # Game over
            final_scores = game_state["state"]["scores"]
            winner = max(final_scores, key=final_scores.get) if max(final_scores.values()) > min(final_scores.values()) else None

            # Settle game winner bets
            game_payouts = betting_engine.settle_game_bets(game_id, winner if winner else "tie")

            await sio.emit('game_complete', {
                'winner': winner,
                'final_scores': final_scores,
                'message': f'🏆 Game Over! Winner: {winner}' if winner else '🤝 Game Over! It\'s a tie!',
                'history': game_state["state"]["history"],
                'final_payouts': game_payouts
            }, room=f"game_{game_id}")

            game_state["status"] = "completed"
        else:
            # Next round
            game_state["state"]["round"] += 1
            await sio.emit('next_round', {
                'round': game_state["state"]["round"],
                'message': f'Round {game_state["state"]["round"]} - Make your moves!'
            }, room=f"game_{game_id}")

    return {'success': True}

@sio.event
async def place_spectator_bet(sid, data):
    """Place a bet as spectator."""
    try:
        username = connected_users.get(sid)
        if not username:
            await sio.emit('error', {'message': 'Please login first'}, room=sid)
            return {'success': False}

        game_id = data.get('game_id')
        bet_type = BetType(data.get('bet_type'))
        selection = data.get('selection')
        amount = float(data.get('amount'))

        # Get current game state
        game_state = active_games.get(game_id, {})
        if game_id in chess_games:
            chess_game = chess_games[game_id]
            game_state = {**game_state, **chess_game.get_game_state()}

        result = betting_engine.place_bet(username, game_id, bet_type, selection, amount, game_state)

        if result["success"]:
            # Broadcast bet placement
            await sio.emit('bet_placed', {
                "user": username,
                "bet": result["bet"],
                "pool_summary": result["pool_summary"]
            }, room=f"game_{game_id}")

            # Send confirmation to bettor
            await sio.emit('bet_confirmed', {
                "bet": result["bet"],
                "new_balance": result["new_balance"]
            }, room=sid)
        else:
            await sio.emit('bet_error', {
                "error": result["error"]
            }, room=sid)

        return result

    except Exception as e:
        await sio.emit('bet_error', {'error': str(e)}, room=sid)
        return {'success': False}

def determine_rps_winner(moves: dict, players: list):
    """Determine RPS winner (from Day 1)."""
    p1, p2 = players[0], players[1]
    move1, move2 = moves[p1], moves[p2]

    if move1 == move2:
        return {
            "winner": None,
            "result": "tie",
            "message": f"{move1} vs {move2} - It's a tie!"
        }

    wins = {"rock": "scissors", "scissors": "paper", "paper": "rock"}

    if wins[move1] == move2:
        return {
            "winner": p1,
            "result": "win",
            "message": f"{p1} wins! {move1} beats {move2}"
        }
    else:
        return {
            "winner": p2,
            "result": "win",
            "message": f"{p2} wins! {move2} beats {move1}"
        }

async def handle_user_disconnect(username: str):
    """Handle user disconnection from all games."""
    # Handle RPS games
    for game_id, game_state in active_games.items():
        if game_state.get("type") == "rock_paper_scissors":
            if username in game_state.get("players", {}):
                del game_state["players"][username]
                await sio.emit('player_disconnected', {
                    'username': username,
                    'message': f'{username} disconnected'
                }, room=f"game_{game_id}")

    # Handle Chess games
    for game_id, chess_game in chess_games.items():
        if username in [chess_game.white_player, chess_game.black_player]:
            await sio.emit('player_disconnected', {
                'username': username,
                'message': f'{username} disconnected from chess game'
            }, room=f"game_{game_id}")

# Create Socket.IO app
socket_app = socketio.ASGIApp(sio, app)

if __name__ == "__main__":
    print("🚀 Starting BetBet Enhanced Gaming Platform v2.0")
    print("🎮 Games: Chess ♟️ + Rock Paper Scissors ✂️")
    print("💰 Features: Spectator Betting + Live Odds")
    print("🏆 Ready for: Tournaments + Multiple Rooms")
    print("👥 Demo users:", ', '.join(demo_users.keys()))
    print("🌐 Server: http://localhost:8003")

    uvicorn.run(
        "app.games.realtime_server:socket_app",
        host="0.0.0.0",
        port=8003,
        reload=False
    )
