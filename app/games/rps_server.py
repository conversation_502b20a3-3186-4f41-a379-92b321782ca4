#!/usr/bin/env python3
"""
Real-time Rock Paper Scissors Demo
Day 1 MVP: Working real-time RPS with spectators
"""
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import socketio
import uvicorn
import json
import uuid
from datetime import datetime
from typing import Dict, Any

# Create FastAPI app
app = FastAPI(
    title="BetBet Real-time Rock Paper Scissors",
    version="1.0.0",
    description="Real-time multiplayer Rock Paper Scissors with spectator betting"
)

# Add CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Create Socket.IO server
sio = socketio.AsyncServer(
    async_mode='asgi',
    cors_allowed_origins="*",
    logger=False,
    engineio_logger=False
)

# In-memory game state
active_games: Dict[str, Dict[str, Any]] = {}
connected_users: Dict[str, str] = {}  # sid -> username
user_sessions: Dict[str, str] = {}    # username -> sid

# Demo users with balances
demo_users = {
    "alice_gamer": {"balance": 1000.0, "level": 5},
    "bob_player": {"balance": 750.0, "level": 3},
    "charlie_pro": {"balance": 1500.0, "level": 8},
    "diana_chess": {"balance": 2000.0, "level": 12},
    "eve_spectator": {"balance": 500.0, "level": 2}
}

# Basic routes
@app.get("/")
async def root():
    return {
        "message": "🎮 BetBet Real-time Rock Paper Scissors",
        "status": "running",
        "features": ["real-time-multiplayer", "spectator-mode", "live-betting"],
        "active_games": len(active_games),
        "connected_users": len(connected_users),
        "demo_users": list(demo_users.keys())
    }

@app.get("/health")
async def health():
    return {
        "status": "healthy",
        "websocket": "socket.io",
        "active_connections": len(connected_users),
        "active_games": len(active_games)
    }

@app.get("/api/games")
async def get_games():
    """Get list of active games."""
    games = []
    for game_id, game_state in active_games.items():
        games.append({
            "id": game_id,
            "status": game_state.get("status"),
            "players": [{"username": p["username"], "ready": p["ready"]}
                       for p in game_state.get("players", {}).values()],
            "spectators": len(game_state.get("spectators", [])),
            "round": game_state.get("state", {}).get("round", 1),
            "scores": game_state.get("state", {}).get("scores", {}),
            "wager": game_state.get("wager", 0),
            "created_at": game_state.get("created_at")
        })
    return {"games": games, "count": len(games)}

@app.post("/api/games/create")
async def create_game(game_data: dict):
    """Create a new game."""
    game_id = str(uuid.uuid4())[:8]  # Short ID for demo

    active_games[game_id] = {
        "id": game_id,
        "status": "waiting",
        "players": {},
        "spectators": [],
        "wager": game_data.get("wager", 50),
        "state": {
            "round": 1,
            "max_rounds": game_data.get("rounds", 3),
            "scores": {},
            "moves": {},
            "history": []
        },
        "created_at": datetime.utcnow().isoformat()
    }

    return {"success": True, "game_id": game_id}

# WebSocket Events
@sio.event
async def connect(sid, environ, auth=None):
    """Handle new connection."""
    print(f"🔗 Client connected: {sid}")
    await sio.emit('connected', {
        'message': '🎮 Connected to BetBet Real-time Gaming!',
        'sid': sid,
        'active_games': len(active_games),
        'demo_users': list(demo_users.keys())
    }, room=sid)
    return True

@sio.event
async def disconnect(sid):
    """Handle disconnection."""
    print(f"❌ Client disconnected: {sid}")

    # Clean up
    if sid in connected_users:
        username = connected_users[sid]
        del connected_users[sid]
        if username in user_sessions:
            del user_sessions[username]

        # Remove from games
        for game_id, game_state in active_games.items():
            if username in game_state.get("players", {}):
                await handle_player_disconnect(game_id, username)
            elif sid in game_state.get("spectators", []):
                game_state["spectators"].remove(sid)
                await sio.emit('spectator_left', {
                    'spectator_count': len(game_state["spectators"])
                }, room=f"game_{game_id}")

@sio.event
async def login(sid, data):
    """Demo login."""
    username = data.get('username')
    if username in demo_users:
        connected_users[sid] = username
        user_sessions[username] = sid

        await sio.emit('login_success', {
            'username': username,
            'user_data': demo_users[username],
            'message': f'Welcome back, {username}!'
        }, room=sid)

        return {'success': True}
    else:
        await sio.emit('login_error', {
            'message': 'Invalid username. Try: alice_gamer, bob_player, charlie_pro, diana_chess, eve_spectator'
        }, room=sid)
        return {'success': False}

@sio.event
async def join_game(sid, data):
    """Join a game."""
    game_id = data.get('game_id')
    role = data.get('role', 'player')

    if game_id not in active_games:
        await sio.emit('error', {'message': 'Game not found'}, room=sid)
        return {'success': False}

    username = connected_users.get(sid)
    if not username:
        await sio.emit('error', {'message': 'Please login first'}, room=sid)
        return {'success': False}

    game_state = active_games[game_id]

    if role == 'spectator':
        # Join as spectator
        if sid not in game_state["spectators"]:
            game_state["spectators"].append(sid)

        await sio.enter_room(sid, f"game_{game_id}")
        await sio.emit('joined_as_spectator', {
            'game_id': game_id,
            'game_state': game_state["state"],
            'players': [p["username"] for p in game_state["players"].values()]
        }, room=sid)

        await sio.emit('spectator_joined', {
            'username': username,
            'spectator_count': len(game_state["spectators"])
        }, room=f"game_{game_id}", skip_sid=sid)

    else:
        # Join as player
        if len(game_state["players"]) >= 2:
            await sio.emit('error', {'message': 'Game is full'}, room=sid)
            return {'success': False}

        if username in [p["username"] for p in game_state["players"].values()]:
            await sio.emit('error', {'message': 'Already in this game'}, room=sid)
            return {'success': False}

        # Add player
        player_num = len(game_state["players"]) + 1
        game_state["players"][username] = {
            'sid': sid,
            'username': username,
            'player_num': player_num,
            'ready': True
        }

        # Initialize score
        game_state["state"]["scores"][username] = 0

        await sio.enter_room(sid, f"game_{game_id}")
        await sio.emit('joined_game', {
            'game_id': game_id,
            'player_num': player_num,
            'username': username,
            'wager': game_state["wager"]
        }, room=sid)

        await sio.emit('player_joined', {
            'username': username,
            'player_num': player_num,
            'players_count': len(game_state["players"])
        }, room=f"game_{game_id}", skip_sid=sid)

        # Start game if 2 players
        if len(game_state["players"]) == 2:
            game_state["status"] = "in_progress"
            await sio.emit('game_started', {
                'message': '🎮 Game started! Round 1 - Make your moves!',
                'round': 1,
                'max_rounds': game_state["state"]["max_rounds"]
            }, room=f"game_{game_id}")

    return {'success': True}

@sio.event
async def make_move(sid, data):
    """Make a Rock Paper Scissors move."""
    game_id = data.get('game_id')
    move = data.get('move')  # 'rock', 'paper', 'scissors'

    if game_id not in active_games:
        await sio.emit('error', {'message': 'Game not found'}, room=sid)
        return {'success': False}

    username = connected_users.get(sid)
    game_state = active_games[game_id]

    if username not in game_state["players"]:
        await sio.emit('error', {'message': 'Not a player in this game'}, room=sid)
        return {'success': False}

    # Record move
    current_round = game_state["state"]["round"]
    if str(current_round) not in game_state["state"]["moves"]:
        game_state["state"]["moves"][str(current_round)] = {}

    game_state["state"]["moves"][str(current_round)][username] = move

    # Notify move recorded
    await sio.emit('move_recorded', {
        'message': f'Move recorded: {move}',
        'waiting_for': 'opponent'
    }, room=sid)

    # Check if both players moved
    round_moves = game_state["state"]["moves"][str(current_round)]
    players = list(game_state["players"].keys())

    if len(round_moves) == 2:
        # Determine winner
        result = determine_winner(round_moves, players)

        # Update scores
        if result["winner"]:
            game_state["state"]["scores"][result["winner"]] += 1

        # Add to history
        game_state["state"]["history"].append({
            "round": current_round,
            "moves": round_moves,
            "result": result
        })

        # Broadcast round result
        await sio.emit('round_complete', {
            'round': current_round,
            'moves': round_moves,
            'result': result,
            'scores': game_state["state"]["scores"],
            'message': f'Round {current_round}: {result["message"]}'
        }, room=f"game_{game_id}")

        # Check game end
        max_rounds = game_state["state"]["max_rounds"]
        if current_round >= max_rounds:
            # Game over
            final_scores = game_state["state"]["scores"]
            winner = max(final_scores, key=final_scores.get) if max(final_scores.values()) > min(final_scores.values()) else None

            await sio.emit('game_complete', {
                'winner': winner,
                'final_scores': final_scores,
                'message': f'🏆 Game Over! Winner: {winner}' if winner else '🤝 Game Over! It\'s a tie!',
                'history': game_state["state"]["history"]
            }, room=f"game_{game_id}")

            game_state["status"] = "completed"
        else:
            # Next round
            game_state["state"]["round"] += 1
            await sio.emit('next_round', {
                'round': game_state["state"]["round"],
                'message': f'Round {game_state["state"]["round"]} - Make your moves!'
            }, room=f"game_{game_id}")

    return {'success': True}

def determine_winner(moves: dict, players: list):
    """Determine RPS winner."""
    p1, p2 = players[0], players[1]
    move1, move2 = moves[p1], moves[p2]

    if move1 == move2:
        return {
            "winner": None,
            "result": "tie",
            "message": f"{move1} vs {move2} - It's a tie!"
        }

    wins = {"rock": "scissors", "scissors": "paper", "paper": "rock"}

    if wins[move1] == move2:
        return {
            "winner": p1,
            "result": "win",
            "message": f"{p1} wins! {move1} beats {move2}"
        }
    else:
        return {
            "winner": p2,
            "result": "win",
            "message": f"{p2} wins! {move2} beats {move1}"
        }

async def handle_player_disconnect(game_id: str, username: str):
    """Handle player leaving."""
    if game_id in active_games:
        game_state = active_games[game_id]
        if username in game_state["players"]:
            del game_state["players"][username]

            await sio.emit('player_disconnected', {
                'username': username,
                'message': f'{username} disconnected'
            }, room=f"game_{game_id}")

            # End game if in progress
            if game_state["status"] == "in_progress":
                game_state["status"] = "abandoned"
                await sio.emit('game_abandoned', {
                    'message': 'Game abandoned due to player disconnect'
                }, room=f"game_{game_id}")

# Create Socket.IO app
socket_app = socketio.ASGIApp(sio, app)

if __name__ == "__main__":
    print("🚀 Starting Real-time Rock Paper Scissors Demo")
    print("🎮 Features: Real-time multiplayer, Spectator mode")
    print("👥 Demo users: alice_gamer, bob_player, charlie_pro, diana_chess, eve_spectator")
    print("🌐 Server: http://localhost:8003")

    uvicorn.run(
        "rps_realtime_demo:socket_app",
        host="0.0.0.0",
        port=8003,
        reload=False
    )
