#!/usr/bin/env python3
"""
Chess Game Engine for BetBet Platform
Real-time chess with move validation and game state management
"""
from typing import Dict, List, Optional, Tuple, Any
from enum import Enum
import json
import copy

class PieceType(Enum):
    PAWN = "pawn"
    ROOK = "rook"
    KNIGHT = "knight"
    BISHOP = "bishop"
    QUEEN = "queen"
    KING = "king"

class Color(Enum):
    WHITE = "white"
    BLACK = "black"

class GameStatus(Enum):
    WAITING = "waiting"
    IN_PROGRESS = "in_progress"
    CHECK = "check"
    CHECKMATE = "checkmate"
    STALEMATE = "stalemate"
    DRAW = "draw"
    ABANDONED = "abandoned"

class ChessPiece:
    def __init__(self, piece_type: PieceType, color: Color):
        self.type = piece_type
        self.color = color
        self.has_moved = False
    
    def to_dict(self):
        return {
            "type": self.type.value,
            "color": self.color.value,
            "has_moved": self.has_moved
        }
    
    @classmethod
    def from_dict(cls, data: dict):
        piece = cls(PieceType(data["type"]), Color(data["color"]))
        piece.has_moved = data.get("has_moved", False)
        return piece

class ChessBoard:
    def __init__(self):
        self.board = [[None for _ in range(8)] for _ in range(8)]
        self.setup_initial_position()
    
    def setup_initial_position(self):
        """Set up the standard chess starting position."""
        # White pieces (bottom)
        self.board[7] = [
            ChessPiece(PieceType.ROOK, Color.WHITE),
            ChessPiece(PieceType.KNIGHT, Color.WHITE),
            ChessPiece(PieceType.BISHOP, Color.WHITE),
            ChessPiece(PieceType.QUEEN, Color.WHITE),
            ChessPiece(PieceType.KING, Color.WHITE),
            ChessPiece(PieceType.BISHOP, Color.WHITE),
            ChessPiece(PieceType.KNIGHT, Color.WHITE),
            ChessPiece(PieceType.ROOK, Color.WHITE)
        ]
        self.board[6] = [ChessPiece(PieceType.PAWN, Color.WHITE) for _ in range(8)]
        
        # Black pieces (top)
        self.board[0] = [
            ChessPiece(PieceType.ROOK, Color.BLACK),
            ChessPiece(PieceType.KNIGHT, Color.BLACK),
            ChessPiece(PieceType.BISHOP, Color.BLACK),
            ChessPiece(PieceType.QUEEN, Color.BLACK),
            ChessPiece(PieceType.KING, Color.BLACK),
            ChessPiece(PieceType.BISHOP, Color.BLACK),
            ChessPiece(PieceType.KNIGHT, Color.BLACK),
            ChessPiece(PieceType.ROOK, Color.BLACK)
        ]
        self.board[1] = [ChessPiece(PieceType.PAWN, Color.BLACK) for _ in range(8)]
    
    def get_piece(self, row: int, col: int) -> Optional[ChessPiece]:
        """Get piece at position."""
        if 0 <= row < 8 and 0 <= col < 8:
            return self.board[row][col]
        return None
    
    def set_piece(self, row: int, col: int, piece: Optional[ChessPiece]):
        """Set piece at position."""
        if 0 <= row < 8 and 0 <= col < 8:
            self.board[row][col] = piece
    
    def move_piece(self, from_row: int, from_col: int, to_row: int, to_col: int) -> bool:
        """Move piece from one position to another."""
        piece = self.get_piece(from_row, from_col)
        if piece:
            self.set_piece(to_row, to_col, piece)
            self.set_piece(from_row, from_col, None)
            piece.has_moved = True
            return True
        return False
    
    def to_dict(self):
        """Convert board to dictionary for JSON serialization."""
        board_dict = []
        for row in self.board:
            row_dict = []
            for piece in row:
                if piece:
                    row_dict.append(piece.to_dict())
                else:
                    row_dict.append(None)
            board_dict.append(row_dict)
        return board_dict
    
    @classmethod
    def from_dict(cls, data: List[List]):
        """Create board from dictionary."""
        board = cls()
        board.board = [[None for _ in range(8)] for _ in range(8)]
        
        for row_idx, row in enumerate(data):
            for col_idx, piece_data in enumerate(row):
                if piece_data:
                    board.board[row_idx][col_idx] = ChessPiece.from_dict(piece_data)
        
        return board

class ChessGame:
    def __init__(self, game_id: str, white_player: str, black_player: str = None):
        self.game_id = game_id
        self.white_player = white_player
        self.black_player = black_player
        self.board = ChessBoard()
        self.current_turn = Color.WHITE
        self.status = GameStatus.WAITING if not black_player else GameStatus.IN_PROGRESS
        self.move_history = []
        self.captured_pieces = {"white": [], "black": []}
        self.check_status = None
        self.created_at = None
        self.started_at = None
        self.last_move_at = None
    
    def add_player(self, player_id: str) -> bool:
        """Add second player to start the game."""
        if not self.black_player and player_id != self.white_player:
            self.black_player = player_id
            self.status = GameStatus.IN_PROGRESS
            return True
        return False
    
    def is_valid_move(self, from_pos: Tuple[int, int], to_pos: Tuple[int, int], player_id: str) -> bool:
        """Validate if a move is legal."""
        from_row, from_col = from_pos
        to_row, to_col = to_pos
        
        # Check if it's player's turn
        if (self.current_turn == Color.WHITE and player_id != self.white_player) or \
           (self.current_turn == Color.BLACK and player_id != self.black_player):
            return False
        
        # Check if source position has a piece
        piece = self.board.get_piece(from_row, from_col)
        if not piece or piece.color != self.current_turn:
            return False
        
        # Check if destination is within bounds
        if not (0 <= to_row < 8 and 0 <= to_col < 8):
            return False
        
        # Check if destination has own piece
        dest_piece = self.board.get_piece(to_row, to_col)
        if dest_piece and dest_piece.color == piece.color:
            return False
        
        # Basic piece movement validation (simplified)
        return self._is_valid_piece_move(piece, from_pos, to_pos)
    
    def _is_valid_piece_move(self, piece: ChessPiece, from_pos: Tuple[int, int], to_pos: Tuple[int, int]) -> bool:
        """Validate piece-specific movement rules (simplified)."""
        from_row, from_col = from_pos
        to_row, to_col = to_pos
        row_diff = abs(to_row - from_row)
        col_diff = abs(to_col - from_col)
        
        if piece.type == PieceType.PAWN:
            direction = -1 if piece.color == Color.WHITE else 1
            start_row = 6 if piece.color == Color.WHITE else 1
            
            # Forward move
            if from_col == to_col:
                if to_row == from_row + direction:
                    return self.board.get_piece(to_row, to_col) is None
                elif from_row == start_row and to_row == from_row + 2 * direction:
                    return (self.board.get_piece(to_row, to_col) is None and 
                           self.board.get_piece(from_row + direction, from_col) is None)
            # Capture
            elif col_diff == 1 and to_row == from_row + direction:
                return self.board.get_piece(to_row, to_col) is not None
            
            return False
        
        elif piece.type == PieceType.ROOK:
            return (row_diff == 0 or col_diff == 0) and self._is_path_clear(from_pos, to_pos)
        
        elif piece.type == PieceType.BISHOP:
            return row_diff == col_diff and self._is_path_clear(from_pos, to_pos)
        
        elif piece.type == PieceType.QUEEN:
            return ((row_diff == 0 or col_diff == 0) or (row_diff == col_diff)) and self._is_path_clear(from_pos, to_pos)
        
        elif piece.type == PieceType.KING:
            return row_diff <= 1 and col_diff <= 1
        
        elif piece.type == PieceType.KNIGHT:
            return (row_diff == 2 and col_diff == 1) or (row_diff == 1 and col_diff == 2)
        
        return False
    
    def _is_path_clear(self, from_pos: Tuple[int, int], to_pos: Tuple[int, int]) -> bool:
        """Check if path between positions is clear."""
        from_row, from_col = from_pos
        to_row, to_col = to_pos
        
        row_step = 0 if from_row == to_row else (1 if to_row > from_row else -1)
        col_step = 0 if from_col == to_col else (1 if to_col > from_col else -1)
        
        current_row, current_col = from_row + row_step, from_col + col_step
        
        while (current_row, current_col) != (to_row, to_col):
            if self.board.get_piece(current_row, current_col) is not None:
                return False
            current_row += row_step
            current_col += col_step
        
        return True
    
    def make_move(self, from_pos: Tuple[int, int], to_pos: Tuple[int, int], player_id: str) -> Dict[str, Any]:
        """Make a move and return the result."""
        if not self.is_valid_move(from_pos, to_pos, player_id):
            return {"success": False, "error": "Invalid move"}
        
        from_row, from_col = from_pos
        to_row, to_col = to_pos
        
        # Capture piece if present
        captured_piece = self.board.get_piece(to_row, to_col)
        if captured_piece:
            self.captured_pieces[captured_piece.color.value].append(captured_piece.to_dict())
        
        # Make the move
        piece = self.board.get_piece(from_row, from_col)
        self.board.move_piece(from_row, from_col, to_row, to_col)
        
        # Record move
        move_record = {
            "from": from_pos,
            "to": to_pos,
            "piece": piece.to_dict(),
            "captured": captured_piece.to_dict() if captured_piece else None,
            "player": player_id,
            "turn": self.current_turn.value
        }
        self.move_history.append(move_record)
        
        # Switch turns
        self.current_turn = Color.BLACK if self.current_turn == Color.WHITE else Color.WHITE
        
        # Check for game end conditions (simplified)
        game_status = self._check_game_status()
        
        return {
            "success": True,
            "move": move_record,
            "board": self.board.to_dict(),
            "current_turn": self.current_turn.value,
            "status": game_status.value,
            "captured": self.captured_pieces
        }
    
    def _check_game_status(self) -> GameStatus:
        """Check current game status (simplified)."""
        # This is a simplified implementation
        # In a full chess engine, you'd check for check, checkmate, stalemate, etc.
        return GameStatus.IN_PROGRESS
    
    def get_game_state(self) -> Dict[str, Any]:
        """Get current game state."""
        return {
            "game_id": self.game_id,
            "white_player": self.white_player,
            "black_player": self.black_player,
            "board": self.board.to_dict(),
            "current_turn": self.current_turn.value,
            "status": self.status.value,
            "move_history": self.move_history,
            "captured_pieces": self.captured_pieces,
            "move_count": len(self.move_history)
        }
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert game to dictionary for serialization."""
        return self.get_game_state()
