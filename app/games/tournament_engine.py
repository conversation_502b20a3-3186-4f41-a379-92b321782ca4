#!/usr/bin/env python3
"""
Tournament Engine for BetBet Platform
Single elimination tournaments with bracket management
"""
from typing import Dict, List, Optional, Any
from enum import Enum
from datetime import datetime, timedelta
import uuid
import json
import math

class TournamentStatus(Enum):
    REGISTRATION = "registration"
    STARTING = "starting"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    CANCELLED = "cancelled"

class TournamentType(Enum):
    SINGLE_ELIMINATION = "single_elimination"
    DOUBLE_ELIMINATION = "double_elimination"
    ROUND_ROBIN = "round_robin"

class MatchStatus(Enum):
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    WALKOVER = "walkover"

class TournamentMatch:
    def __init__(self, match_id: str, round_num: int, match_num: int, 
                 player1: str = None, player2: str = None):
        self.match_id = match_id
        self.round_num = round_num
        self.match_num = match_num
        self.player1 = player1
        self.player2 = player2
        self.winner = None
        self.status = MatchStatus.PENDING
        self.game_id = None
        self.score = {}
        self.started_at = None
        self.completed_at = None
    
    def can_start(self) -> bool:
        """Check if match can start."""
        return self.player1 and self.player2 and self.status == MatchStatus.PENDING
    
    def start_match(self, game_id: str):
        """Start the match."""
        if self.can_start():
            self.status = MatchStatus.IN_PROGRESS
            self.game_id = game_id
            self.started_at = datetime.utcnow()
            return True
        return False
    
    def complete_match(self, winner: str, score: Dict = None):
        """Complete the match."""
        if self.status == MatchStatus.IN_PROGRESS:
            self.winner = winner
            self.status = MatchStatus.COMPLETED
            self.score = score or {}
            self.completed_at = datetime.utcnow()
            return True
        return False
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert match to dictionary."""
        return {
            "match_id": self.match_id,
            "round": self.round_num,
            "match_num": self.match_num,
            "player1": self.player1,
            "player2": self.player2,
            "winner": self.winner,
            "status": self.status.value,
            "game_id": self.game_id,
            "score": self.score,
            "started_at": self.started_at.isoformat() if self.started_at else None,
            "completed_at": self.completed_at.isoformat() if self.completed_at else None
        }

class Tournament:
    def __init__(self, tournament_id: str, name: str, game_type: str, 
                 max_players: int = 8, entry_fee: float = 0.0):
        self.tournament_id = tournament_id
        self.name = name
        self.game_type = game_type  # "chess" or "rock_paper_scissors"
        self.tournament_type = TournamentType.SINGLE_ELIMINATION
        self.max_players = max_players
        self.entry_fee = entry_fee
        self.prize_pool = 0.0
        self.status = TournamentStatus.REGISTRATION
        
        self.players: List[str] = []
        self.matches: Dict[str, TournamentMatch] = {}
        self.bracket: Dict[int, List[str]] = {}  # round -> match_ids
        self.current_round = 0
        
        self.created_at = datetime.utcnow()
        self.started_at = None
        self.completed_at = None
        self.winner = None
        
        # Prize distribution (percentages)
        self.prize_distribution = {
            1: 0.6,  # Winner gets 60%
            2: 0.3,  # Runner-up gets 30%
            3: 0.1   # Semi-finalists get 5% each
        }
    
    def can_register(self, player_id: str) -> bool:
        """Check if player can register."""
        return (self.status == TournamentStatus.REGISTRATION and 
                len(self.players) < self.max_players and 
                player_id not in self.players)
    
    def register_player(self, player_id: str) -> bool:
        """Register a player for the tournament."""
        if self.can_register(player_id):
            self.players.append(player_id)
            self.prize_pool += self.entry_fee
            return True
        return False
    
    def can_start(self) -> bool:
        """Check if tournament can start."""
        return (self.status == TournamentStatus.REGISTRATION and 
                len(self.players) >= 2 and 
                self._is_power_of_two(len(self.players)))
    
    def _is_power_of_two(self, n: int) -> bool:
        """Check if number is power of 2."""
        return n > 0 and (n & (n - 1)) == 0
    
    def start_tournament(self) -> bool:
        """Start the tournament and create bracket."""
        if not self.can_start():
            return False
        
        self.status = TournamentStatus.STARTING
        self.started_at = datetime.utcnow()
        
        # Create bracket
        self._create_bracket()
        
        # Start first round
        self._start_round(1)
        
        self.status = TournamentStatus.IN_PROGRESS
        return True
    
    def _create_bracket(self):
        """Create tournament bracket."""
        num_players = len(self.players)
        num_rounds = int(math.log2(num_players))
        
        # Shuffle players for random seeding
        import random
        shuffled_players = self.players.copy()
        random.shuffle(shuffled_players)
        
        # Create first round matches
        round_1_matches = []
        for i in range(0, num_players, 2):
            match_id = f"{self.tournament_id}_r1_m{i//2 + 1}"
            match = TournamentMatch(
                match_id=match_id,
                round_num=1,
                match_num=i//2 + 1,
                player1=shuffled_players[i],
                player2=shuffled_players[i + 1] if i + 1 < num_players else None
            )
            self.matches[match_id] = match
            round_1_matches.append(match_id)
        
        self.bracket[1] = round_1_matches
        
        # Create subsequent rounds (empty for now)
        for round_num in range(2, num_rounds + 1):
            round_matches = []
            matches_in_round = num_players // (2 ** round_num)
            
            for match_num in range(1, matches_in_round + 1):
                match_id = f"{self.tournament_id}_r{round_num}_m{match_num}"
                match = TournamentMatch(
                    match_id=match_id,
                    round_num=round_num,
                    match_num=match_num
                )
                self.matches[match_id] = match
                round_matches.append(match_id)
            
            self.bracket[round_num] = round_matches
    
    def _start_round(self, round_num: int):
        """Start all matches in a round."""
        self.current_round = round_num
        
        if round_num in self.bracket:
            for match_id in self.bracket[round_num]:
                match = self.matches[match_id]
                if match.can_start():
                    # Match will be started when game is created
                    pass
    
    def complete_match(self, match_id: str, winner: str, score: Dict = None) -> bool:
        """Complete a match and advance winner."""
        if match_id not in self.matches:
            return False
        
        match = self.matches[match_id]
        if not match.complete_match(winner, score):
            return False
        
        # Advance winner to next round
        self._advance_winner(match)
        
        # Check if round is complete
        if self._is_round_complete(match.round_num):
            self._check_tournament_completion()
        
        return True
    
    def _advance_winner(self, completed_match: TournamentMatch):
        """Advance winner to next round."""
        next_round = completed_match.round_num + 1
        
        if next_round not in self.bracket:
            # Tournament is complete
            self.winner = completed_match.winner
            return
        
        # Find the next match for this winner
        next_match_num = (completed_match.match_num + 1) // 2
        next_match_id = f"{self.tournament_id}_r{next_round}_m{next_match_num}"
        
        if next_match_id in self.matches:
            next_match = self.matches[next_match_id]
            
            # Determine if winner goes to player1 or player2 slot
            if completed_match.match_num % 2 == 1:  # Odd match number
                next_match.player1 = completed_match.winner
            else:  # Even match number
                next_match.player2 = completed_match.winner
    
    def _is_round_complete(self, round_num: int) -> bool:
        """Check if all matches in a round are complete."""
        if round_num not in self.bracket:
            return False
        
        for match_id in self.bracket[round_num]:
            match = self.matches[match_id]
            if match.status != MatchStatus.COMPLETED:
                return False
        
        return True
    
    def _check_tournament_completion(self):
        """Check if tournament is complete."""
        final_round = max(self.bracket.keys())
        
        if self._is_round_complete(final_round):
            # Tournament is complete
            self.status = TournamentStatus.COMPLETED
            self.completed_at = datetime.utcnow()
            
            # Determine final rankings
            final_match = self.matches[self.bracket[final_round][0]]
            self.winner = final_match.winner
        else:
            # Start next round
            next_round = self.current_round + 1
            if next_round in self.bracket:
                self._start_round(next_round)
    
    def get_available_matches(self) -> List[TournamentMatch]:
        """Get matches that can be started."""
        available = []
        for match in self.matches.values():
            if match.can_start():
                available.append(match)
        return available
    
    def get_bracket_display(self) -> Dict[str, Any]:
        """Get bracket for display."""
        bracket_display = {}
        
        for round_num, match_ids in self.bracket.items():
            round_matches = []
            for match_id in match_ids:
                match = self.matches[match_id]
                round_matches.append(match.to_dict())
            
            bracket_display[f"round_{round_num}"] = {
                "round_number": round_num,
                "round_name": self._get_round_name(round_num),
                "matches": round_matches
            }
        
        return bracket_display
    
    def _get_round_name(self, round_num: int) -> str:
        """Get display name for round."""
        total_rounds = len(self.bracket)
        
        if round_num == total_rounds:
            return "Final"
        elif round_num == total_rounds - 1:
            return "Semi-Final"
        elif round_num == total_rounds - 2:
            return "Quarter-Final"
        else:
            return f"Round {round_num}"
    
    def calculate_prizes(self) -> Dict[str, float]:
        """Calculate prize distribution."""
        if self.status != TournamentStatus.COMPLETED:
            return {}
        
        prizes = {}
        
        # Winner
        if self.winner:
            prizes[self.winner] = self.prize_pool * self.prize_distribution[1]
        
        # Runner-up (loser of final)
        final_round = max(self.bracket.keys())
        final_match = self.matches[self.bracket[final_round][0]]
        runner_up = final_match.player1 if final_match.winner == final_match.player2 else final_match.player2
        if runner_up:
            prizes[runner_up] = self.prize_pool * self.prize_distribution[2]
        
        # Semi-finalists (if applicable)
        if len(self.bracket) >= 2:
            semi_final_round = final_round - 1
            if semi_final_round in self.bracket:
                semi_prize = self.prize_pool * self.prize_distribution[3] / 2
                for match_id in self.bracket[semi_final_round]:
                    match = self.matches[match_id]
                    loser = match.player1 if match.winner == match.player2 else match.player2
                    if loser and loser != runner_up:  # Don't double-pay runner-up
                        prizes[loser] = semi_prize
        
        return prizes
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert tournament to dictionary."""
        return {
            "tournament_id": self.tournament_id,
            "name": self.name,
            "game_type": self.game_type,
            "tournament_type": self.tournament_type.value,
            "max_players": self.max_players,
            "entry_fee": self.entry_fee,
            "prize_pool": self.prize_pool,
            "status": self.status.value,
            "players": self.players,
            "current_round": self.current_round,
            "winner": self.winner,
            "created_at": self.created_at.isoformat(),
            "started_at": self.started_at.isoformat() if self.started_at else None,
            "completed_at": self.completed_at.isoformat() if self.completed_at else None,
            "bracket": self.get_bracket_display(),
            "available_matches": [m.to_dict() for m in self.get_available_matches()],
            "prizes": self.calculate_prizes() if self.status == TournamentStatus.COMPLETED else {}
        }

class TournamentManager:
    def __init__(self):
        self.tournaments: Dict[str, Tournament] = {}
    
    def create_tournament(self, name: str, game_type: str, max_players: int = 8, 
                         entry_fee: float = 0.0) -> str:
        """Create a new tournament."""
        tournament_id = str(uuid.uuid4())[:8]
        tournament = Tournament(tournament_id, name, game_type, max_players, entry_fee)
        self.tournaments[tournament_id] = tournament
        return tournament_id
    
    def get_tournament(self, tournament_id: str) -> Optional[Tournament]:
        """Get tournament by ID."""
        return self.tournaments.get(tournament_id)
    
    def register_player(self, tournament_id: str, player_id: str) -> bool:
        """Register player for tournament."""
        tournament = self.get_tournament(tournament_id)
        if tournament:
            return tournament.register_player(player_id)
        return False
    
    def start_tournament(self, tournament_id: str) -> bool:
        """Start a tournament."""
        tournament = self.get_tournament(tournament_id)
        if tournament:
            return tournament.start_tournament()
        return False
    
    def complete_match(self, tournament_id: str, match_id: str, winner: str, score: Dict = None) -> bool:
        """Complete a tournament match."""
        tournament = self.get_tournament(tournament_id)
        if tournament:
            return tournament.complete_match(match_id, winner, score)
        return False
    
    def get_active_tournaments(self) -> List[Dict[str, Any]]:
        """Get all active tournaments."""
        active = []
        for tournament in self.tournaments.values():
            if tournament.status in [TournamentStatus.REGISTRATION, TournamentStatus.IN_PROGRESS]:
                active.append(tournament.to_dict())
        return active
    
    def get_tournament_summary(self, tournament_id: str) -> Optional[Dict[str, Any]]:
        """Get tournament summary."""
        tournament = self.get_tournament(tournament_id)
        if tournament:
            return tournament.to_dict()
        return None
