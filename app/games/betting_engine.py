#!/usr/bin/env python3
"""
Spectator Betting Engine for BetBet Platform
Real-time betting on game outcomes with dynamic odds
"""
from typing import Dict, List, Optional, Any
from enum import Enum
from datetime import datetime, timedelta
import uuid
import json

class BetType(Enum):
    GAME_WINNER = "game_winner"
    ROUND_WINNER = "round_winner"  # For RPS
    NEXT_MOVE = "next_move"        # For Chess
    GAME_DURATION = "game_duration"
    FIRST_CAPTURE = "first_capture"  # For Chess
    TOTAL_ROUNDS = "total_rounds"    # For RPS

class BetStatus(Enum):
    ACTIVE = "active"
    WON = "won"
    LOST = "lost"
    CANCELLED = "cancelled"
    PENDING = "pending"

class OddsCalculator:
    """Calculate dynamic odds based on game state and betting patterns."""
    
    @staticmethod
    def calculate_game_winner_odds(game_state: Dict, betting_pool: Dict) -> Dict[str, float]:
        """Calculate odds for game winner bets."""
        base_odds = 1.8  # Base odds for even match
        
        # Adjust based on game state
        if game_state.get("type") == "rock_paper_scissors":
            scores = game_state.get("state", {}).get("scores", {})
            if scores:
                players = list(scores.keys())
                if len(players) == 2:
                    score_diff = abs(scores[players[0]] - scores[players[1]])
                    # Adjust odds based on score difference
                    if score_diff >= 2:
                        leader = max(players, key=lambda p: scores[p])
                        return {
                            leader: 1.2,  # Lower odds for leader
                            players[1] if players[0] == leader else players[0]: 3.5  # Higher odds for underdog
                        }
        
        elif game_state.get("type") == "chess":
            # Adjust based on captured pieces, move count, etc.
            captured = game_state.get("captured_pieces", {"white": [], "black": []})
            white_value = sum(OddsCalculator._piece_values().get(p.get("type", ""), 0) for p in captured["black"])
            black_value = sum(OddsCalculator._piece_values().get(p.get("type", ""), 0) for p in captured["white"])
            
            if white_value > black_value + 3:
                return {"white": 1.3, "black": 2.8}
            elif black_value > white_value + 3:
                return {"white": 2.8, "black": 1.3}
        
        # Default even odds
        players = game_state.get("players", [])
        if len(players) == 2:
            return {players[0]: base_odds, players[1]: base_odds}
        
        return {}
    
    @staticmethod
    def _piece_values() -> Dict[str, int]:
        """Chess piece values for odds calculation."""
        return {
            "pawn": 1,
            "knight": 3,
            "bishop": 3,
            "rook": 5,
            "queen": 9,
            "king": 0
        }
    
    @staticmethod
    def calculate_round_winner_odds(game_state: Dict) -> Dict[str, float]:
        """Calculate odds for next round winner."""
        # For RPS, odds are generally even unless there's a pattern
        return {"player1": 1.9, "player2": 1.9, "tie": 3.2}
    
    @staticmethod
    def calculate_next_move_odds(game_state: Dict) -> Dict[str, float]:
        """Calculate odds for next move type in chess."""
        # Simplified - in reality would analyze position
        return {
            "capture": 2.5,
            "castle": 8.0,
            "check": 4.0,
            "normal": 1.2
        }

class Bet:
    def __init__(self, bet_id: str, user_id: str, game_id: str, bet_type: BetType, 
                 selection: str, amount: float, odds: float):
        self.bet_id = bet_id
        self.user_id = user_id
        self.game_id = game_id
        self.bet_type = bet_type
        self.selection = selection
        self.amount = amount
        self.odds = odds
        self.status = BetStatus.ACTIVE
        self.placed_at = datetime.utcnow()
        self.settled_at = None
        self.payout = 0.0
    
    def calculate_potential_payout(self) -> float:
        """Calculate potential payout if bet wins."""
        return self.amount * self.odds
    
    def settle_bet(self, won: bool) -> float:
        """Settle the bet and return payout amount."""
        self.settled_at = datetime.utcnow()
        if won:
            self.status = BetStatus.WON
            self.payout = self.calculate_potential_payout()
        else:
            self.status = BetStatus.LOST
            self.payout = 0.0
        return self.payout
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert bet to dictionary."""
        return {
            "bet_id": self.bet_id,
            "user_id": self.user_id,
            "game_id": self.game_id,
            "bet_type": self.bet_type.value,
            "selection": self.selection,
            "amount": self.amount,
            "odds": self.odds,
            "status": self.status.value,
            "potential_payout": self.calculate_potential_payout(),
            "actual_payout": self.payout,
            "placed_at": self.placed_at.isoformat(),
            "settled_at": self.settled_at.isoformat() if self.settled_at else None
        }

class BettingPool:
    def __init__(self, game_id: str):
        self.game_id = game_id
        self.bets: Dict[str, Bet] = {}
        self.total_pool = 0.0
        self.pool_by_selection: Dict[str, float] = {}
        self.active_bet_types: List[BetType] = []
    
    def add_bet(self, bet: Bet) -> bool:
        """Add a bet to the pool."""
        if bet.bet_id not in self.bets:
            self.bets[bet.bet_id] = bet
            self.total_pool += bet.amount
            
            if bet.selection not in self.pool_by_selection:
                self.pool_by_selection[bet.selection] = 0.0
            self.pool_by_selection[bet.selection] += bet.amount
            
            if bet.bet_type not in self.active_bet_types:
                self.active_bet_types.append(bet.bet_type)
            
            return True
        return False
    
    def get_bets_by_user(self, user_id: str) -> List[Bet]:
        """Get all bets by a specific user."""
        return [bet for bet in self.bets.values() if bet.user_id == user_id]
    
    def get_bets_by_type(self, bet_type: BetType) -> List[Bet]:
        """Get all bets of a specific type."""
        return [bet for bet in self.bets.values() if bet.bet_type == bet_type]
    
    def settle_bets(self, bet_type: BetType, winning_selection: str) -> Dict[str, float]:
        """Settle all bets of a specific type."""
        payouts = {}
        for bet in self.get_bets_by_type(bet_type):
            if bet.status == BetStatus.ACTIVE:
                won = bet.selection == winning_selection
                payout = bet.settle_bet(won)
                if payout > 0:
                    payouts[bet.user_id] = payouts.get(bet.user_id, 0) + payout
        return payouts
    
    def get_pool_summary(self) -> Dict[str, Any]:
        """Get summary of betting pool."""
        active_bets = [bet for bet in self.bets.values() if bet.status == BetStatus.ACTIVE]
        return {
            "game_id": self.game_id,
            "total_pool": self.total_pool,
            "active_bets": len(active_bets),
            "total_bets": len(self.bets),
            "pool_by_selection": self.pool_by_selection,
            "active_bet_types": [bt.value for bt in self.active_bet_types]
        }

class BettingEngine:
    def __init__(self):
        self.betting_pools: Dict[str, BettingPool] = {}
        self.user_balances: Dict[str, float] = {}  # In production, this would be in database
    
    def create_betting_pool(self, game_id: str) -> BettingPool:
        """Create a new betting pool for a game."""
        if game_id not in self.betting_pools:
            self.betting_pools[game_id] = BettingPool(game_id)
        return self.betting_pools[game_id]
    
    def place_bet(self, user_id: str, game_id: str, bet_type: BetType, 
                  selection: str, amount: float, game_state: Dict) -> Dict[str, Any]:
        """Place a bet and return result."""
        # Validate user has sufficient balance
        user_balance = self.user_balances.get(user_id, 0.0)
        if user_balance < amount:
            return {"success": False, "error": "Insufficient balance"}
        
        # Calculate current odds
        betting_pool = self.betting_pools.get(game_id)
        if not betting_pool:
            betting_pool = self.create_betting_pool(game_id)
        
        odds = self._get_current_odds(game_state, bet_type, selection, betting_pool)
        if not odds:
            return {"success": False, "error": "Invalid bet selection"}
        
        # Create bet
        bet_id = str(uuid.uuid4())[:8]
        bet = Bet(bet_id, user_id, game_id, bet_type, selection, amount, odds)
        
        # Add to pool
        if betting_pool.add_bet(bet):
            # Deduct from user balance
            self.user_balances[user_id] = user_balance - amount
            
            return {
                "success": True,
                "bet": bet.to_dict(),
                "new_balance": self.user_balances[user_id],
                "pool_summary": betting_pool.get_pool_summary()
            }
        
        return {"success": False, "error": "Failed to place bet"}
    
    def _get_current_odds(self, game_state: Dict, bet_type: BetType, 
                         selection: str, betting_pool: BettingPool) -> Optional[float]:
        """Get current odds for a bet."""
        if bet_type == BetType.GAME_WINNER:
            odds_map = OddsCalculator.calculate_game_winner_odds(game_state, betting_pool.pool_by_selection)
            return odds_map.get(selection)
        elif bet_type == BetType.ROUND_WINNER:
            odds_map = OddsCalculator.calculate_round_winner_odds(game_state)
            return odds_map.get(selection)
        elif bet_type == BetType.NEXT_MOVE:
            odds_map = OddsCalculator.calculate_next_move_odds(game_state)
            return odds_map.get(selection)
        
        return None
    
    def settle_game_bets(self, game_id: str, winner: str) -> Dict[str, float]:
        """Settle all game winner bets."""
        betting_pool = self.betting_pools.get(game_id)
        if betting_pool:
            payouts = betting_pool.settle_bets(BetType.GAME_WINNER, winner)
            # Add payouts to user balances
            for user_id, payout in payouts.items():
                self.user_balances[user_id] = self.user_balances.get(user_id, 0) + payout
            return payouts
        return {}
    
    def settle_round_bets(self, game_id: str, round_winner: str) -> Dict[str, float]:
        """Settle round winner bets."""
        betting_pool = self.betting_pools.get(game_id)
        if betting_pool:
            payouts = betting_pool.settle_bets(BetType.ROUND_WINNER, round_winner)
            for user_id, payout in payouts.items():
                self.user_balances[user_id] = self.user_balances.get(user_id, 0) + payout
            return payouts
        return {}
    
    def get_available_bets(self, game_id: str, game_state: Dict) -> Dict[str, Any]:
        """Get available betting options for a game."""
        betting_pool = self.betting_pools.get(game_id)
        if not betting_pool:
            betting_pool = self.create_betting_pool(game_id)
        
        available_bets = {}
        
        # Game winner bets (always available during game)
        if game_state.get("status") in ["waiting", "in_progress"]:
            game_winner_odds = OddsCalculator.calculate_game_winner_odds(game_state, betting_pool.pool_by_selection)
            if game_winner_odds:
                available_bets["game_winner"] = {
                    "type": "game_winner",
                    "options": game_winner_odds,
                    "description": "Bet on who will win the game"
                }
        
        # Round winner bets (for RPS)
        if game_state.get("type") == "rock_paper_scissors" and game_state.get("status") == "in_progress":
            round_odds = OddsCalculator.calculate_round_winner_odds(game_state)
            available_bets["round_winner"] = {
                "type": "round_winner",
                "options": round_odds,
                "description": "Bet on who will win the next round"
            }
        
        # Next move bets (for Chess)
        if game_state.get("type") == "chess" and game_state.get("status") == "in_progress":
            move_odds = OddsCalculator.calculate_next_move_odds(game_state)
            available_bets["next_move"] = {
                "type": "next_move",
                "options": move_odds,
                "description": "Bet on the type of next move"
            }
        
        return {
            "game_id": game_id,
            "available_bets": available_bets,
            "pool_summary": betting_pool.get_pool_summary()
        }
    
    def get_user_bets(self, user_id: str, game_id: str = None) -> List[Dict]:
        """Get user's betting history."""
        user_bets = []
        
        pools_to_check = [self.betting_pools[game_id]] if game_id and game_id in self.betting_pools else self.betting_pools.values()
        
        for pool in pools_to_check:
            user_bets.extend([bet.to_dict() for bet in pool.get_bets_by_user(user_id)])
        
        return sorted(user_bets, key=lambda x: x["placed_at"], reverse=True)
    
    def get_user_balance(self, user_id: str) -> float:
        """Get user's current balance."""
        return self.user_balances.get(user_id, 0.0)
    
    def set_user_balance(self, user_id: str, balance: float):
        """Set user's balance (for demo purposes)."""
        self.user_balances[user_id] = balance
