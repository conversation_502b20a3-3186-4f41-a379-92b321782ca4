"""Payment-related models for BetBet platform."""
from sqlalchemy import Column, Integer, String, Boolean, DateTime, Float, ForeignKey, Text
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.database.db import Base


class PaymentMethod(Base):
    """Payment method model."""
    __tablename__ = "payment_methods"

    id = Column(Integer, primary_key=True)
    wallet_id = Column(Integer, ForeignKey("wallets.id"), nullable=False)

    type = Column(String, nullable=False)  # credit_card, paypal, crypto
    is_default = Column(Boolean, default=False)
    is_active = Column(Boolean, default=True)

    # For cards
    last4 = Column(String(4), nullable=True)
    card_type = Column(String, nullable=True)  # visa, mastercard, etc.
    card_holder_name = Column(String, nullable=True)
    expiry_month = Column(Integer, nullable=True)
    expiry_year = Column(Integer, nullable=True)

    # For digital wallets
    email = Column(String, nullable=True)
    wallet_address = Column(String, nullable=True)

    # Security
    token = Column(String, nullable=True)  # Tokenized payment method ID from payment processor

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    last_used_at = Column(DateTime(timezone=True), nullable=True)

    # Relationships
    wallet = relationship("Wallet", back_populates="payment_methods")
    transactions = relationship("Transaction", back_populates="payment_method")


class Transaction(Base):
    """Enhanced transaction model."""
    __tablename__ = "transactions"

    id = Column(Integer, primary_key=True)
    wallet_id = Column(Integer, ForeignKey("wallets.id"), nullable=False)

    type = Column(String, nullable=False)  # deposit, withdrawal, bet_placed, bet_won, bet_lost, transfer
    amount = Column(Float, nullable=False)
    fee = Column(Float, default=0.0)
    net_amount = Column(Float, nullable=False)  # amount - fee

    # Balance tracking
    balance_before = Column(Float, nullable=False)
    balance_after = Column(Float, nullable=False)

    # Related entities
    game_id = Column(String, ForeignKey("games.id"), nullable=True)
    bet_id = Column(String, ForeignKey("bets.id"), nullable=True)
    payment_method_id = Column(Integer, ForeignKey("payment_methods.id"), nullable=True)
    recipient_wallet_id = Column(Integer, ForeignKey("wallets.id"), nullable=True)  # For transfers

    # Status
    status = Column(String, default="pending")  # pending, processing, completed, failed, cancelled

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    processed_at = Column(DateTime(timezone=True), nullable=True)
    completed_at = Column(DateTime(timezone=True), nullable=True)
    failed_at = Column(DateTime(timezone=True), nullable=True)

    # Additional info
    description = Column(Text, nullable=True)
    reference_number = Column(String, unique=True, nullable=False)  # For tracking
    processor_transaction_id = Column(String, nullable=True)  # From payment processor
    failure_reason = Column(Text, nullable=True)
    extra_data = Column(Text, nullable=True)  # JSON string for additional data

    # Relationships
    wallet = relationship("Wallet", back_populates="transactions", foreign_keys=[wallet_id])
    recipient_wallet = relationship("Wallet", foreign_keys=[recipient_wallet_id])
    payment_method = relationship("PaymentMethod", back_populates="transactions")
    game = relationship("Game")
    bet = relationship("Bet")

    def __repr__(self):
        return f"<Transaction {self.reference_number}: {self.type} {self.amount}>"