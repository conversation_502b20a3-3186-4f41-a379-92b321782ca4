"""
Watchlist model for tracking players that users want to follow
"""
import uuid
from sqlalchemy import Column, String, DateTime, ForeignKey, UniqueConstraint
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.database.db import Base


class Watchlist(Base):
    """User watchlist model for tracking followed players."""
    __tablename__ = "watchlists"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String, ForeignKey("users.id"), nullable=False)
    watched_user_id = Column(String, ForeignKey("users.id"), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    user = relationship("User", foreign_keys=[user_id], back_populates="watchlist_entries")
    watched_user = relationship("User", foreign_keys=[watched_user_id])

    # Ensure a user can't watch the same player twice
    __table_args__ = (
        UniqueConstraint('user_id', 'watched_user_id', name='unique_user_watchlist'),
    )

    def __repr__(self):
        return f"<Watchlist(user_id={self.user_id}, watched_user_id={self.watched_user_id})>"
