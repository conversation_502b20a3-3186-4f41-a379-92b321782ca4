"""
Game Catalog Model - Defines the inventory of available games on the platform
"""
from sqlalchemy import Column, String, Integer, Boolean, JSON, DateTime, Float
from sqlalchemy.sql import func
from app.database.db import Base
import uuid


class GameCatalog(Base):
    """Game catalog - inventory of available games on the platform"""
    __tablename__ = "game_catalog"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    
    # Basic game info
    name = Column(String, nullable=False)  # "Chess", "Rock Paper Scissors", etc.
    slug = Column(String, unique=True, nullable=False)  # "chess", "rock_paper_scissors"
    description = Column(String, nullable=True)
    category = Column(String, nullable=False)  # "strategy", "casual", "trivia", etc.
    
    # Game mechanics
    min_players = Column(Integer, default=2)
    max_players = Column(Integer, default=2)
    estimated_duration_minutes = Column(Integer, default=30)
    skill_based = Column(Boolean, default=True)
    
    # Platform settings
    is_active = Column(Boolean, default=True)
    is_featured = Column(Boolean, default=False)
    sort_order = Column(Integer, default=0)
    
    # Game configuration
    default_settings = Column(JSON, default={})  # Default game settings
    available_settings = Column(JSON, default={})  # Available customizable settings
    rules = Column(JSON, default={})  # Game rules and instructions
    
    # Betting configuration
    min_wager = Column(Float, default=0.0)
    max_wager = Column(Float, default=10000.0)
    allows_spectators = Column(Boolean, default=True)
    allows_tournaments = Column(Boolean, default=True)
    
    # UI/UX
    thumbnail_url = Column(String, nullable=True)
    icon_url = Column(String, nullable=True)
    background_color = Column(String, default="#1a1a1a")
    text_color = Column(String, default="#ffffff")
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    def to_dict(self):
        return {
            "id": self.id,
            "name": self.name,
            "slug": self.slug,
            "description": self.description,
            "category": self.category,
            "min_players": self.min_players,
            "max_players": self.max_players,
            "estimated_duration_minutes": self.estimated_duration_minutes,
            "skill_based": self.skill_based,
            "is_active": self.is_active,
            "is_featured": self.is_featured,
            "default_settings": self.default_settings,
            "available_settings": self.available_settings,
            "rules": self.rules,
            "min_wager": self.min_wager,
            "max_wager": self.max_wager,
            "allows_spectators": self.allows_spectators,
            "allows_tournaments": self.allows_tournaments,
            "thumbnail_url": self.thumbnail_url,
            "icon_url": self.icon_url,
            "background_color": self.background_color,
            "text_color": self.text_color,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }


# Game categories for organization
GAME_CATEGORIES = {
    "strategy": {
        "name": "Strategy Games",
        "description": "Games requiring strategic thinking and planning",
        "icon": "🧠"
    },
    "casual": {
        "name": "Casual Games", 
        "description": "Quick and easy games for everyone",
        "icon": "🎮"
    },
    "trivia": {
        "name": "Trivia & Knowledge",
        "description": "Test your knowledge and learn new facts",
        "icon": "🧩"
    },
    "card": {
        "name": "Card Games",
        "description": "Classic and modern card games",
        "icon": "🃏"
    },
    "sports": {
        "name": "Sports Games",
        "description": "Sports-related games and challenges",
        "icon": "⚽"
    },
    "puzzle": {
        "name": "Puzzle Games",
        "description": "Brain teasers and logic puzzles",
        "icon": "🧩"
    }
}

# Default game configurations for the 8 platform games
DEFAULT_GAMES = [
    {
        "name": "Chess",
        "slug": "chess",
        "description": "The classic strategy game of kings and queens",
        "category": "strategy",
        "min_players": 2,
        "max_players": 2,
        "estimated_duration_minutes": 30,
        "skill_based": True,
        "default_settings": {
            "time_limit": 600,
            "variant": "standard",
            "increment": 0,
            "rated": True
        },
        "available_settings": {
            "time_limit": {"type": "integer", "min": 60, "max": 3600, "default": 600},
            "variant": {"type": "select", "options": ["standard", "blitz", "bullet"], "default": "standard"},
            "increment": {"type": "integer", "min": 0, "max": 30, "default": 0},
            "rated": {"type": "boolean", "default": True}
        },
        "min_wager": 0.0,
        "max_wager": 5000.0,
        "thumbnail_url": "/images/games/chess-thumb.jpg",
        "icon_url": "/images/games/chess-icon.svg",
        "background_color": "#8B4513",
        "text_color": "#FFFFFF"
    },
    {
        "name": "Checkers",
        "slug": "checkers", 
        "description": "Classic board game of strategy and tactics",
        "category": "strategy",
        "min_players": 2,
        "max_players": 2,
        "estimated_duration_minutes": 20,
        "skill_based": True,
        "default_settings": {
            "variant": "american",
            "forced_capture": True,
            "time_limit": 300
        },
        "min_wager": 0.0,
        "max_wager": 2000.0,
        "thumbnail_url": "/images/games/checkers-thumb.jpg",
        "background_color": "#DC143C"
    },
    {
        "name": "Rock Paper Scissors",
        "slug": "rock_paper_scissors",
        "description": "Quick decision-making game of chance and psychology",
        "category": "casual",
        "min_players": 2,
        "max_players": 2,
        "estimated_duration_minutes": 5,
        "skill_based": False,
        "default_settings": {
            "rounds": 3,
            "reveal_simultaneously": True,
            "time_limit": 30
        },
        "min_wager": 0.0,
        "max_wager": 1000.0,
        "thumbnail_url": "/images/games/rps-thumb.jpg",
        "background_color": "#4169E1"
    },
    {
        "name": "Highlight Hero",
        "slug": "highlight_hero",
        "description": "Guess the sports highlights and test your sports knowledge",
        "category": "sports",
        "min_players": 2,
        "max_players": 8,
        "estimated_duration_minutes": 15,
        "skill_based": True,
        "default_settings": {
            "sport": "football",
            "difficulty": "medium",
            "rounds": 5
        },
        "min_wager": 0.0,
        "max_wager": 3000.0,
        "thumbnail_url": "/images/games/highlight-hero-thumb.jpg",
        "background_color": "#FF6347"
    },
    {
        "name": "Blur Detective",
        "slug": "blur_detective",
        "description": "Identify images as they become clearer",
        "category": "puzzle",
        "min_players": 2,
        "max_players": 6,
        "estimated_duration_minutes": 10,
        "skill_based": True,
        "default_settings": {
            "category": "mixed",
            "blur_levels": 5,
            "time_per_level": 10
        },
        "min_wager": 0.0,
        "max_wager": 2000.0,
        "thumbnail_url": "/images/games/blur-detective-thumb.jpg",
        "background_color": "#9370DB"
    },
    {
        "name": "Word Jumble",
        "slug": "word_jumble",
        "description": "Unscramble words as fast as you can",
        "category": "puzzle",
        "min_players": 2,
        "max_players": 4,
        "estimated_duration_minutes": 12,
        "skill_based": True,
        "default_settings": {
            "difficulty": "medium",
            "category": "general",
            "word_count": 10
        },
        "min_wager": 0.0,
        "max_wager": 1500.0,
        "thumbnail_url": "/images/games/word-jumble-thumb.jpg",
        "background_color": "#32CD32"
    },
    {
        "name": "Quiz Arena",
        "slug": "quiz_arena",
        "description": "Test your knowledge in various categories",
        "category": "trivia",
        "min_players": 2,
        "max_players": 10,
        "estimated_duration_minutes": 15,
        "skill_based": True,
        "default_settings": {
            "category": "general",
            "difficulty": "medium",
            "question_count": 10,
            "time_per_question": 30
        },
        "min_wager": 0.0,
        "max_wager": 4000.0,
        "thumbnail_url": "/images/games/quiz-arena-thumb.jpg",
        "background_color": "#FFD700"
    },
    {
        "name": "Crazy Eights",
        "slug": "crazy_eights",
        "description": "Classic card game with special card effects",
        "category": "card",
        "min_players": 2,
        "max_players": 4,
        "estimated_duration_minutes": 20,
        "skill_based": True,
        "default_settings": {
            "starting_cards": 7,
            "draw_penalty": 2,
            "special_rules": True
        },
        "min_wager": 0.0,
        "max_wager": 2500.0,
        "thumbnail_url": "/images/games/crazy-eights-thumb.jpg",
        "background_color": "#FF1493"
    }
]
