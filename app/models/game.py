from sqlalchemy import Column, String, Float, DateTime, JSO<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Integer
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.database.db import Base
import uuid
import enum

class GameStatus(enum.Enum):
    WAITING = "waiting"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    CANCELLED = "cancelled"

class GameType(enum.Enum):
    CHESS = "chess"
    CHECKERS = "checkers"
    ROCK_PAPER_SCISSORS = "rock_paper_scissors"
    HIGHLIGHT_HERO = "highlight_hero"
    BLUR_DETECTIVE = "blur_detective"
    WORD_JUMBLE = "word_jumble"
    QUIZ_ARENA = "quiz_arena"
    CRAZY_EIGHTS = "crazy_eights"

class Game(Base):
    __tablename__ = "games"

    id = Column(String, primary_key=True, index=True, default=lambda: str(uuid.uuid4()))
    game_type = Column(Enum(GameType), nullable=False)
    status = Column(Enum(GameStatus), default=GameStatus.WAITING)

    # Players
    player1_id = Column(String, ForeignKey("users.id"), nullable=False)
    player2_id = Column(String, ForeignKey("users.id"), nullable=True)
    current_player_id = Column(String, ForeignKey("users.id"), nullable=True)
    winner_id = Column(String, ForeignKey("users.id"), nullable=True)

    # Game settings
    settings = Column(JSON, default={})  # Game-specific settings
    state = Column(JSON, default={})     # Current game state (legacy)
    game_state = Column(JSON, default={})  # Enhanced game state

    # Betting
    wager_amount = Column(Float, default=0.0)
    total_pot = Column(Float, default=0.0)

    # Player limits and privacy
    max_players = Column(Integer, default=2)
    is_private = Column(Boolean, default=False)

    # Tournament reference (optional) - temporarily removed until migration is created
    # tournament_id = Column(String, ForeignKey("game_tournaments.id"), nullable=True)

    # Timing
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    started_at = Column(DateTime(timezone=True), nullable=True)
    completed_at = Column(DateTime(timezone=True), nullable=True)
    last_move_at = Column(DateTime(timezone=True), nullable=True)

    # Relationships
    player1 = relationship("User", foreign_keys=[player1_id], backref="games_as_player1")
    player2 = relationship("User", foreign_keys=[player2_id], backref="games_as_player2")
    current_player = relationship("User", foreign_keys=[current_player_id])
    winner = relationship("User", foreign_keys=[winner_id])

    # Enhanced relationships
    players = relationship("GamePlayer", back_populates="game")
    chat_messages = relationship("GameChat", back_populates="game")
    # tournament = relationship("GameTournament", back_populates="games", foreign_keys="Game.tournament_id", overlaps="games")

class GameMove(Base):
    __tablename__ = "game_moves"

    id = Column(String, primary_key=True, index=True, default=lambda: str(uuid.uuid4()))
    game_id = Column(String, ForeignKey("games.id"), nullable=False)
    player_id = Column(String, ForeignKey("users.id"), nullable=False)
    move_number = Column(Integer, nullable=False)
    move_data = Column(JSON, nullable=False)  # Game-specific move data
    timestamp = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    game = relationship("Game", backref="moves")
    player = relationship("User")

class GameSpectator(Base):
    __tablename__ = "game_spectators"

    id = Column(String, primary_key=True, index=True, default=lambda: str(uuid.uuid4()))
    game_id = Column(String, ForeignKey("games.id"), nullable=False)
    user_id = Column(String, ForeignKey("users.id"), nullable=False)
    joined_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    game = relationship("Game", backref="spectators")
    user = relationship("User")

class GameInvite(Base):
    __tablename__ = "game_invites"

    id = Column(String, primary_key=True, index=True, default=lambda: str(uuid.uuid4()))
    game_id = Column(String, ForeignKey("games.id"), nullable=False)
    inviter_id = Column(String, ForeignKey("users.id"), nullable=False)
    invited_user_id = Column(String, ForeignKey("users.id"), nullable=False)
    status = Column(String, default="pending")  # pending, accepted, declined
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    responded_at = Column(DateTime(timezone=True), nullable=True)

    # Relationships
    game = relationship("Game", backref="invites")
    inviter = relationship("User", foreign_keys=[inviter_id])
    invited_user = relationship("User", foreign_keys=[invited_user_id])