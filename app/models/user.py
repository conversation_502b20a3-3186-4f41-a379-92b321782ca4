"""User model for authentication and user management."""
from sqlalchemy import <PERSON>olean, Column, String, Float, DateTime, Integer, Text
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.database.db import Base
import uuid


class User(Base):
    """User model."""
    __tablename__ = "users"

    id = Column(String, primary_key=True, index=True, default=lambda: str(uuid.uuid4()))
    username = Column(String, unique=True, index=True, nullable=False)  # Display name for platform
    email = Column(String, unique=True, index=True, nullable=False)
    hashed_password = Column(String, nullable=False)
    is_active = Column(Boolean, default=True)
    is_verified = Column(Boolean, default=False)
    is_superuser = Column(Boolean, default=False)  # Admin flag

    # Profile fields
    avatar_url = Column(String, nullable=True)
    cover_photo_url = Column(String, nullable=True)
    bio = Column(Text, nullable=True)
    country = Column(String, nullable=True)
    status = Column(String, default="Rookie")  # Rookie, Pro Player, Elite, etc.

    # Gamification
    level = Column(Integer, default=1)
    xp = Column(Integer, default=0)
    next_level_xp = Column(Integer, default=1000)

    # Activity tracking
    last_active = Column(DateTime(timezone=True), server_default=func.now())
    open_to_challenges = Column(Boolean, default=True)

    # KYC and real identity fields
    real_name = Column(String, nullable=True)  # Legal name for admin/compliance
    id_card_url = Column(String, nullable=True)  # Path to uploaded ID card
    kyc_status = Column(String, default="pending")  # pending, approved, rejected
    kyc_submitted_at = Column(DateTime(timezone=True), nullable=True)
    kyc_verified_at = Column(DateTime(timezone=True), nullable=True)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    wallet = relationship("Wallet", back_populates="user", uselist=False)
    stats = relationship("UserStats", back_populates="user", uselist=False)
    games = relationship("GamePlayer", back_populates="user")
    bets = relationship("Bet", back_populates="user", foreign_keys='Bet.user_id')
    achievements = relationship("UserAchievement", back_populates="user")
    friendships = relationship("Friendship", foreign_keys='Friendship.user_id', back_populates="user")
    notifications = relationship("Notification", back_populates="user", foreign_keys='Notification.user_id')
    leaderboard_entry = relationship("LeaderboardEntry", back_populates="user", uselist=False)
    expert_profile = relationship("ExpertProfile", back_populates="user", uselist=False)
    watchlist_entries = relationship("Watchlist", foreign_keys='Watchlist.user_id', back_populates="user")