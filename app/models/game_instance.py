from sqlalchemy import Column, String, Integer, Text, DateTime, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Enum, JSO<PERSON>
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.database.db import Base
import enum
import uuid
from datetime import datetime

class GameType(str, enum.Enum):
    CHESS = "chess"
    CHECKERS = "checkers"
    ROCK_PAPER_SCISSORS = "rock_paper_scissors"
    HIGHLIGHT_HERO = "highlight_hero"
    BLUR_DETECTIVE = "blur_detective"
    WORD_JUMBLE = "word_jumble"
    QUIZ_ARENA = "quiz_arena"
    CRAZY_EIGHTS = "crazy_eights"

class GameStatus(str, enum.Enum):
    WAITING = "waiting"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    ABANDONED = "abandoned"

class GameInstance(Base):
    __tablename__ = "game_instances"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    game_catalog_id = Column(String, <PERSON><PERSON><PERSON>("game_catalog.id"), nullable=False)
    game_type = Column(Enum(GameType), nullable=False)  # Kept for backward compatibility
    status = Column(Enum(GameStatus), default=GameStatus.WAITING)

    # Game settings
    wager_amount = Column(Integer, default=0)
    max_players = Column(Integer, default=2)
    time_limit = Column(Integer)  # in seconds
    settings = Column(JSON, default={})  # Additional game-specific settings

    # Game state
    current_state = Column(JSON, default={})  # Current game state (board, scores, etc.)
    current_turn = Column(String)  # Current player's turn
    turn_count = Column(Integer, default=0)

    # Players
    host_id = Column(Integer, ForeignKey("users.id"))
    host = relationship("User", foreign_keys=[host_id])

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    started_at = Column(DateTime(timezone=True))
    completed_at = Column(DateTime(timezone=True))
    last_activity = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # Relationships
    game_catalog = relationship("GameCatalog", foreign_keys=[game_catalog_id])
    players = relationship("GamePlayer", back_populates="game", cascade="all, delete-orphan")
    moves = relationship("GameInstanceMove", back_populates="game", cascade="all, delete-orphan")

    def to_dict(self):
        return {
            "id": self.id,
            "game_type": self.game_type.value if self.game_type else None,
            "status": self.status.value if self.status else None,
            "wager_amount": self.wager_amount,
            "max_players": self.max_players,
            "time_limit": self.time_limit,
            "settings": self.settings,
            "current_state": self.current_state,
            "current_turn": self.current_turn,
            "turn_count": self.turn_count,
            "host_id": self.host_id,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "started_at": self.started_at.isoformat() if self.started_at else None,
            "last_activity": self.last_activity.isoformat() if self.last_activity else None,
            "players": [p.to_dict() for p in self.players],
        }

class GamePlayer(Base):
    __tablename__ = "game_players"

    id = Column(Integer, primary_key=True)
    game_id = Column(String, ForeignKey("game_instances.id"))
    user_id = Column(Integer, ForeignKey("users.id"))
    position = Column(Integer)  # 0 = first player, 1 = second player, etc.
    color = Column(String)  # For chess/checkers
    score = Column(Integer, default=0)
    is_ready = Column(Boolean, default=False)
    joined_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    game = relationship("GameInstance", back_populates="players")
    user = relationship("User")

    def to_dict(self):
        return {
            "id": self.id,
            "game_id": self.game_id,
            "user_id": self.user_id,
            "username": self.user.username if self.user else None,
            "position": self.position,
            "color": self.color,
            "score": self.score,
            "is_ready": self.is_ready,
            "joined_at": self.joined_at.isoformat() if self.joined_at else None,
        }

class GameInstanceMove(Base):
    __tablename__ = "game_instance_moves"

    id = Column(Integer, primary_key=True)
    game_id = Column(String, ForeignKey("game_instances.id"))
    player_id = Column(Integer, ForeignKey("users.id"))
    move_number = Column(Integer)
    move_data = Column(JSON)  # Game-specific move data
    timestamp = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    game = relationship("GameInstance", back_populates="moves")
    player = relationship("User")

    def to_dict(self):
        return {
            "id": self.id,
            "game_id": self.game_id,
            "player_id": self.player_id,
            "move_number": self.move_number,
            "move_data": self.move_data,
            "timestamp": self.timestamp.isoformat() if self.timestamp else None,
        }