"""Enhanced User models for BetBet platform."""
from sqlalchemy import Column, Integer, String, Boolean, DateTime, Float, Text, ForeignKey, JSON, UniqueConstraint
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.database.db import Base


class UserStats(Base):
    """User statistics model."""
    __tablename__ = "user_stats"

    id = Column(Integer, primary_key=True)
    user_id = Column(String, ForeignKey("users.id"), unique=True, nullable=False)

    # Game statistics
    games_played = Column(Integer, default=0)
    games_won = Column(Integer, default=0)
    win_rate = Column(Float, default=0.0)
    win_streak = Column(Integer, default=0)
    longest_win_streak = Column(Integer, default=0)

    # Financial statistics
    total_wagered = Column(Float, default=0.0)
    total_earned = Column(Float, default=0.0)
    biggest_win = Column(Float, default=0.0)
    biggest_loss = Column(Float, default=0.0)

    # Social statistics
    challenges_issued = Column(Integer, default=0)
    challenges_accepted = Column(Integer, default=0)
    challenges_won = Column(Integer, default=0)
    challenges_declined = Column(Integer, default=0)
    friends_count = Column(Integer, default=0)

    # Favorite game
    most_played_game = Column(String, nullable=True)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    user = relationship("User", back_populates="stats")


class Achievement(Base):
    """Achievement definition model."""
    __tablename__ = "achievements"

    id = Column(Integer, primary_key=True)
    name = Column(String, nullable=False, unique=True)
    description = Column(Text, nullable=False)
    icon = Column(String, nullable=True)
    category = Column(String, nullable=False)  # gaming, financial, social

    # Requirements
    requirement_type = Column(String, nullable=False)  # games_won, amount_earned, friends_count, etc.
    requirement_value = Column(Integer, nullable=False)

    # Rewards
    xp_reward = Column(Integer, default=100)
    token_reward = Column(Integer, default=0)
    badge_color = Column(String, default="bronze")  # bronze, silver, gold

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    user_achievements = relationship("UserAchievement", back_populates="achievement")


class UserAchievement(Base):
    """User achievement progress model."""
    __tablename__ = "user_achievements"

    id = Column(Integer, primary_key=True)
    user_id = Column(String, ForeignKey("users.id"), nullable=False)
    achievement_id = Column(Integer, ForeignKey("achievements.id"), nullable=False)

    # Progress tracking
    current_progress = Column(Integer, default=0)
    earned = Column(Boolean, default=False)
    earned_at = Column(DateTime(timezone=True), nullable=True)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    user = relationship("User", back_populates="achievements")
    achievement = relationship("Achievement", back_populates="user_achievements")

    __table_args__ = (UniqueConstraint('user_id', 'achievement_id'),)


class Friendship(Base):
    """User friendship model."""
    __tablename__ = "friendships"

    id = Column(Integer, primary_key=True)
    user_id = Column(String, ForeignKey("users.id"), nullable=False)
    friend_id = Column(String, ForeignKey("users.id"), nullable=False)
    status = Column(String, default="pending")  # pending, accepted, blocked
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    accepted_at = Column(DateTime(timezone=True), nullable=True)

    # Relationships
    user = relationship("User", foreign_keys=[user_id], back_populates="friendships")
    friend = relationship("User", foreign_keys=[friend_id])

    __table_args__ = (UniqueConstraint('user_id', 'friend_id'),)


class Notification(Base):
    """User notification model."""
    __tablename__ = "notifications"

    id = Column(Integer, primary_key=True)
    user_id = Column(String, ForeignKey("users.id"), nullable=False)

    type = Column(String, nullable=False)  # friend_request, challenge, bet_result, etc.
    title = Column(String, nullable=False)
    message = Column(Text, nullable=False)
    data = Column(JSON, nullable=True)  # Additional data for the notification

    # Related entities
    related_user_id = Column(String, ForeignKey("users.id"), nullable=True)
    related_game_id = Column(String, ForeignKey("games.id"), nullable=True)
    related_bet_id = Column(String, ForeignKey("bets.id"), nullable=True)

    # Status
    is_read = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    read_at = Column(DateTime(timezone=True), nullable=True)

    # Relationships
    user = relationship("User", back_populates="notifications", foreign_keys=[user_id])
    related_user = relationship("User", foreign_keys=[related_user_id])


class LeaderboardEntry(Base):
    """Leaderboard ranking model."""
    __tablename__ = "leaderboard_entries"

    id = Column(Integer, primary_key=True)
    user_id = Column(String, ForeignKey("users.id"), unique=True, nullable=False)

    # Rankings
    global_rank = Column(Integer, nullable=True)
    weekly_rank = Column(Integer, nullable=True)
    monthly_rank = Column(Integer, nullable=True)

    # Metrics
    total_earnings = Column(Float, default=0.0)
    win_rate = Column(Float, default=0.0)
    games_played = Column(Integer, default=0)

    # Time-based metrics
    week_earnings = Column(Float, default=0.0)
    month_earnings = Column(Float, default=0.0)

    # Timestamps
    last_updated = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    user = relationship("User")