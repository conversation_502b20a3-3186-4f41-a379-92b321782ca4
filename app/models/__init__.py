"""Database models for BetBet platform."""

# Core models
from app.models.user import User
from app.models.wallet import Wallet
from app.models.session import UserSession

# User profile models
from app.models.user_profile import (
    UserStats,
    Achievement,
    UserAchievement,
    Friendship,
    Notification,
    LeaderboardEntry
)

# Payment models
from app.models.payment import PaymentMethod, Transaction

# Marketplace models (betting)
from app.models.marketplace import (
    Bet,
    BetParticipation,
    BetStatus,
    BetResult,
    BetType,
    BetVisibility,
    CounterBet,
    BetInvite,
    BetTemplate
)

# Game models
from app.models.game import Game, GameMove, GameSpectator, GameStatus, GameType, GameInvite
from app.models.game_catalog import GameCatalog
# from app.models.game_instance import GameInstance  # Temporarily disabled due to conflicts
# from app.models.game_management import (  # Temporarily disabled due to conflicts
#     GamePlayer,
#     GameChat,
#     GameTournament,
#     TournamentParticipant
# )
from app.models.watchlist import Watchlist

# Expert Picks models
from app.models.expert_picks import (
    ExpertProfile,
    ExpertPick,
    PickPack,
    PickPackItem,
    PickPurchase,
    Pick<PERSON>ackPurchase,
    ExpertFollower,
    PickStatus,
    PickType
)

__all__ = [
    # Core
    'User',
    'Wallet',

    # User-related
    'UserStats',
    'Achievement',
    'UserAchievement',
    'Friendship',
    'Notification',
    'LeaderboardEntry',

    # Payment
    'PaymentMethod',
    'Transaction',

    # Bets
    'Bet',
    'BetParticipation',
    'BetStatus',
    'BetResult',
    'BetType',
    'BetVisibility',
    'CounterBet',
    'BetInvite',
    'BetTemplate',

    # Games
    'Game',
    'GameMove',
    'GameSpectator',
    'GameStatus',
    'GameType',
    'GamePlayer',
    'GameChat',
    'GameInvite',
    'GameTournament',
    'TournamentParticipant',

    # Session
    'UserSession',

    # Expert Picks
    'ExpertProfile',
    'ExpertPick',
    'PickPack',
    'PickPackItem',
    'PickPurchase',
    'PickPackPurchase',
    'ExpertFollower',
    'PickStatus',
    'PickType'
]