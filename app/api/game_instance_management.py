"""
Complete Game Instance Management API
Integrates with React game engines and WebSocket system
"""
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime
import uuid
import json

from app.database.db import get_db
from app.models.user import User
from app.models.game_catalog import GameCatalog
from app.models.game_instance import GameInstance, GamePlayer, GameType, GameStatus
from app.models.wallet import Wallet
from app.core.dependencies import get_current_user
from app.services.wallet import WalletService
from app.core.websocket import manager, broadcast_game_update

router = APIRouter()

# Pydantic Models
class CreateGameInstanceRequest(BaseModel):
    game_slug: str = Field(..., description="Game slug from catalog")
    wager_amount: float = Field(default=0.0, description="Wager amount per player")
    settings: Dict[str, Any] = Field(default_factory=dict, description="Game-specific settings")
    is_private: bool = Field(default=False, description="Private game (invite only)")
    max_players: Optional[int] = Field(default=None, description="Override max players")

class JoinGameRequest(BaseModel):
    game_instance_id: str = Field(..., description="Game instance ID to join")

class GameInstanceResponse(BaseModel):
    id: str
    game_name: str
    game_slug: str
    game_type: str
    status: str
    host_id: str
    host_username: str
    current_player_id: Optional[str]
    winner_id: Optional[str]
    wager_amount: float
    max_players: int
    current_players: int
    settings: Dict[str, Any]
    game_state: Dict[str, Any]
    created_at: datetime
    started_at: Optional[datetime]
    completed_at: Optional[datetime]
    players: List[Dict[str, Any]]

class PlayerResponse(BaseModel):
    user_id: str
    username: str
    position: int
    is_ready: bool
    joined_at: datetime
    is_host: bool

def generate_initial_game_state(game_slug: str, settings: Dict[str, Any]) -> Dict[str, Any]:
    """Generate initial game state for each game type"""

    if game_slug == "chess":
        return {
            "board": "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1",  # FEN notation
            "current_turn": "white",
            "move_history": [],
            "captured_pieces": {"white": [], "black": []},
            "check_status": None,
            "castling_rights": {
                "white": {"kingside": True, "queenside": True},
                "black": {"kingside": True, "queenside": True}
            },
            "en_passant": None,
            "half_move_clock": 0,
            "full_move_number": 1,
            "game_status": "playing"
        }

    elif game_slug == "checkers":
        # Initialize 8x8 checkers board
        board = [[0 for _ in range(8)] for _ in range(8)]
        # Place initial pieces (1=red, 2=black, 3=red king, 4=black king)
        for row in range(8):
            for col in range(8):
                if (row + col) % 2 == 1:  # Dark squares only
                    if row < 3:
                        board[row][col] = 2  # Black pieces
                    elif row > 4:
                        board[row][col] = 1  # Red pieces

        return {
            "board": board,
            "current_turn": "red",
            "captured_pieces": {"red": [], "black": []},
            "must_capture": None,
            "game_status": "playing"
        }

    elif game_slug == "rock_paper_scissors":
        return {
            "current_round": 1,
            "max_rounds": settings.get("rounds", 3),
            "scores": {},
            "round_history": [],
            "player_choices": {},
            "game_status": "playing"
        }

    elif game_slug == "highlight_hero":
        return {
            "current_round": 1,
            "max_rounds": settings.get("rounds", 5),
            "scores": {},
            "current_highlight": None,
            "sport": settings.get("sport", "football"),
            "difficulty": settings.get("difficulty", "medium"),
            "guesses": {},
            "game_status": "playing"
        }

    elif game_slug == "blur_detective":
        return {
            "current_round": 1,
            "max_rounds": settings.get("rounds", 5),
            "scores": {},
            "current_image": None,
            "blur_level": settings.get("blur_levels", 5),
            "category": settings.get("category", "mixed"),
            "guesses": {},
            "time_remaining": settings.get("time_per_level", 10),
            "game_status": "playing"
        }

    elif game_slug == "word_jumble":
        return {
            "current_word": 1,
            "total_words": settings.get("word_count", 10),
            "scores": {},
            "current_jumble": None,
            "solved_words": [],
            "difficulty": settings.get("difficulty", "medium"),
            "category": settings.get("category", "general"),
            "game_status": "playing"
        }

    elif game_slug == "quiz_arena":
        return {
            "current_question": 1,
            "total_questions": settings.get("question_count", 10),
            "scores": {},
            "current_question_data": None,
            "answers": {},
            "category": settings.get("category", "general"),
            "difficulty": settings.get("difficulty", "medium"),
            "time_per_question": settings.get("time_per_question", 30),
            "game_status": "playing"
        }

    elif game_slug == "crazy_eights":
        # Initialize deck and deal cards
        suits = ["hearts", "diamonds", "clubs", "spades"]
        ranks = ["A", "2", "3", "4", "5", "6", "7", "8", "9", "10", "J", "Q", "K"]
        deck = [{"suit": suit, "rank": rank} for suit in suits for rank in ranks]

        return {
            "deck": deck,
            "hands": {},
            "discard_pile": [],
            "current_suit": None,
            "current_rank": None,
            "turn_order": [],
            "current_player_index": 0,
            "direction": 1,  # 1 for clockwise, -1 for counter-clockwise
            "starting_cards": settings.get("starting_cards", 7),
            "special_rules": settings.get("special_rules", True),
            "game_status": "playing"
        }

    return {"game_status": "waiting"}

@router.post("/create", response_model=GameInstanceResponse)
async def create_game_instance(
    request: CreateGameInstanceRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Create a new game instance"""

    # Find game in catalog
    game_catalog = db.query(GameCatalog).filter(GameCatalog.slug == request.game_slug).first()
    if not game_catalog:
        raise HTTPException(status_code=404, detail="Game not found in catalog")

    if not game_catalog.is_active:
        raise HTTPException(status_code=400, detail="Game is not currently available")

    # Validate wager amount
    if request.wager_amount < game_catalog.min_wager or request.wager_amount > game_catalog.max_wager:
        raise HTTPException(
            status_code=400,
            detail=f"Wager must be between ${game_catalog.min_wager} and ${game_catalog.max_wager}"
        )

    # Check user funds
    if request.wager_amount > 0:
        wallet_service = WalletService(db)
        wallet = wallet_service.get_or_create_wallet(current_user)
        if wallet.balance < request.wager_amount:
            raise HTTPException(status_code=400, detail="Insufficient funds")

    # Merge settings
    final_settings = game_catalog.default_settings.copy()
    final_settings.update(request.settings)
    final_settings["is_private"] = request.is_private

    # Determine max players
    max_players = request.max_players or game_catalog.max_players
    if max_players > game_catalog.max_players:
        max_players = game_catalog.max_players

    # Create game instance
    game_instance = GameInstance(
        id=str(uuid.uuid4()),
        game_catalog_id=game_catalog.id,
        game_type=GameType(request.game_slug),
        status=GameStatus.WAITING,
        host_id=current_user.id,
        wager_amount=int(request.wager_amount * 100),  # Store as cents
        max_players=max_players,
        settings=final_settings,
        current_state=generate_initial_game_state(request.game_slug, final_settings)
    )

    # Add host as first player
    host_player = GamePlayer(
        game_id=game_instance.id,
        user_id=current_user.id,
        position=0,
        is_ready=True
    )

    # Deduct wager from host's wallet
    if request.wager_amount > 0:
        wallet_service = WalletService(db)
        wallet_service.deduct_balance(
            current_user,
            request.wager_amount,
            f"Game wager for {game_catalog.name}"
        )

    db.add(game_instance)
    db.add(host_player)
    db.commit()
    db.refresh(game_instance)

    # Get players for response
    players = db.query(GamePlayer).filter(GamePlayer.game_id == game_instance.id).all()
    player_responses = []
    for player in players:
        user = db.query(User).filter(User.id == player.user_id).first()
        player_responses.append({
            "user_id": player.user_id,
            "username": user.username if user else "Unknown",
            "position": player.position,
            "is_ready": player.is_ready,
            "joined_at": player.joined_at,
            "is_host": player.user_id == game_instance.host_id
        })

    return GameInstanceResponse(
        id=game_instance.id,
        game_name=game_catalog.name,
        game_slug=game_catalog.slug,
        game_type=game_instance.game_type.value,
        status=game_instance.status.value,
        host_id=game_instance.host_id,
        host_username=current_user.username,
        current_player_id=game_instance.current_player_id,
        winner_id=game_instance.winner_id,
        wager_amount=request.wager_amount,
        max_players=game_instance.max_players,
        current_players=len(player_responses),
        settings=game_instance.settings,
        game_state=game_instance.current_state,
        created_at=game_instance.created_at,
        started_at=game_instance.started_at,
        completed_at=game_instance.completed_at,
        players=player_responses
    )

@router.post("/join")
async def join_game_instance(
    request: JoinGameRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Join an existing game instance"""

    # Find game instance
    game_instance = db.query(GameInstance).filter(
        GameInstance.id == request.game_instance_id
    ).first()

    if not game_instance:
        raise HTTPException(status_code=404, detail="Game instance not found")

    if game_instance.status != GameStatus.WAITING:
        raise HTTPException(status_code=400, detail="Game is not accepting new players")

    # Check if user is already in the game
    existing_player = db.query(GamePlayer).filter(
        GamePlayer.game_id == game_instance.id,
        GamePlayer.user_id == current_user.id
    ).first()

    if existing_player:
        raise HTTPException(status_code=400, detail="You are already in this game")

    # Check if game is full
    current_players = db.query(GamePlayer).filter(
        GamePlayer.game_id == game_instance.id
    ).count()

    if current_players >= game_instance.max_players:
        raise HTTPException(status_code=400, detail="Game is full")

    # Check user funds for wager
    wager_amount = game_instance.wager_amount / 100.0  # Convert from cents
    if wager_amount > 0:
        wallet_service = WalletService(db)
        wallet = wallet_service.get_or_create_wallet(current_user)
        if wallet.balance < wager_amount:
            raise HTTPException(status_code=400, detail="Insufficient funds")

    # Add player to game
    new_player = GamePlayer(
        game_id=game_instance.id,
        user_id=current_user.id,
        position=current_players,
        is_ready=False
    )

    # Deduct wager from player's wallet
    if wager_amount > 0:
        wallet_service = WalletService(db)
        game_catalog = db.query(GameCatalog).filter(
            GameCatalog.id == game_instance.game_catalog_id
        ).first()
        wallet_service.deduct_balance(
            current_user,
            wager_amount,
            f"Game wager for {game_catalog.name if game_catalog else 'game'}"
        )

    db.add(new_player)
    db.commit()

    # Check if game should start (all players joined)
    total_players = db.query(GamePlayer).filter(
        GamePlayer.game_id == game_instance.id
    ).count()

    if total_players == game_instance.max_players:
        # Start the game
        game_instance.status = GameStatus.IN_PROGRESS
        game_instance.started_at = datetime.utcnow()

        # Set first player (usually host or position 0)
        first_player = db.query(GamePlayer).filter(
            GamePlayer.game_id == game_instance.id,
            GamePlayer.position == 0
        ).first()

        if first_player:
            game_instance.current_player_id = first_player.user_id

        db.commit()

        # Broadcast game start to all players
        await broadcast_game_update(game_instance.id, {
            "type": "game_started",
            "game_id": game_instance.id,
            "status": "in_progress",
            "current_player_id": game_instance.current_player_id,
            "game_state": game_instance.current_state
        })
    else:
        # Broadcast player joined
        await broadcast_game_update(game_instance.id, {
            "type": "player_joined",
            "game_id": game_instance.id,
            "player": {
                "user_id": current_user.id,
                "username": current_user.username,
                "position": new_player.position
            },
            "current_players": total_players,
            "max_players": game_instance.max_players
        })

    return {
        "success": True,
        "message": "Successfully joined game",
        "game_id": game_instance.id,
        "status": game_instance.status.value,
        "position": new_player.position,
        "current_players": total_players,
        "max_players": game_instance.max_players
    }

@router.get("/available")
async def get_available_games(
    game_slug: Optional[str] = Query(None, description="Filter by game type"),
    min_wager: Optional[float] = Query(None, description="Minimum wager"),
    max_wager: Optional[float] = Query(None, description="Maximum wager"),
    limit: int = Query(20, ge=1, le=100),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get available game instances to join"""

    query = db.query(GameInstance).filter(
        GameInstance.status == GameStatus.WAITING
    )

    # Filter by game type
    if game_slug:
        query = query.filter(GameInstance.game_type == GameType(game_slug))

    # Filter by wager amount
    if min_wager is not None:
        query = query.filter(GameInstance.wager_amount >= int(min_wager * 100))

    if max_wager is not None:
        query = query.filter(GameInstance.wager_amount <= int(max_wager * 100))

    # Exclude games where user is already a player
    user_game_ids = db.query(GamePlayer.game_id).filter(
        GamePlayer.user_id == current_user.id
    ).subquery()

    query = query.filter(~GameInstance.id.in_(user_game_ids))

    # Get games with available slots
    games = query.order_by(GameInstance.created_at.desc()).limit(limit).all()

    result = []
    for game in games:
        # Get current player count
        player_count = db.query(GamePlayer).filter(
            GamePlayer.game_id == game.id
        ).count()

        # Skip full games
        if player_count >= game.max_players:
            continue

        # Get game catalog info
        catalog = db.query(GameCatalog).filter(
            GameCatalog.id == game.game_catalog_id
        ).first()

        # Get host info
        host = db.query(User).filter(User.id == game.host_id).first()

        result.append({
            "id": game.id,
            "game_name": catalog.name if catalog else "Unknown Game",
            "game_slug": catalog.slug if catalog else game.game_type.value,
            "host_username": host.username if host else "Unknown",
            "wager_amount": game.wager_amount / 100.0,
            "current_players": player_count,
            "max_players": game.max_players,
            "settings": game.settings,
            "created_at": game.created_at,
            "estimated_duration": catalog.estimated_duration_minutes if catalog else 30
        })

    return {
        "games": result,
        "total": len(result)
    }

@router.get("/{game_instance_id}", response_model=GameInstanceResponse)
async def get_game_instance(
    game_instance_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get detailed game instance information"""

    game_instance = db.query(GameInstance).filter(
        GameInstance.id == game_instance_id
    ).first()

    if not game_instance:
        raise HTTPException(status_code=404, detail="Game instance not found")

    # Check if user has access (is player or spectator allowed)
    is_player = db.query(GamePlayer).filter(
        GamePlayer.game_id == game_instance.id,
        GamePlayer.user_id == current_user.id
    ).first() is not None

    # Get game catalog
    catalog = db.query(GameCatalog).filter(
        GameCatalog.id == game_instance.game_catalog_id
    ).first()

    # Get host info
    host = db.query(User).filter(User.id == game_instance.host_id).first()

    # Get all players
    players = db.query(GamePlayer).filter(
        GamePlayer.game_id == game_instance.id
    ).order_by(GamePlayer.position).all()

    player_responses = []
    for player in players:
        user = db.query(User).filter(User.id == player.user_id).first()
        player_responses.append({
            "user_id": player.user_id,
            "username": user.username if user else "Unknown",
            "position": player.position,
            "is_ready": player.is_ready,
            "joined_at": player.joined_at,
            "is_host": player.user_id == game_instance.host_id
        })

    # Only show game state to players
    game_state = game_instance.current_state if is_player else {}

    return GameInstanceResponse(
        id=game_instance.id,
        game_name=catalog.name if catalog else "Unknown Game",
        game_slug=catalog.slug if catalog else game_instance.game_type.value,
        game_type=game_instance.game_type.value,
        status=game_instance.status.value,
        host_id=game_instance.host_id,
        host_username=host.username if host else "Unknown",
        current_player_id=game_instance.current_player_id,
        winner_id=game_instance.winner_id,
        wager_amount=game_instance.wager_amount / 100.0,
        max_players=game_instance.max_players,
        current_players=len(player_responses),
        settings=game_instance.settings,
        game_state=game_state,
        created_at=game_instance.created_at,
        started_at=game_instance.started_at,
        completed_at=game_instance.completed_at,
        players=player_responses
    )

@router.post("/{game_instance_id}/move")
async def make_move(
    game_instance_id: str,
    move_data: Dict[str, Any],
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Process a game move"""

    game_instance = db.query(GameInstance).filter(
        GameInstance.id == game_instance_id
    ).first()

    if not game_instance:
        raise HTTPException(status_code=404, detail="Game instance not found")

    if game_instance.status != GameStatus.IN_PROGRESS:
        raise HTTPException(status_code=400, detail="Game is not in progress")

    # Check if user is a player in this game
    player = db.query(GamePlayer).filter(
        GamePlayer.game_id == game_instance.id,
        GamePlayer.user_id == current_user.id
    ).first()

    if not player:
        raise HTTPException(status_code=403, detail="You are not a player in this game")

    # For turn-based games, check if it's the player's turn
    if game_instance.current_player_id and game_instance.current_player_id != current_user.id:
        # Allow some games to have simultaneous moves (like RPS)
        if game_instance.game_type.value not in ["rock_paper_scissors", "blur_detective", "quiz_arena"]:
            raise HTTPException(status_code=400, detail="Not your turn")

    # Process the move and update game state
    # This would integrate with your existing game logic
    new_state = game_instance.current_state.copy()

    # Basic move processing - you would expand this based on game type
    if "move" in move_data:
        # Record the move
        from app.models.game_instance import GameInstanceMove
        game_move = GameInstanceMove(
            game_id=game_instance.id,
            player_id=current_user.id,
            move_number=len(game_instance.moves) + 1,
            move_data=move_data
        )
        db.add(game_move)

    # Update game state
    game_instance.current_state = new_state
    game_instance.last_activity = datetime.utcnow()

    # Check for game end conditions
    game_ended = False
    winner_id = None

    # You would implement game-specific win condition checking here

    if game_ended:
        game_instance.status = GameStatus.COMPLETED
        game_instance.winner_id = winner_id
        game_instance.completed_at = datetime.utcnow()

        # Handle prize distribution
        if game_instance.wager_amount > 0:
            await handle_game_completion(game_instance, db)

    db.commit()

    # Broadcast move to all players
    await broadcast_game_update(game_instance.id, {
        "type": "move_made",
        "game_id": game_instance.id,
        "player_id": current_user.id,
        "move_data": move_data,
        "game_state": new_state,
        "current_player_id": game_instance.current_player_id,
        "game_ended": game_ended,
        "winner_id": winner_id
    })

    return {
        "success": True,
        "game_state": new_state,
        "game_ended": game_ended,
        "winner_id": winner_id
    }

async def handle_game_completion(game_instance: GameInstance, db: Session):
    """Handle prize distribution when game completes"""
    if game_instance.winner_id and game_instance.wager_amount > 0:
        # Calculate total prize pool
        player_count = db.query(GamePlayer).filter(
            GamePlayer.game_id == game_instance.id
        ).count()

        total_prize = (game_instance.wager_amount / 100.0) * player_count

        # Award to winner
        winner = db.query(User).filter(User.id == game_instance.winner_id).first()
        if winner:
            wallet_service = WalletService(db)
            wallet_service.add_balance(
                winner,
                total_prize,
                f"Game win prize from {game_instance.game_type.value}"
            )
