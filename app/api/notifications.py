"""Notifications API endpoints."""
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime
from pydantic import BaseModel

from app.database.db import get_db
from app.models.user import User
from app.core.dependencies import get_current_user, get_current_user_optional
from app.services.notifications import NotificationService

router = APIRouter()

# Response schemas
class NotificationResponse(BaseModel):
    id: int
    type: str
    title: str
    message: str
    data: Optional[dict]
    is_read: bool
    created_at: datetime
    read_at: Optional[datetime]
    related_user_id: Optional[int]
    related_game_id: Optional[str]
    related_bet_id: Optional[int]

    class Config:
        from_attributes = True

class NotificationListResponse(BaseModel):
    notifications: List[NotificationResponse]
    total: int
    unread_count: int

class NotificationCountResponse(BaseModel):
    unread_count: int
    total_count: int

class MarkReadRequest(BaseModel):
    notification_ids: Optional[List[int]] = None  # If None, mark all as read

# Endpoints
@router.get("/", response_model=NotificationListResponse)
def get_notifications(
    unread_only: bool = False,
    limit: int = 50,
    offset: int = 0,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get user's notifications."""
    notification_service = NotificationService(db)

    if unread_only:
        notifications = notification_service.get_unread_notifications(current_user, limit)
        total = len(notifications)
    else:
        notifications, total = notification_service.get_all_notifications(
            current_user, limit, offset
        )

    # Get unread count
    from app.models.user_profile import Notification
    unread_count = db.query(Notification).filter(
        Notification.user_id == current_user.id,
        Notification.is_read == False
    ).count()

    return {
        "notifications": notifications,
        "total": total,
        "unread_count": unread_count
    }

@router.get("/count", response_model=NotificationCountResponse)
def get_notification_count(
    db: Session = Depends(get_db),
    current_user: Optional[User] = Depends(get_current_user_optional)
):
    """Get notification counts."""
    # If user is not authenticated, return demo data
    if not current_user:
        return {
            "unread_count": 3,
            "total_count": 12
        }

    from app.models.user_profile import Notification

    total_count = db.query(Notification).filter(
        Notification.user_id == current_user.id
    ).count()

    unread_count = db.query(Notification).filter(
        Notification.user_id == current_user.id,
        Notification.is_read == False
    ).count()

    return {
        "unread_count": unread_count,
        "total_count": total_count
    }

@router.get("/{notification_id}", response_model=NotificationResponse)
def get_notification(
    notification_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get a specific notification."""
    from app.models.user_profile import Notification

    notification = db.query(Notification).filter(
        Notification.id == notification_id,
        Notification.user_id == current_user.id
    ).first()

    if not notification:
        raise HTTPException(status_code=404, detail="Notification not found")

    return notification

@router.post("/mark-read")
def mark_notifications_read(
    request: MarkReadRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Mark notifications as read."""
    notification_service = NotificationService(db)

    if request.notification_ids is None:
        # Mark all as read
        count = notification_service.mark_all_as_read(current_user)
        return {"message": f"Marked {count} notifications as read"}
    else:
        # Mark specific notifications as read
        success_count = 0
        for notification_id in request.notification_ids:
            if notification_service.mark_as_read(notification_id, current_user):
                success_count += 1

        return {"message": f"Marked {success_count} notifications as read"}

@router.delete("/{notification_id}")
def delete_notification(
    notification_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Delete a notification."""
    notification_service = NotificationService(db)

    if not notification_service.delete_notification(notification_id, current_user):
        raise HTTPException(status_code=404, detail="Notification not found")

    return {"message": "Notification deleted"}

@router.delete("/")
def delete_all_notifications(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Delete all notifications for the user."""
    from app.models.user_profile import Notification

    count = db.query(Notification).filter(
        Notification.user_id == current_user.id
    ).delete()

    db.commit()

    return {"message": f"Deleted {count} notifications"}