"""
Enhanced Game Management API using existing games table
Integrates with React game engines and WebSocket system
"""
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime
import uuid

from app.database.db import get_db
from app.models.user import User
from app.models.game import Game, GameMove, GameStatus, GameType
from app.models.game_management import GamePlayer, GameChat
from app.models.wallet import Wallet
from app.core.dependencies import get_current_user
from app.services.wallet import WalletService
from app.core.websocket import manager, broadcast_game_update

router = APIRouter()

# Pydantic Models
class CreateGameRequest(BaseModel):
    game_type: str = Field(..., description="Game type slug")
    wager_amount: float = Field(default=0.0, description="Wager amount per player")
    settings: Dict[str, Any] = Field(default_factory=dict, description="Game-specific settings")
    is_private: bool = Field(default=False, description="Private game (invite only)")
    max_players: int = Field(default=2, description="Maximum players (2-10)")

class JoinGameRequest(BaseModel):
    game_id: str = Field(..., description="Game ID to join")

class GameMoveRequest(BaseModel):
    move_data: Dict[str, Any] = Field(..., description="Move data specific to game type")

def generate_initial_game_state(game_type: str, settings: Dict[str, Any]) -> Dict[str, Any]:
    """Generate initial game state for each game type"""

    if game_type == "chess":
        return {
            "board": "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1",  # FEN notation
            "current_turn": "white",
            "move_history": [],
            "captured_pieces": {"white": [], "black": []},
            "check_status": None,
            "castling_rights": {
                "white": {"kingside": True, "queenside": True},
                "black": {"kingside": True, "queenside": True}
            },
            "en_passant": None,
            "half_move_clock": 0,
            "full_move_number": 1,
            "game_status": "playing"
        }

    elif game_type == "checkers":
        # Initialize 8x8 checkers board
        board = [[0 for _ in range(8)] for _ in range(8)]
        # Place initial pieces (1=red, 2=black, 3=red king, 4=black king)
        for row in range(8):
            for col in range(8):
                if (row + col) % 2 == 1:  # Dark squares only
                    if row < 3:
                        board[row][col] = 2  # Black pieces
                    elif row > 4:
                        board[row][col] = 1  # Red pieces

        return {
            "board": board,
            "current_turn": "red",
            "captured_pieces": {"red": [], "black": []},
            "must_capture": None,
            "game_status": "playing"
        }

    elif game_type == "rock_paper_scissors":
        return {
            "current_round": 1,
            "max_rounds": settings.get("rounds", 3),
            "scores": {},
            "round_history": [],
            "player_choices": {},
            "game_status": "playing"
        }

    elif game_type == "highlight_hero":
        return {
            "current_round": 1,
            "max_rounds": settings.get("rounds", 5),
            "scores": {},
            "current_highlight": None,
            "sport": settings.get("sport", "football"),
            "difficulty": settings.get("difficulty", "medium"),
            "guesses": {},
            "game_status": "playing"
        }

    elif game_type == "blur_detective":
        return {
            "current_round": 1,
            "max_rounds": settings.get("rounds", 5),
            "scores": {},
            "current_image": None,
            "blur_level": settings.get("blur_levels", 5),
            "category": settings.get("category", "mixed"),
            "guesses": {},
            "time_remaining": settings.get("time_per_level", 10),
            "game_status": "playing"
        }

    elif game_type == "word_jumble":
        return {
            "current_word": 1,
            "total_words": settings.get("word_count", 10),
            "scores": {},
            "current_jumble": None,
            "solved_words": [],
            "difficulty": settings.get("difficulty", "medium"),
            "category": settings.get("category", "general"),
            "game_status": "playing"
        }

    elif game_type == "quiz_arena":
        return {
            "current_question": 1,
            "total_questions": settings.get("question_count", 10),
            "scores": {},
            "current_question_data": None,
            "answers": {},
            "category": settings.get("category", "general"),
            "difficulty": settings.get("difficulty", "medium"),
            "time_per_question": settings.get("time_per_question", 30),
            "game_status": "playing"
        }

    elif game_type == "crazy_eights":
        # Initialize deck and deal cards
        suits = ["hearts", "diamonds", "clubs", "spades"]
        ranks = ["A", "2", "3", "4", "5", "6", "7", "8", "9", "10", "J", "Q", "K"]
        deck = [{"suit": suit, "rank": rank} for suit in suits for rank in ranks]

        return {
            "deck": deck,
            "hands": {},
            "discard_pile": [],
            "current_suit": None,
            "current_rank": None,
            "turn_order": [],
            "current_player_index": 0,
            "direction": 1,  # 1 for clockwise, -1 for counter-clockwise
            "starting_cards": settings.get("starting_cards", 7),
            "special_rules": settings.get("special_rules", True),
            "game_status": "playing"
        }

    return {"game_status": "waiting"}

@router.post("/create")
async def create_game(
    request: CreateGameRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Create a new game using existing games table"""

    # Validate game type
    try:
        game_type_enum = GameType(request.game_type)
    except ValueError:
        raise HTTPException(status_code=400, detail=f"Invalid game type: {request.game_type}")

    # Validate max players
    if request.max_players < 2 or request.max_players > 10:
        raise HTTPException(status_code=400, detail="Max players must be between 2 and 10")

    # Check user funds for wager
    if request.wager_amount > 0:
        wallet_service = WalletService(db)
        wallet = wallet_service.get_or_create_wallet(current_user)
        if wallet.balance < request.wager_amount:
            raise HTTPException(status_code=400, detail="Insufficient funds")

    # Merge settings with defaults
    final_settings = {
        "is_private": request.is_private,
        "max_players": request.max_players,
        **request.settings
    }

    # Create game using existing Game model
    game = Game(
        id=str(uuid.uuid4()),
        game_type=game_type_enum,
        status=GameStatus.WAITING,
        player1_id=current_user.id,  # Host is player1
        wager_amount=request.wager_amount,
        total_pot=request.wager_amount,  # Will increase as players join
        max_players=request.max_players,
        is_private=request.is_private,
        settings=final_settings,
        game_state=generate_initial_game_state(request.game_type, final_settings)
    )

    # Add host as first player in game_players table
    game_player = GamePlayer(
        game_id=game.id,
        user_id=current_user.id,
        player_number=1,
        amount_wagered=request.wager_amount,
        status="ready"
    )

    # Save game first so it exists in database
    db.add(game)
    db.add(game_player)
    db.commit()
    db.refresh(game)

    # Now deduct wager from host's wallet (after game exists in DB)
    if request.wager_amount > 0:
        wallet_service = WalletService(db)
        wallet_service.place_game_wager(
            current_user,
            request.wager_amount,
            game.id,
            f"Game wager for {request.game_type}"
        )

    return {
        "success": True,
        "game_id": game.id,
        "game_type": game.game_type.value,
        "status": game.status.value,
        "wager_amount": game.wager_amount,
        "max_players": game.max_players,
        "settings": game.settings,
        "game_state": game.game_state,
        "created_at": game.created_at,
        "host_id": current_user.id,
        "host_username": current_user.username
    }

@router.post("/join")
async def join_game(
    request: JoinGameRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Join an existing game"""

    # Find the game
    game = db.query(Game).filter(Game.id == request.game_id).first()
    if not game:
        raise HTTPException(status_code=404, detail="Game not found")

    if game.status != GameStatus.WAITING:
        raise HTTPException(status_code=400, detail="Game is not accepting new players")

    # Check if user is already in the game
    existing_player = db.query(GamePlayer).filter(
        GamePlayer.game_id == game.id,
        GamePlayer.user_id == current_user.id
    ).first()

    if existing_player:
        raise HTTPException(status_code=400, detail="You are already in this game")

    # Check if game is full
    current_player_count = db.query(GamePlayer).filter(
        GamePlayer.game_id == game.id
    ).count()

    if current_player_count >= game.max_players:
        raise HTTPException(status_code=400, detail="Game is full")

    # Check user funds for wager
    if game.wager_amount > 0:
        wallet_service = WalletService(db)
        wallet = wallet_service.get_or_create_wallet(current_user)
        if wallet.balance < game.wager_amount:
            raise HTTPException(status_code=400, detail="Insufficient funds")

    # Add player to game
    game_player = GamePlayer(
        game_id=game.id,
        user_id=current_user.id,
        player_number=current_player_count + 1,
        amount_wagered=game.wager_amount,
        status="ready"
    )

    # Update game state
    if current_player_count == 0:  # Second player joining
        game.player2_id = current_user.id

    # Update total pot
    game.total_pot += game.wager_amount

    # Deduct wager from player's wallet
    if game.wager_amount > 0:
        wallet_service = WalletService(db)
        wallet_service.place_game_wager(
            current_user,
            game.wager_amount,
            game.id,
            f"Game wager for {game.game_type.value}"
        )

    db.add(game_player)

    # Check if game should start
    new_player_count = current_player_count + 1
    if new_player_count >= 2:  # Minimum players to start
        game.status = GameStatus.IN_PROGRESS
        game.started_at = datetime.utcnow()
        game.current_player_id = game.player1_id  # Host starts first

    db.commit()

    # Broadcast to all players
    if game.status == GameStatus.IN_PROGRESS:
        await broadcast_game_update(game.id, {
            "type": "game_started",
            "game_id": game.id,
            "status": "in_progress",
            "current_player_id": game.current_player_id,
            "game_state": game.game_state,
            "players": new_player_count
        })
    else:
        await broadcast_game_update(game.id, {
            "type": "player_joined",
            "game_id": game.id,
            "player": {
                "user_id": current_user.id,
                "username": current_user.username,
                "player_number": game_player.player_number
            },
            "current_players": new_player_count,
            "max_players": game.max_players
        })

    return {
        "success": True,
        "message": "Successfully joined game",
        "game_id": game.id,
        "status": game.status.value,
        "player_number": game_player.player_number,
        "current_players": new_player_count,
        "max_players": game.max_players
    }

@router.get("/available")
async def get_available_games(
    game_type: Optional[str] = Query(None, description="Filter by game type"),
    min_wager: Optional[float] = Query(None, description="Minimum wager"),
    max_wager: Optional[float] = Query(None, description="Maximum wager"),
    limit: int = Query(20, ge=1, le=100),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get available games to join"""

    query = db.query(Game).filter(
        Game.status == GameStatus.WAITING,
        Game.is_private == False
    )

    # Filter by game type
    if game_type:
        try:
            game_type_enum = GameType(game_type)
            query = query.filter(Game.game_type == game_type_enum)
        except ValueError:
            raise HTTPException(status_code=400, detail=f"Invalid game type: {game_type}")

    # Filter by wager amount
    if min_wager is not None:
        query = query.filter(Game.wager_amount >= min_wager)

    if max_wager is not None:
        query = query.filter(Game.wager_amount <= max_wager)

    # Exclude games where user is already a player
    user_game_ids = db.query(GamePlayer.game_id).filter(
        GamePlayer.user_id == current_user.id
    ).subquery()

    query = query.filter(~Game.id.in_(user_game_ids))

    games = query.order_by(Game.created_at.desc()).limit(limit).all()

    result = []
    for game in games:
        # Get current player count
        player_count = db.query(GamePlayer).filter(
            GamePlayer.game_id == game.id
        ).count()

        # Skip full games
        if player_count >= game.max_players:
            continue

        # Get host info
        host = db.query(User).filter(User.id == game.player1_id).first()

        result.append({
            "id": game.id,
            "game_type": game.game_type.value,
            "host_username": host.username if host else "Unknown",
            "wager_amount": game.wager_amount,
            "current_players": player_count,
            "max_players": game.max_players,
            "settings": game.settings,
            "created_at": game.created_at
        })

    return {
        "games": result,
        "total": len(result)
    }

@router.get("/{game_id}")
async def get_game_details(
    game_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get detailed game information"""

    game = db.query(Game).filter(Game.id == game_id).first()
    if not game:
        raise HTTPException(status_code=404, detail="Game not found")

    # Check if user is a player
    is_player = db.query(GamePlayer).filter(
        GamePlayer.game_id == game.id,
        GamePlayer.user_id == current_user.id
    ).first() is not None

    # Get all players
    players = db.query(GamePlayer).filter(
        GamePlayer.game_id == game.id
    ).order_by(GamePlayer.player_number).all()

    player_list = []
    for player in players:
        user = db.query(User).filter(User.id == player.user_id).first()
        player_list.append({
            "user_id": player.user_id,
            "username": user.username if user else "Unknown",
            "player_number": player.player_number,
            "status": player.status,
            "amount_wagered": player.amount_wagered,
            "is_host": player.user_id == game.player1_id
        })

    # Only show game state to players
    game_state = game.game_state if is_player else {}

    return {
        "id": game.id,
        "game_type": game.game_type.value,
        "status": game.status.value,
        "wager_amount": game.wager_amount,
        "total_pot": game.total_pot,
        "max_players": game.max_players,
        "current_players": len(player_list),
        "settings": game.settings,
        "game_state": game_state,
        "current_player_id": game.current_player_id,
        "winner_id": game.winner_id,
        "created_at": game.created_at,
        "started_at": game.started_at,
        "completed_at": game.completed_at,
        "players": player_list,
        "is_player": is_player
    }

@router.post("/{game_id}/move")
async def make_move(
    game_id: str,
    request: GameMoveRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Process a game move"""

    game = db.query(Game).filter(Game.id == game_id).first()
    if not game:
        raise HTTPException(status_code=404, detail="Game not found")

    if game.status != GameStatus.IN_PROGRESS:
        raise HTTPException(status_code=400, detail="Game is not in progress")

    # Check if user is a player
    player = db.query(GamePlayer).filter(
        GamePlayer.game_id == game.id,
        GamePlayer.user_id == current_user.id
    ).first()

    if not player:
        raise HTTPException(status_code=403, detail="You are not a player in this game")

    # For turn-based games, check if it's the player's turn
    if game.current_player_id and game.current_player_id != current_user.id:
        # Allow simultaneous moves for certain games
        if game.game_type.value not in ["rock_paper_scissors", "blur_detective", "quiz_arena"]:
            raise HTTPException(status_code=400, detail="Not your turn")

    # Record the move
    move_number = db.query(GameMove).filter(GameMove.game_id == game.id).count() + 1
    game_move = GameMove(
        id=str(uuid.uuid4()),
        game_id=game.id,
        player_id=current_user.id,
        move_number=move_number,
        move_data=request.move_data
    )

    # Update game state (this would be expanded with game-specific logic)
    new_state = game.game_state.copy()

    # Basic state update - you would expand this with game-specific logic
    if "board" in request.move_data:
        new_state["board"] = request.move_data["board"]

    if "scores" in request.move_data:
        new_state["scores"] = request.move_data["scores"]

    # Check for game end conditions
    game_ended = request.move_data.get("game_ended", False)
    winner_id = request.move_data.get("winner_id")

    # Update game
    game.game_state = new_state
    game.last_move_at = datetime.utcnow()

    if game_ended:
        game.status = GameStatus.COMPLETED
        game.winner_id = winner_id
        game.completed_at = datetime.utcnow()

        # Handle prize distribution
        if winner_id and game.total_pot > 0:
            winner = db.query(User).filter(User.id == winner_id).first()
            if winner:
                wallet_service = WalletService(db)
                wallet_service.process_game_win(
                    winner,
                    game.total_pot,
                    game.id,
                    f"Game win prize from {game.game_type.value}"
                )

                # Update player record
                winner_player = db.query(GamePlayer).filter(
                    GamePlayer.game_id == game.id,
                    GamePlayer.user_id == winner_id
                ).first()
                if winner_player:
                    winner_player.is_winner = True
                    winner_player.amount_won = game.total_pot
    else:
        # Switch turns for turn-based games
        if game.game_type.value in ["chess", "checkers"]:
            if game.current_player_id == game.player1_id:
                game.current_player_id = game.player2_id
            else:
                game.current_player_id = game.player1_id

    db.add(game_move)
    db.commit()

    # Broadcast move to all players
    await broadcast_game_update(game.id, {
        "type": "move_made",
        "game_id": game.id,
        "player_id": current_user.id,
        "move_data": request.move_data,
        "game_state": new_state,
        "current_player_id": game.current_player_id,
        "game_ended": game_ended,
        "winner_id": winner_id,
        "move_number": move_number
    })

    return {
        "success": True,
        "game_state": new_state,
        "current_player_id": game.current_player_id,
        "game_ended": game_ended,
        "winner_id": winner_id,
        "move_number": move_number
    }
