"""
Unified Game Instance Management API
Handles creation, setup, and management of all 8 game types on the platform
Uses proper architecture: game_catalog (inventory) + game_instances (sessions)
"""
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime, timedelta
import uuid

from app.database.db import get_db
from app.models.user import User
from app.models.game_catalog import GameCatalog, DEFAULT_GAMES
from app.models.game_instance import GameInstance, GamePlayer, GameInstanceMove, GameType, GameStatus
from app.models.wallet import Wallet
from app.core.dependencies import get_current_user
from app.services.wallet import WalletService

router = APIRouter()

# Game Configuration Models
class GameSettings(BaseModel):
    """Base game settings that apply to all games"""
    time_limit: Optional[int] = Field(default=300, description="Time limit per player in seconds")
    is_timed: bool = Field(default=False, description="Whether the game has time limits")
    is_private: bool = Field(default=False, description="Whether the game is private")
    max_spectators: int = Field(default=50, description="Maximum number of spectators")
    allow_chat: bool = Field(default=True, description="Allow in-game chat")

class ChessSettings(GameSettings):
    """Chess-specific settings"""
    variant: str = Field(default="standard", description="Chess variant: standard, blitz, bullet")
    increment: int = Field(default=0, description="Time increment per move in seconds")
    rated: bool = Field(default=True, description="Whether the game affects rating")

class CheckersSettings(GameSettings):
    """Checkers-specific settings"""
    variant: str = Field(default="american", description="Checkers variant: american, international")
    forced_capture: bool = Field(default=True, description="Force capture when available")

class RPSSettings(GameSettings):
    """Rock Paper Scissors settings"""
    rounds: int = Field(default=3, description="Number of rounds (best of)")
    reveal_simultaneously: bool = Field(default=True, description="Reveal moves simultaneously")

class HighlightHeroSettings(GameSettings):
    """Highlight Hero settings"""
    sport: str = Field(default="football", description="Sport type: football, basketball, soccer")
    difficulty: str = Field(default="medium", description="Difficulty: easy, medium, hard")
    rounds: int = Field(default=5, description="Number of highlights to guess")

class BlurDetectiveSettings(GameSettings):
    """Blur Detective settings"""
    category: str = Field(default="mixed", description="Image category: sports, celebrities, landmarks, mixed")
    blur_levels: int = Field(default=5, description="Number of blur reduction levels")
    time_per_level: int = Field(default=10, description="Time per blur level in seconds")

class WordJumbleSettings(GameSettings):
    """Word Jumble settings"""
    difficulty: str = Field(default="medium", description="Word difficulty: easy, medium, hard")
    category: str = Field(default="general", description="Word category: general, sports, science, etc.")
    word_count: int = Field(default=10, description="Number of words to solve")

class QuizArenaSettings(GameSettings):
    """Quiz Arena settings"""
    category: str = Field(default="general", description="Quiz category")
    difficulty: str = Field(default="medium", description="Question difficulty")
    question_count: int = Field(default=10, description="Number of questions")
    time_per_question: int = Field(default=30, description="Time per question in seconds")

class CrazyEightsSettings(GameSettings):
    """Crazy Eights card game settings"""
    starting_cards: int = Field(default=7, description="Number of starting cards")
    draw_penalty: int = Field(default=2, description="Cards to draw when can't play")
    special_rules: bool = Field(default=True, description="Enable special card effects")

# Game Creation Request Models
class CreateGameRequest(BaseModel):
    """Request to create a new game instance"""
    game_type: str = Field(..., description="Type of game to create")
    wager_amount: float = Field(default=0.0, description="Wager amount for the game")
    settings: Dict[str, Any] = Field(default_factory=dict, description="Game-specific settings")
    invite_player: Optional[str] = Field(default=None, description="Player ID to invite directly")
    tournament_id: Optional[str] = Field(default=None, description="Tournament this game belongs to")

class JoinGameRequest(BaseModel):
    """Request to join an existing game"""
    game_id: str = Field(..., description="ID of the game to join")

class GameResponse(BaseModel):
    """Response model for game operations"""
    id: str
    game_type: str
    status: str
    player1_id: Optional[str]
    player2_id: Optional[str]
    wager_amount: float
    total_pot: float
    settings: Dict[str, Any]
    created_at: datetime
    started_at: Optional[datetime]
    estimated_duration: Optional[int]  # in minutes

# Game Type Configuration
GAME_CONFIGS = {
    "chess": {
        "name": "Chess",
        "min_players": 2,
        "max_players": 2,
        "estimated_duration": 30,  # minutes
        "settings_model": ChessSettings,
        "default_settings": {"time_limit": 600, "variant": "standard"}
    },
    "checkers": {
        "name": "Checkers",
        "min_players": 2,
        "max_players": 2,
        "estimated_duration": 20,
        "settings_model": CheckersSettings,
        "default_settings": {"time_limit": 300, "variant": "american"}
    },
    "rock_paper_scissors": {
        "name": "Rock Paper Scissors",
        "min_players": 2,
        "max_players": 2,
        "estimated_duration": 5,
        "settings_model": RPSSettings,
        "default_settings": {"rounds": 3, "time_limit": 30}
    },
    "highlight_hero": {
        "name": "Highlight Hero",
        "min_players": 2,
        "max_players": 8,
        "estimated_duration": 15,
        "settings_model": HighlightHeroSettings,
        "default_settings": {"sport": "football", "rounds": 5}
    },
    "blur_detective": {
        "name": "Blur Detective",
        "min_players": 2,
        "max_players": 6,
        "estimated_duration": 10,
        "settings_model": BlurDetectiveSettings,
        "default_settings": {"category": "mixed", "blur_levels": 5}
    },
    "word_jumble": {
        "name": "Word Jumble",
        "min_players": 2,
        "max_players": 4,
        "estimated_duration": 12,
        "settings_model": WordJumbleSettings,
        "default_settings": {"difficulty": "medium", "word_count": 10}
    },
    "quiz_arena": {
        "name": "Quiz Arena",
        "min_players": 2,
        "max_players": 10,
        "estimated_duration": 15,
        "settings_model": QuizArenaSettings,
        "default_settings": {"category": "general", "question_count": 10}
    },
    "crazy_eights": {
        "name": "Crazy Eights",
        "min_players": 2,
        "max_players": 4,
        "estimated_duration": 20,
        "settings_model": CrazyEightsSettings,
        "default_settings": {"starting_cards": 7, "special_rules": True}
    }
}

def get_game_type_enum(game_type_str: str) -> GameType:
    """Convert string to GameType enum"""
    type_mapping = {
        "chess": GameType.CHESS,
        "checkers": GameType.CHECKERS,
        "rock_paper_scissors": GameType.ROCK_PAPER_SCISSORS,
        "highlight_hero": GameType.HIGHLIGHT_HERO,
        "blur_detective": GameType.BLUR_DETECTIVE,
        "word_jumble": GameType.WORD_JUMBLE,
        "quiz_arena": GameType.QUIZ_ARENA,
        "crazy_eights": GameType.CRAZY_EIGHTS
    }
    return type_mapping.get(game_type_str, GameType.CHESS)

def validate_game_settings(game_type: str, settings: Dict[str, Any]) -> Dict[str, Any]:
    """Validate and merge game settings with defaults"""
    if game_type not in GAME_CONFIGS:
        raise HTTPException(status_code=400, detail=f"Unsupported game type: {game_type}")

    config = GAME_CONFIGS[game_type]
    default_settings = config["default_settings"].copy()
    default_settings.update(settings)

    # Validate using Pydantic model
    settings_model = config["settings_model"]
    try:
        validated = settings_model(**default_settings)
        return validated.dict()
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Invalid settings: {str(e)}")

def generate_initial_game_state(game_type: str, settings: Dict[str, Any]) -> Dict[str, Any]:
    """Generate initial game state based on game type"""
    if game_type == "chess":
        return {
            "board": "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1",  # FEN notation
            "turn": "white",
            "move_history": [],
            "captured_pieces": {"white": [], "black": []},
            "check_status": None,
            "castling_rights": {"white": {"kingside": True, "queenside": True}, "black": {"kingside": True, "queenside": True}}
        }
    elif game_type == "checkers":
        return {
            "board": [[0 for _ in range(8)] for _ in range(8)],  # Will be populated with initial setup
            "turn": "red",
            "captured_pieces": {"red": [], "black": []},
            "kings": {"red": [], "black": []},
            "must_capture": None
        }
    elif game_type == "rock_paper_scissors":
        return {
            "current_round": 1,
            "max_rounds": settings.get("rounds", 3),
            "scores": {},
            "round_history": [],
            "player_moves": {}
        }
    elif game_type == "highlight_hero":
        return {
            "current_round": 1,
            "max_rounds": settings.get("rounds", 5),
            "scores": {},
            "current_highlight": None,
            "sport": settings.get("sport", "football"),
            "guesses": {}
        }
    elif game_type == "blur_detective":
        return {
            "current_image": None,
            "blur_level": settings.get("blur_levels", 5),
            "scores": {},
            "guesses": {},
            "category": settings.get("category", "mixed")
        }
    elif game_type == "word_jumble":
        return {
            "current_word": 1,
            "total_words": settings.get("word_count", 10),
            "scores": {},
            "current_jumble": None,
            "solved_words": [],
            "difficulty": settings.get("difficulty", "medium")
        }
    elif game_type == "quiz_arena":
        return {
            "current_question": 1,
            "total_questions": settings.get("question_count", 10),
            "scores": {},
            "current_question_data": None,
            "answers": {},
            "category": settings.get("category", "general")
        }
    elif game_type == "crazy_eights":
        return {
            "deck": [],  # Will be shuffled deck
            "hands": {},  # Player hands
            "discard_pile": [],
            "current_suit": None,
            "current_rank": None,
            "turn_order": [],
            "current_player": 0,
            "direction": 1  # 1 for clockwise, -1 for counter-clockwise
        }

    return {}

# API Endpoints
@router.get("/catalog")
async def get_game_catalog(
    category: Optional[str] = Query(None, description="Filter by game category"),
    active_only: bool = Query(True, description="Only return active games"),
    db: Session = Depends(get_db)
):
    """Get all available games from the catalog"""
    query = db.query(GameCatalog)

    if active_only:
        query = query.filter(GameCatalog.is_active == True)

    if category:
        query = query.filter(GameCatalog.category == category)

    games = query.order_by(GameCatalog.sort_order, GameCatalog.name).all()

    return {
        "games": [game.to_dict() for game in games],
        "total": len(games)
    }

@router.get("/catalog/{game_slug}")
async def get_game_details(
    game_slug: str,
    db: Session = Depends(get_db)
):
    """Get detailed information about a specific game"""
    game = db.query(GameCatalog).filter(GameCatalog.slug == game_slug).first()
    if not game:
        raise HTTPException(status_code=404, detail="Game not found")

    return game.to_dict()

@router.post("/create")
async def create_game_instance(
    game_slug: str = Field(..., description="Game slug from catalog"),
    wager_amount: float = Field(default=0.0, description="Wager amount"),
    settings: Dict[str, Any] = Field(default_factory=dict, description="Game settings"),
    invite_player_id: Optional[str] = Field(default=None, description="Player ID to invite"),
    is_private: bool = Field(default=False, description="Make game private"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Create a new game instance from catalog"""

    # Find the game in catalog
    game_catalog = db.query(GameCatalog).filter(GameCatalog.slug == game_slug).first()
    if not game_catalog:
        raise HTTPException(status_code=404, detail="Game not found in catalog")

    if not game_catalog.is_active:
        raise HTTPException(status_code=400, detail="Game is not currently available")

    # Validate wager amount
    if wager_amount < game_catalog.min_wager or wager_amount > game_catalog.max_wager:
        raise HTTPException(
            status_code=400,
            detail=f"Wager must be between ${game_catalog.min_wager} and ${game_catalog.max_wager}"
        )

    # Check if user has sufficient funds for wager
    if wager_amount > 0:
        wallet_service = WalletService(db)
        wallet = wallet_service.get_or_create_wallet(current_user)
        if wallet.balance < wager_amount:
            raise HTTPException(status_code=400, detail="Insufficient funds for wager")

    # Merge settings with defaults
    final_settings = game_catalog.default_settings.copy()
    final_settings.update(settings)
    final_settings["is_private"] = is_private

    # Create game instance
    game_instance = GameInstance(
        id=str(uuid.uuid4()),
        game_catalog_id=game_catalog.id,
        game_type=GameType(game_slug),
        status=GameStatus.WAITING,
        wager_amount=int(wager_amount * 100),  # Store as cents
        max_players=game_catalog.max_players,
        settings=final_settings,
        current_state=generate_initial_game_state(game_slug, final_settings),
        host_id=int(current_user.id) if current_user.id.isdigit() else None
    )

    # Add host as first player
    host_player = GamePlayer(
        game_id=game_instance.id,
        user_id=int(current_user.id) if current_user.id.isdigit() else None,
        position=0,
        is_ready=True
    )

    # Deduct wager from player's wallet
    if wager_amount > 0:
        wallet_service = WalletService(db)
        wallet_service.deduct_balance(current_user, wager_amount, f"Game wager for {game_catalog.name}")

    db.add(game_instance)
    db.add(host_player)
    db.commit()
    db.refresh(game_instance)

    return {
        "success": True,
        "game_instance_id": game_instance.id,
        "game_name": game_catalog.name,
        "status": game_instance.status.value,
        "wager_amount": wager_amount,
        "max_players": game_catalog.max_players,
        "estimated_duration": game_catalog.estimated_duration_minutes,
        "settings": final_settings
    }

@router.post("/join")
async def join_game(
    request: JoinGameRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Join an existing game"""

    # Find the game
    game = db.query(Game).filter(Game.id == request.game_id).first()
    if not game:
        raise HTTPException(status_code=404, detail="Game not found")

    # Check if game is joinable
    if game.status != GameStatus.WAITING:
        raise HTTPException(status_code=400, detail="Game is not accepting new players")

    if game.player1_id == current_user.id:
        raise HTTPException(status_code=400, detail="You cannot join your own game")

    if game.player2_id is not None:
        raise HTTPException(status_code=400, detail="Game is already full")

    # Check if user has sufficient funds for wager
    if game.wager_amount > 0:
        wallet_service = WalletService(db)
        wallet = wallet_service.get_or_create_wallet(current_user)
        if wallet.balance < game.wager_amount:
            raise HTTPException(status_code=400, detail="Insufficient funds for wager")

    # Join the game
    game.player2_id = current_user.id
    game.total_pot = game.wager_amount * 2
    game.status = GameStatus.IN_PROGRESS
    game.started_at = datetime.utcnow()

    # Deduct wager from player's wallet
    if game.wager_amount > 0:
        wallet_service.deduct_balance(current_user, game.wager_amount, f"Game wager for {game.game_type.value}")

    db.commit()

    return {
        "success": True,
        "message": "Successfully joined game",
        "game_id": game.id,
        "status": game.status.value
    }

@router.get("/available")
async def get_available_games(
    game_type: Optional[str] = Query(None, description="Filter by game type"),
    min_wager: Optional[float] = Query(None, description="Minimum wager amount"),
    max_wager: Optional[float] = Query(None, description="Maximum wager amount"),
    limit: int = Query(20, ge=1, le=100, description="Number of games to return"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get list of available games to join"""

    query = db.query(Game).filter(
        Game.status == GameStatus.WAITING,
        Game.player1_id != current_user.id,
        Game.is_private == False,
        Game.player2_id.is_(None)
    )

    if game_type:
        if game_type not in GAME_CONFIGS:
            raise HTTPException(status_code=400, detail=f"Invalid game type: {game_type}")
        query = query.filter(Game.game_type == get_game_type_enum(game_type))

    if min_wager is not None:
        query = query.filter(Game.wager_amount >= min_wager)

    if max_wager is not None:
        query = query.filter(Game.wager_amount <= max_wager)

    games = query.order_by(Game.created_at.desc()).limit(limit).all()

    return {
        "games": [
            {
                "id": game.id,
                "game_type": game.game_type.value,
                "player1_username": game.player1.username if game.player1 else None,
                "wager_amount": game.wager_amount,
                "total_pot": game.total_pot,
                "settings": game.settings,
                "created_at": game.created_at,
                "estimated_duration": GAME_CONFIGS.get(game.game_type.value, {}).get("estimated_duration", 30)
            }
            for game in games
        ]
    }

@router.get("/{game_id}")
async def get_game_details(
    game_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get detailed information about a specific game"""

    game = db.query(Game).filter(Game.id == game_id).first()
    if not game:
        raise HTTPException(status_code=404, detail="Game not found")

    # Check if user has access to view this game
    is_player = current_user.id in [game.player1_id, game.player2_id]
    is_public = not game.is_private

    if not (is_player or is_public):
        raise HTTPException(status_code=403, detail="Access denied to this game")

    return {
        "id": game.id,
        "game_type": game.game_type.value,
        "status": game.status.value,
        "player1": {
            "id": game.player1_id,
            "username": game.player1.username if game.player1 else None
        } if game.player1_id else None,
        "player2": {
            "id": game.player2_id,
            "username": game.player2.username if game.player2 else None
        } if game.player2_id else None,
        "wager_amount": game.wager_amount,
        "total_pot": game.total_pot,
        "settings": game.settings,
        "game_state": game.game_state if is_player else None,  # Only players can see game state
        "created_at": game.created_at,
        "started_at": game.started_at,
        "completed_at": game.completed_at,
        "winner_id": game.winner_id,
        "estimated_duration": GAME_CONFIGS.get(game.game_type.value, {}).get("estimated_duration", 30),
        "is_player": is_player
    }

@router.delete("/{game_id}")
async def cancel_game(
    game_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Cancel a game (only creator can cancel waiting games)"""

    game = db.query(Game).filter(Game.id == game_id).first()
    if not game:
        raise HTTPException(status_code=404, detail="Game not found")

    if game.player1_id != current_user.id:
        raise HTTPException(status_code=403, detail="Only the game creator can cancel the game")

    if game.status != GameStatus.WAITING:
        raise HTTPException(status_code=400, detail="Can only cancel games that are waiting for players")

    # Refund wager to creator
    if game.wager_amount > 0:
        wallet_service = WalletService(db)
        wallet_service.add_balance(current_user, game.wager_amount, f"Refund for cancelled {game.game_type.value} game")

    # Cancel the game
    game.status = GameStatus.CANCELLED
    game.completed_at = datetime.utcnow()

    db.commit()

    return {
        "success": True,
        "message": "Game cancelled successfully",
        "refund_amount": game.wager_amount
    }
