"""WebSocket event handlers for real-time game communication."""
from typing import Dict, Any, Optional
import logging
from datetime import datetime
from sqlalchemy.orm import Session

from app.core.websocket import sio, manager, broadcast_game_update, send_error
from app.core.dependencies import get_db, verify_websocket_token
from app.models.game import Game, GameStatus
from app.models.user import User
from app.services.game_service import GameService
from app.services.wallet import WalletService

logger = logging.getLogger(__name__)


@sio.event
async def connect(sid: str, environ: dict, auth: Optional[Dict[str, Any]] = None):
    """Handle new WebSocket connection."""
    try:
        # Verify authentication
        if not auth or 'token' not in auth:
            logger.warning(f"Connection attempt without auth token from {sid}")
            await sio.disconnect(sid)
            return False

        # Verify token and get user
        user = await verify_websocket_token(auth['token'])
        if not user:
            logger.warning(f"Invalid auth token from {sid}")
            await sio.disconnect(sid)
            return False

        # Register connection
        await manager.connect(sid, user.id)

        # Send connection confirmation
        await sio.emit('connected', {
            'user_id': user.id,
            'username': user.username,
            'timestamp': datetime.utcnow().isoformat()
        }, room=sid)

        logger.info(f"User {user.username} connected via WebSocket")
        return True

    except Exception as e:
        logger.error(f"Connection error: {e}")
        await sio.disconnect(sid)
        return False


@sio.event
async def disconnect(sid: str):
    """Handle WebSocket disconnection."""
    user_id = manager.get_user_id(sid)
    if user_id:
        logger.info(f"User {user_id} disconnected")

    await manager.disconnect(sid)


@sio.event
async def join_game(sid: str, data: Dict[str, Any]):
    """Join a game room."""
    try:
        game_id = data.get('game_id')
        user_id = manager.get_user_id(sid)

        if not game_id or not user_id:
            await send_error(sid, "Invalid request")
            return {'success': False, 'error': 'Invalid request'}

        # Verify user is part of the game
        # TODO: Add database check

        # Join game room
        await manager.join_game_room(sid, game_id)

        # Notify other players
        await sio.emit('player_joined', {
            'user_id': user_id,
            'game_id': game_id,
            'timestamp': datetime.utcnow().isoformat()
        }, room=f"game_{game_id}", skip_sid=sid)

        return {'success': True}

    except Exception as e:
        logger.error(f"Error joining game: {e}")
        await send_error(sid, "Failed to join game")
        return {'success': False, 'error': str(e)}


@sio.event
async def leave_game(sid: str, data: Dict[str, Any]):
    """Leave a game room."""
    try:
        game_id = data.get('game_id')
        user_id = manager.get_user_id(sid)

        if not game_id or not user_id:
            return {'success': False, 'error': 'Invalid request'}

        # Leave game room
        await manager.leave_game_room(sid, game_id)

        # Notify other players
        await sio.emit('player_left', {
            'user_id': user_id,
            'game_id': game_id,
            'timestamp': datetime.utcnow().isoformat()
        }, room=f"game_{game_id}")

        return {'success': True}

    except Exception as e:
        logger.error(f"Error leaving game: {e}")
        return {'success': False, 'error': str(e)}


@sio.event
async def game_move(sid: str, data: Dict[str, Any]):
    """Process a game move."""
    try:
        game_id = data.get('game_id')
        move_data = data.get('move')
        user_id = manager.get_user_id(sid)

        if not all([game_id, move_data, user_id]):
            await send_error(sid, "Invalid move data")
            return {'success': False, 'error': 'Invalid move data'}

        # Process move through game service
        from app.database.db import SessionLocal
        db = SessionLocal()
        try:
            game_service = GameService(db)
            result = await game_service.process_move(
                game_id=game_id,
                user_id=user_id,
                move_data=move_data
            )
        finally:
            db.close()

        if result['valid']:
            # Broadcast validated move to all players
            await broadcast_game_update(game_id, {
                'type': 'move',
                'player_id': user_id,
                'move': move_data,
                'game_state': result['game_state'],
                'timestamp': datetime.utcnow().isoformat()
            })

            # Check for game end
            if result.get('game_ended'):
                await handle_game_end(game_id, result)

            return {'success': True}
        else:
            await send_error(sid, result.get('error', 'Invalid move'))
            return {'success': False, 'error': result.get('error')}

    except Exception as e:
        logger.error(f"Error processing move: {e}")
        await send_error(sid, "Failed to process move")
        return {'success': False, 'error': str(e)}


@sio.event
async def chat_message(sid: str, data: Dict[str, Any]):
    """Send chat message in game."""
    try:
        game_id = data.get('game_id')
        message = data.get('message')
        user_id = manager.get_user_id(sid)

        if not all([game_id, message, user_id]):
            return {'success': False, 'error': 'Invalid message'}

        # TODO: Add chat validation and filtering

        # Broadcast message to game room
        await sio.emit('chat_message', {
            'game_id': game_id,
            'user_id': user_id,
            'message': message,
            'timestamp': datetime.utcnow().isoformat()
        }, room=f"game_{game_id}")

        return {'success': True}

    except Exception as e:
        logger.error(f"Error sending chat message: {e}")
        return {'success': False, 'error': str(e)}


@sio.event
async def spectate_game(sid: str, data: Dict[str, Any]):
    """Join game as spectator."""
    try:
        game_id = data.get('game_id')

        if not game_id:
            await send_error(sid, "Invalid game ID")
            return {'success': False}

        # Join spectator room
        await sio.enter_room(sid, f"spectate_{game_id}")

        # TODO: Send current game state to spectator

        return {'success': True}

    except Exception as e:
        logger.error(f"Error joining as spectator: {e}")
        return {'success': False, 'error': str(e)}


@sio.event
async def place_live_bet(sid: str, data: Dict[str, Any]):
    """Place a live bet on ongoing game."""
    try:
        game_id = data.get('game_id')
        bet_data = data.get('bet')
        user_id = manager.get_user_id(sid)

        if not all([game_id, bet_data, user_id]):
            await send_error(sid, "Invalid bet data")
            return {'success': False}

        # TODO: Implement live betting service

        # For now, just acknowledge
        return {'success': True, 'bet_id': 'mock_bet_id'}

    except Exception as e:
        logger.error(f"Error placing bet: {e}")
        return {'success': False, 'error': str(e)}


async def handle_game_end(game_id: str, result: Dict[str, Any]):
    """Handle game end events."""
    try:
        # Broadcast game end to all players and spectators
        end_data = {
            'game_id': game_id,
            'winner_id': result.get('winner_id'),
            'final_state': result.get('game_state'),
            'timestamp': datetime.utcnow().isoformat()
        }

        await broadcast_game_update(game_id, {
            'type': 'game_end',
            **end_data
        })

        # Notify spectators
        await sio.emit('game_ended', end_data, room=f"spectate_{game_id}")

        # TODO: Process payouts, update ratings, etc.

    except Exception as e:
        logger.error(f"Error handling game end: {e}")


# Utility functions for testing
async def emit_test_event(event: str, data: dict, room: Optional[str] = None):
    """Emit test event for debugging."""
    if room:
        await sio.emit(event, data, room=room)
    else:
        await sio.emit(event, data)