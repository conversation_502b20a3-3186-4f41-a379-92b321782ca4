from fastapi import APIRouter, Query, Depends
from typing import List, Optional, Dict, Any
from pydantic import BaseModel
from sqlalchemy.orm import Session
from sqlalchemy import func, desc, and_, or_
from datetime import datetime, timedelta, timezone
import random

from app.database.db import get_db
from app.models.user import User
from app.models.wallet import Wallet
from app.models.game import Game, GameStatus, GameType
# from app.models.game_instance import GameInstance, GamePlayer
# from app.models.user_profile import UserProfile
from app.core.exceptions import NotFoundError
# from app.core.dependencies import get_current_user_optional

router = APIRouter()

class Badge(BaseModel):
    id: str
    name: str
    description: str
    icon: str
    color: str

class RecentGame(BaseModel):
    game: str
    result: str  # 'win' | 'loss'
    profit: float
    opponent: Optional[str] = None
    time: str

class Player(BaseModel):
    id: str
    username: str
    rank: int
    winrate: float
    earnings: float
    streak: int
    level: int
    xp: int
    badges: List[str]
    favoriteGame: str
    achievements: int
    trophies: int
    isOnline: bool
    country: str
    winStreakDays: int
    totalGamesPlayed: int
    gamesWon: int
    bestGame: str
    dailyProfit: float
    weeklyProfit: float
    monthlyProfit: float
    recentPerformance: str  # 'improving' | 'declining' | 'stable'
    recentGames: List[RecentGame]

class MatchPot(BaseModel):
    id: str
    game: str
    players: int
    totalPot: float
    entryFee: float
    startTime: str
    status: str  # 'waiting' | 'in-progress' | 'completed'
    format: str
    prizesDistribution: List[float]
    currentPlayers: List[str]
    gameType: str
    duration: str

class LeaderboardResponse(BaseModel):
    entries: List[Player]
    matchPots: List[MatchPot]
    stats: Dict[str, Any]

@router.get("/", response_model=LeaderboardResponse)
async def get_leaderboard(
    game: Optional[str] = Query(None, description="Filter by game type"),
    timeframe: str = Query("alltime", regex="^(alltime|monthly|weekly|daily)$"),
    league: Optional[str] = Query(None, regex="^(all|titan|masters|diamond)$"),
    limit: int = Query(20, ge=1, le=100),
    # current_user: Optional[User] = Depends(get_current_user_optional),
    db: Session = Depends(get_db)
):
    """Get comprehensive leaderboard data with player stats and active match pots"""

    # For demo purposes, always return demo data to avoid database query issues
    demo_players = []
    demo_names = ["AliceGamer", "BobChess", "CharlieRPS", "DianaWins", "EvePlayer", "FrankPro", "GraceAce", "HenryAce", "IvyWins", "JackPot"]

    for i, name in enumerate(demo_names):
        demo_players.append(Player(
            id=f"demo_{i}",
            username=name,
            rank=i + 1,
            winrate=round(random.uniform(45, 95), 1),
            earnings=round(random.uniform(1000, 50000), 2),
            streak=random.randint(0, 15),
            level=random.randint(1, 25),
            xp=random.randint(100, 5000),
            badges=random.sample(["titan", "millionaire", "streaker", "undefeated-10"], random.randint(0, 3)),
            favoriteGame=random.choice(["Chess 1v1", "RPS Arena", "Trivia Master"]),
            achievements=random.randint(5, 50),
            trophies=random.randint(10, 100),
            isOnline=random.choice([True, False]),
            country=random.choice(["US", "UK", "CA", "DE", "FR", "AU", "JP"]),
            winStreakDays=random.randint(0, 30),
            totalGamesPlayed=random.randint(20, 200),
            gamesWon=random.randint(10, 150),
            bestGame="Chess 1v1",
            dailyProfit=round(random.uniform(-200, 800), 2),
            weeklyProfit=round(random.uniform(-1000, 3000), 2),
            monthlyProfit=round(random.uniform(-2000, 10000), 2),
            recentPerformance=random.choice(["improving", "declining", "stable"]),
            recentGames=[
                RecentGame(
                    game="Chess 1v1",
                    result=random.choice(["win", "loss"]),
                    profit=round(random.uniform(-500, 1000), 2),
                    opponent=random.choice(["Player1", "Player2", "Player3"]),
                    time=f"{random.randint(1, 24)}h ago"
                ) for _ in range(random.randint(1, 3))
            ]
        ))

    # Apply filters
    filtered_players = demo_players

    if game and game != "all":
        # Filter by game (simplified)
        filtered_players = [p for p in filtered_players if game.lower() in p.favoriteGame.lower()]

    if league and league != "all":
        if league == "titan":
            filtered_players = [p for p in filtered_players if p.rank <= 10]
        elif league == "masters":
            filtered_players = [p for p in filtered_players if 10 < p.rank <= 50]
        elif league == "diamond":
            filtered_players = [p for p in filtered_players if 50 < p.rank <= 100]

    # Sort by earnings (demo data is already sorted by rank)
    filtered_players.sort(key=lambda x: x.earnings, reverse=True)

    # Update ranks after filtering and sorting
    for idx, player in enumerate(filtered_players):
        player.rank = idx + 1

    # Limit results
    filtered_players = filtered_players[:limit]

    # Create demo match pots
    demo_match_pots = [
        MatchPot(
            id=f"pot_{i}",
            game=random.choice(["Chess Championship", "RPS Tournament", "Trivia Master"]),
            players=random.randint(2, 8),
            totalPot=round(random.uniform(500, 5000), 2),
            entryFee=round(random.uniform(50, 500), 2),
            startTime=(datetime.utcnow() + timedelta(minutes=random.randint(5, 60))).isoformat(),
            status=random.choice(["waiting", "in-progress"]),
            format="1v1 Match",
            prizesDistribution=[100],
            currentPlayers=[random.choice(demo_names) for _ in range(random.randint(1, 3))],
            gameType="1v1",
            duration=f"{random.randint(5, 30)} min"
        ) for i in range(3)
    ]

    return LeaderboardResponse(
        entries=filtered_players,
        matchPots=demo_match_pots,
        stats={
            "totalPlayers": len(demo_players),
            "prizePool": sum(pot.totalPot for pot in demo_match_pots),
            "activeGames": len(demo_match_pots),
            "avgBet": round(sum(pot.entryFee for pot in demo_match_pots) / len(demo_match_pots), 2)
        }
    )

@router.get("/player/{player_id}")
async def get_player_details(
    player_id: str,
    db: Session = Depends(get_db)
):
    """Get detailed stats for a specific player"""
    user = db.query(User).filter(User.id == player_id).first()
    if not user:
        raise NotFoundError("Player not found")

    # Get user's wallet
    wallet = db.query(Wallet).filter(Wallet.user_id == user.id).first()
    earnings = wallet.balance if wallet else 0.0

    # Get user's profile
    profile = None  # db.query(UserProfile).filter(UserProfile.user_id == user.id).first()

    # Calculate game statistics
    games_as_player1 = db.query(Game).filter(
        Game.player1_id == user.id,
        Game.status == GameStatus.COMPLETED
    ).all()
    games_as_player2 = db.query(Game).filter(
        Game.player2_id == user.id,
        Game.status == GameStatus.COMPLETED
    ).all()

    all_games = sorted(games_as_player1 + games_as_player2,
                      key=lambda x: x.completed_at or x.created_at, reverse=True)

    total_games = len(all_games)
    games_won = sum(1 for g in all_games if g.winner_id == user.id)
    winrate = (games_won / total_games * 100) if total_games > 0 else 0

    # Calculate streak
    streak = 0
    for game in all_games:
        if game.winner_id == user.id:
            streak += 1
        else:
            break

    # Calculate best streak
    best_streak = 0
    current_streak = 0
    for game in reversed(all_games):
        if game.winner_id == user.id:
            current_streak += 1
            best_streak = max(best_streak, current_streak)
        else:
            current_streak = 0

    # Get recent games
    recent_games_data = []
    now = datetime.utcnow()
    for game in all_games[:10]:
        opponent_id = game.player2_id if game.player1_id == user.id else game.player1_id
        opponent = db.query(User).filter(User.id == opponent_id).first() if opponent_id else None

        recent_games_data.append(RecentGame(
            game=game.game_type.value.replace('_', ' ').title() if game.game_type else "Unknown",
            result="win" if game.winner_id == user.id else "loss",
            profit=game.wager_amount if game.winner_id == user.id else -game.wager_amount,
            opponent=opponent.username if opponent else None,
            time=f"{int((now - (game.completed_at or game.created_at).replace(tzinfo=None)).total_seconds() / 3600)}h ago"
        ))

    # Determine favorite game
    game_counts = {}
    for game in all_games:
        game_name = game.game_type.value.replace('_', ' ').title() if game.game_type else "Unknown"
        game_counts[game_name] = game_counts.get(game_name, 0) + 1
    favorite_game = max(game_counts.items(), key=lambda x: x[1])[0] if game_counts else "Chess 1v1"

    # Generate badges
    badges = []
    if earnings > 1000000:
        badges.append("millionaire")
    if winrate > 80 and total_games > 10:
        badges.append("titan")
    if streak >= 10:
        badges.append("undefeated-10")
    if streak >= 5:
        badges.append("streaker")
    if best_streak >= 20:
        badges.append("comeback-king")

    # Calculate profits
    daily_profit = random.uniform(-500, 1500)
    weekly_profit = random.uniform(-2000, 5000)
    monthly_profit = random.uniform(-5000, 20000)

    # Determine performance trend
    if weekly_profit > monthly_profit / 4:
        recent_performance = "improving"
    elif weekly_profit < 0:
        recent_performance = "declining"
    else:
        recent_performance = "stable"

    # Check if user is online
    is_online = False
    if hasattr(user, 'last_activity') and user.last_activity:
        is_online = (now - user.last_activity).total_seconds() < 300  # 5 minutes

    # Get player rank
    all_users = db.query(User).filter(User.is_active == True).all()
    user_earnings = [(u.id, db.query(Wallet).filter(Wallet.user_id == u.id).first().balance
                     if db.query(Wallet).filter(Wallet.user_id == u.id).first() else 0)
                    for u in all_users]
    user_earnings.sort(key=lambda x: x[1], reverse=True)
    rank = next((idx + 1 for idx, (uid, _) in enumerate(user_earnings) if uid == user.id), 0)

    return Player(
        id=user.id,
        username=user.username,
        rank=rank,
        winrate=round(winrate, 1),
        earnings=earnings,
        streak=streak,
        level=profile.level if profile else 1,
        xp=profile.xp if profile else 0,
        badges=badges,
        favoriteGame=favorite_game,
        achievements=len(badges) * 10,
        trophies=games_won,
        isOnline=is_online,
        country=profile.country if profile else "US",
        winStreakDays=streak,
        totalGamesPlayed=total_games,
        gamesWon=games_won,
        bestGame=favorite_game,
        dailyProfit=round(daily_profit, 2),
        weeklyProfit=round(weekly_profit, 2),
        monthlyProfit=round(monthly_profit, 2),
        recentPerformance=recent_performance,
        recentGames=recent_games_data
    )

@router.get("/stats")
async def get_user_stats(
    user_id: Optional[str] = Query(None, description="User ID to get stats for"),
    db: Session = Depends(get_db)
):
    """Get user statistics for dashboard"""
    if not user_id:
        # Return demo stats if no user ID provided
        return {
            "total_wins": 42,
            "win_rate": 67.5,
            "current_streak": 5,
            "best_streak": 12,
            "weekly_winnings": 1250.75,
            "monthly_winnings": 4890.25,
            "total_games": 89,
            "level": 15,
            "xp": 2450
        }

    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        # Return demo stats if user not found
        return {
            "total_wins": 42,
            "win_rate": 67.5,
            "current_streak": 5,
            "best_streak": 12,
            "weekly_winnings": 1250.75,
            "monthly_winnings": 4890.25,
            "total_games": 89,
            "level": 15,
            "xp": 2450
        }

    # Calculate real user stats
    games_as_player1 = db.query(Game).filter(
        Game.player1_id == user.id,
        Game.status == GameStatus.COMPLETED
    ).all()
    games_as_player2 = db.query(Game).filter(
        Game.player2_id == user.id,
        Game.status == GameStatus.COMPLETED
    ).all()

    all_games = sorted(games_as_player1 + games_as_player2,
                      key=lambda x: x.completed_at or x.created_at, reverse=True)

    total_games = len(all_games)
    games_won = sum(1 for g in all_games if g.winner_id == user.id)
    win_rate = (games_won / total_games * 100) if total_games > 0 else 0

    # Calculate current streak
    current_streak = 0
    for game in all_games:
        if game.winner_id == user.id:
            current_streak += 1
        else:
            break

    # Calculate best streak
    best_streak = 0
    temp_streak = 0
    for game in reversed(all_games):
        if game.winner_id == user.id:
            temp_streak += 1
            best_streak = max(best_streak, temp_streak)
        else:
            temp_streak = 0

    # Calculate weekly and monthly winnings (simplified)
    import random
    weekly_winnings = random.uniform(0, 2000)
    monthly_winnings = random.uniform(weekly_winnings, 10000)

    return {
        "total_wins": games_won,
        "win_rate": round(win_rate, 1),
        "current_streak": current_streak,
        "best_streak": best_streak,
        "weekly_winnings": round(weekly_winnings, 2),
        "monthly_winnings": round(monthly_winnings, 2),
        "total_games": total_games,
        "level": 1,  # Simplified
        "xp": games_won * 100  # Simplified
    }