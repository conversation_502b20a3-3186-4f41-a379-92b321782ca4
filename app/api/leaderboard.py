from fastapi import APIRouter, Query, Depends, HTTPException
from typing import List, Optional, Dict, Any
from pydantic import BaseModel
from sqlalchemy.orm import Session
from sqlalchemy import func, desc, and_, or_
from datetime import datetime, timedelta, timezone
import random

from app.database.db import get_db
from app.models.user import User
from app.models.wallet import Wallet
from app.models.game import Game, GameStatus, GameType
from app.models.watchlist import Watchlist
# from app.models.game_instance import GameInstance, GamePlayer
# from app.models.user_profile import UserProfile
from app.core.exceptions import NotFoundError
from app.core.dependencies import get_current_user, get_current_user_optional

router = APIRouter()

class Badge(BaseModel):
    id: str
    name: str
    description: str
    icon: str
    color: str

class RecentGame(BaseModel):
    game: str
    result: str  # 'win' | 'loss'
    profit: float
    opponent: Optional[str] = None
    time: str

class Player(BaseModel):
    id: str
    username: str
    rank: int
    winrate: float
    earnings: float
    streak: int
    level: int
    xp: int
    badges: List[str]
    favoriteGame: str
    achievements: int
    trophies: int
    isOnline: bool
    country: str
    winStreakDays: int
    totalGamesPlayed: int
    gamesWon: int
    bestGame: str
    dailyProfit: float
    weeklyProfit: float
    monthlyProfit: float
    recentPerformance: str  # 'improving' | 'declining' | 'stable'
    recentGames: List[RecentGame]

class MatchPot(BaseModel):
    id: str
    game: str
    players: int
    totalPot: float
    entryFee: float
    startTime: str
    status: str  # 'waiting' | 'in-progress' | 'completed'
    format: str
    prizesDistribution: List[float]
    currentPlayers: List[str]
    gameType: str
    duration: str

class LeaderboardResponse(BaseModel):
    entries: List[Player]
    matchPots: List[MatchPot]
    stats: Dict[str, Any]

@router.get("/", response_model=LeaderboardResponse)
async def get_leaderboard(
    game: Optional[str] = Query(None, description="Filter by game type"),
    timeframe: str = Query("alltime", regex="^(alltime|monthly|weekly|daily)$"),
    league: Optional[str] = Query(None, regex="^(all|titan|masters|diamond)$"),
    limit: int = Query(20, ge=1, le=100),
    # current_user: Optional[User] = Depends(get_current_user_optional),
    db: Session = Depends(get_db)
):
    """Get comprehensive leaderboard data with player stats and active match pots"""

    # Get time filter
    time_filter = None
    now = datetime.utcnow()
    if timeframe == "daily":
        time_filter = now - timedelta(days=1)
    elif timeframe == "weekly":
        time_filter = now - timedelta(days=7)
    elif timeframe == "monthly":
        time_filter = now - timedelta(days=30)

    # Build base query for users with their game statistics
    users_query = db.query(User).filter(User.is_active == True)

    # Get all users with their stats
    users = users_query.all()

    players = []
    for idx, user in enumerate(users):
        # Get user's wallet
        wallet = db.query(Wallet).filter(Wallet.user_id == user.id).first()
        earnings = wallet.balance if wallet else 0.0

        # Get user's profile
        profile = None  # db.query(UserProfile).filter(UserProfile.user_id == user.id).first()

        # Calculate game statistics
        games_as_player1 = db.query(Game).filter(
            Game.player1_id == user.id,
            Game.status == GameStatus.COMPLETED
        )
        games_as_player2 = db.query(Game).filter(
            Game.player2_id == user.id,
            Game.status == GameStatus.COMPLETED
        )

        if time_filter:
            games_as_player1 = games_as_player1.filter(Game.completed_at >= time_filter)
            games_as_player2 = games_as_player2.filter(Game.completed_at >= time_filter)

        # Apply game filter if specified
        if game:
            game_type_map = {
                'Chess 1v1': GameType.CHESS,
                'RPS Arena': GameType.ROCK_PAPER_SCISSORS,
                'Checkers Elite': GameType.CHECKERS,
                'Highlight Hero': GameType.HIGHLIGHT_HERO,
                'Blur Detective': GameType.BLUR_DETECTIVE
            }
            if game in game_type_map:
                games_as_player1 = games_as_player1.filter(Game.game_type == game_type_map[game])
                games_as_player2 = games_as_player2.filter(Game.game_type == game_type_map[game])

        games_p1 = games_as_player1.all()
        games_p2 = games_as_player2.all()

        total_games = len(games_p1) + len(games_p2)
        games_won = sum(1 for g in games_p1 if g.winner_id == user.id) + \
                   sum(1 for g in games_p2 if g.winner_id == user.id)

        winrate = (games_won / total_games * 100) if total_games > 0 else 0

        # Calculate streak (simplified - count consecutive wins)
        all_games = sorted(games_p1 + games_p2, key=lambda x: x.completed_at or x.created_at, reverse=True)
        streak = 0
        for game_obj in all_games:
            if game_obj.winner_id == user.id:
                streak += 1
            else:
                break

        # Calculate profits (simplified)
        daily_profit = random.uniform(-500, 1500)
        weekly_profit = random.uniform(-2000, 5000)
        monthly_profit = random.uniform(-5000, 20000)

        # Determine performance trend
        if weekly_profit > monthly_profit / 4:
            recent_performance = "improving"
        elif weekly_profit < 0:
            recent_performance = "declining"
        else:
            recent_performance = "stable"

        # Get recent games
        recent_games_data = []
        for game_obj in all_games[:5]:
            opponent_id = game_obj.player2_id if game_obj.player1_id == user.id else game_obj.player1_id
            opponent = db.query(User).filter(User.id == opponent_id).first() if opponent_id else None

            recent_games_data.append(RecentGame(
                game=game_obj.game_type.value.replace('_', ' ').title() if game_obj.game_type else "Unknown",
                result="win" if game_obj.winner_id == user.id else "loss",
                profit=game_obj.wager_amount if game_obj.winner_id == user.id else -game_obj.wager_amount,
                opponent=opponent.username if opponent else None,
                time=f"{int((now - (game_obj.completed_at or game_obj.created_at).replace(tzinfo=None)).total_seconds() / 3600)}h ago"
            ))

        # Determine favorite game
        game_counts = {}
        for game_obj in all_games:
            game_name = game_obj.game_type.value.replace('_', ' ').title() if game_obj.game_type else "Unknown"
            game_counts[game_name] = game_counts.get(game_name, 0) + 1
        favorite_game = max(game_counts.items(), key=lambda x: x[1])[0] if game_counts else "Chess 1v1"

        # Generate badges based on achievements
        badges = []
        if earnings > 1000000:
            badges.append("millionaire")
        if winrate > 80 and total_games > 10:
            badges.append("titan")
        if streak >= 10:
            badges.append("undefeated-10")
        if streak >= 5:
            badges.append("streaker")

        # Check if user is online (simplified - check last activity)
        is_online = False
        if hasattr(user, 'last_activity') and user.last_activity:
            is_online = (now - user.last_activity).total_seconds() < 300  # 5 minutes

        player = Player(
            id=user.id,
            username=user.username,
            rank=idx + 1,  # Will be re-sorted later
            winrate=round(winrate, 1),
            earnings=earnings,
            streak=streak,
            level=profile.level if profile else 1,
            xp=profile.xp if profile else 0,
            badges=badges,
            favoriteGame=favorite_game,
            achievements=len(badges) * 10,  # Simplified
            trophies=games_won,
            isOnline=is_online,
            country=profile.country if profile else "US",
            winStreakDays=streak,  # Simplified
            totalGamesPlayed=total_games,
            gamesWon=games_won,
            bestGame=favorite_game,
            dailyProfit=round(daily_profit, 2),
            weeklyProfit=round(weekly_profit, 2),
            monthlyProfit=round(monthly_profit, 2),
            recentPerformance=recent_performance,
            recentGames=recent_games_data
        )
        players.append(player)

    # Sort players by earnings
    players.sort(key=lambda x: x.earnings, reverse=True)

    # Apply league filter
    if league and league != "all":
        if league == "titan":
            players = [p for p in players if p.rank <= 10]
        elif league == "masters":
            players = [p for p in players if 10 < p.rank <= 50]
        elif league == "diamond":
            players = [p for p in players if 50 < p.rank <= 100]

    # Update ranks after filtering
    for idx, player in enumerate(players):
        player.rank = idx + 1

    # Limit results
    players = players[:limit]

    # Get active match pots (games waiting for players)
    match_pots = []
    active_games = db.query(Game).filter(
        Game.status == GameStatus.WAITING,
        Game.wager_amount > 0
    ).order_by(desc(Game.total_pot)).limit(10).all()

    for game_obj in active_games:
        game_type_names = {
            GameType.CHESS: "Chess Championship",
            GameType.ROCK_PAPER_SCISSORS: "RPS Tournament",
            GameType.CHECKERS: "Checkers Masters",
            GameType.HIGHLIGHT_HERO: "Highlight Hero Challenge",
            GameType.BLUR_DETECTIVE: "Blur Detective Mystery"
        }

        # Get current players
        current_players = []
        if game_obj.player1:
            current_players.append(game_obj.player1.username)
        if game_obj.player2:
            current_players.append(game_obj.player2.username)

        match_pot = MatchPot(
            id=game_obj.id,
            game=game_type_names.get(game_obj.game_type, "Unknown Game"),
            players=game_obj.max_players or 2,
            totalPot=game_obj.total_pot or (game_obj.wager_amount * 2),
            entryFee=game_obj.wager_amount,
            startTime=(game_obj.created_at + timedelta(minutes=5)).isoformat(),
            status="waiting" if game_obj.status == GameStatus.WAITING else "in-progress",
            format="1v1 Match",
            prizesDistribution=[100],  # Winner takes all
            currentPlayers=current_players,
            gameType="1v1",
            duration="10 min"
        )
        match_pots.append(match_pot)

    # Calculate global stats
    total_players = len(users)
    total_prize_pool = sum(game_obj.total_pot or 0 for game_obj in active_games)
    active_games_count = db.query(Game).filter(
        Game.status.in_([GameStatus.WAITING, GameStatus.IN_PROGRESS])
    ).count()

    stats = {
        "totalPlayers": total_players,
        "prizePool": round(total_prize_pool, 2),
        "activeGames": active_games_count,
        "avgBet": round(total_prize_pool / active_games_count, 2) if active_games_count > 0 else 0
    }

    return LeaderboardResponse(
        entries=players,
        matchPots=match_pots,
        stats=stats
    )

@router.get("/player/{player_id}")
async def get_player_details(
    player_id: str,
    db: Session = Depends(get_db)
):
    """Get detailed stats for a specific player"""
    user = db.query(User).filter(User.id == player_id).first()
    if not user:
        raise NotFoundError("Player not found")

    # Get user's wallet
    wallet = db.query(Wallet).filter(Wallet.user_id == user.id).first()
    earnings = wallet.balance if wallet else 0.0

    # Get user's profile
    profile = None  # db.query(UserProfile).filter(UserProfile.user_id == user.id).first()

    # Calculate game statistics
    games_as_player1 = db.query(Game).filter(
        Game.player1_id == user.id,
        Game.status == GameStatus.COMPLETED
    ).all()
    games_as_player2 = db.query(Game).filter(
        Game.player2_id == user.id,
        Game.status == GameStatus.COMPLETED
    ).all()

    all_games = sorted(games_as_player1 + games_as_player2,
                      key=lambda x: x.completed_at or x.created_at, reverse=True)

    total_games = len(all_games)
    games_won = sum(1 for g in all_games if g.winner_id == user.id)
    winrate = (games_won / total_games * 100) if total_games > 0 else 0

    # Calculate streak
    streak = 0
    for game in all_games:
        if game.winner_id == user.id:
            streak += 1
        else:
            break

    # Calculate best streak
    best_streak = 0
    current_streak = 0
    for game in reversed(all_games):
        if game.winner_id == user.id:
            current_streak += 1
            best_streak = max(best_streak, current_streak)
        else:
            current_streak = 0

    # Get recent games
    recent_games_data = []
    now = datetime.utcnow()
    for game in all_games[:10]:
        opponent_id = game.player2_id if game.player1_id == user.id else game.player1_id
        opponent = db.query(User).filter(User.id == opponent_id).first() if opponent_id else None

        recent_games_data.append(RecentGame(
            game=game.game_type.value.replace('_', ' ').title() if game.game_type else "Unknown",
            result="win" if game.winner_id == user.id else "loss",
            profit=game.wager_amount if game.winner_id == user.id else -game.wager_amount,
            opponent=opponent.username if opponent else None,
            time=f"{int((now - (game.completed_at or game.created_at).replace(tzinfo=None)).total_seconds() / 3600)}h ago"
        ))

    # Determine favorite game
    game_counts = {}
    for game in all_games:
        game_name = game.game_type.value.replace('_', ' ').title() if game.game_type else "Unknown"
        game_counts[game_name] = game_counts.get(game_name, 0) + 1
    favorite_game = max(game_counts.items(), key=lambda x: x[1])[0] if game_counts else "Chess 1v1"

    # Generate badges
    badges = []
    if earnings > 1000000:
        badges.append("millionaire")
    if winrate > 80 and total_games > 10:
        badges.append("titan")
    if streak >= 10:
        badges.append("undefeated-10")
    if streak >= 5:
        badges.append("streaker")
    if best_streak >= 20:
        badges.append("comeback-king")

    # Calculate profits
    daily_profit = random.uniform(-500, 1500)
    weekly_profit = random.uniform(-2000, 5000)
    monthly_profit = random.uniform(-5000, 20000)

    # Determine performance trend
    if weekly_profit > monthly_profit / 4:
        recent_performance = "improving"
    elif weekly_profit < 0:
        recent_performance = "declining"
    else:
        recent_performance = "stable"

    # Check if user is online
    is_online = False
    if hasattr(user, 'last_activity') and user.last_activity:
        is_online = (now - user.last_activity).total_seconds() < 300  # 5 minutes

    # Get player rank
    all_users = db.query(User).filter(User.is_active == True).all()
    user_earnings = [(u.id, db.query(Wallet).filter(Wallet.user_id == u.id).first().balance
                     if db.query(Wallet).filter(Wallet.user_id == u.id).first() else 0)
                    for u in all_users]
    user_earnings.sort(key=lambda x: x[1], reverse=True)
    rank = next((idx + 1 for idx, (uid, _) in enumerate(user_earnings) if uid == user.id), 0)

    return Player(
        id=user.id,
        username=user.username,
        rank=rank,
        winrate=round(winrate, 1),
        earnings=earnings,
        streak=streak,
        level=profile.level if profile else 1,
        xp=profile.xp if profile else 0,
        badges=badges,
        favoriteGame=favorite_game,
        achievements=len(badges) * 10,
        trophies=games_won,
        isOnline=is_online,
        country=profile.country if profile else "US",
        winStreakDays=streak,
        totalGamesPlayed=total_games,
        gamesWon=games_won,
        bestGame=favorite_game,
        dailyProfit=round(daily_profit, 2),
        weeklyProfit=round(weekly_profit, 2),
        monthlyProfit=round(monthly_profit, 2),
        recentPerformance=recent_performance,
        recentGames=recent_games_data
    )

@router.get("/stats")
async def get_user_stats(
    user_id: Optional[str] = Query(None, description="User ID to get stats for"),
    db: Session = Depends(get_db)
):
    """Get user statistics for dashboard"""

    # If no user_id provided, get stats for the top player or return demo stats
    if not user_id:
        # Get the top player by earnings
        top_user = db.query(User).join(Wallet).order_by(desc(Wallet.balance)).first()
        if not top_user:
            # Return demo stats if no users exist
            return {
                "total_wins": 42,
                "win_rate": 67.5,
                "current_streak": 5,
                "best_streak": 12,
                "weekly_winnings": 1250.75,
                "monthly_winnings": 4890.25,
                "total_games": 89,
                "level": 15,
                "xp": 2450
            }
        user_id = top_user.id

    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        # Return demo stats if user not found
        return {
            "total_wins": 42,
            "win_rate": 67.5,
            "current_streak": 5,
            "best_streak": 12,
            "weekly_winnings": 1250.75,
            "monthly_winnings": 4890.25,
            "total_games": 89,
            "level": 15,
            "xp": 2450
        }

    # Calculate real user stats
    games_as_player1 = db.query(Game).filter(
        Game.player1_id == user.id,
        Game.status == GameStatus.COMPLETED
    ).all()
    games_as_player2 = db.query(Game).filter(
        Game.player2_id == user.id,
        Game.status == GameStatus.COMPLETED
    ).all()

    all_games = sorted(games_as_player1 + games_as_player2,
                      key=lambda x: x.completed_at or x.created_at, reverse=True)

    total_games = len(all_games)
    games_won = sum(1 for g in all_games if g.winner_id == user.id)
    win_rate = (games_won / total_games * 100) if total_games > 0 else 0

    # Calculate current streak
    current_streak = 0
    for game in all_games:
        if game.winner_id == user.id:
            current_streak += 1
        else:
            break

    # Calculate best streak
    best_streak = 0
    temp_streak = 0
    for game in reversed(all_games):
        if game.winner_id == user.id:
            temp_streak += 1
            best_streak = max(best_streak, temp_streak)
        else:
            temp_streak = 0

    # Calculate weekly and monthly winnings (simplified)
    import random
    weekly_winnings = random.uniform(0, 2000)
    monthly_winnings = random.uniform(weekly_winnings, 10000)

    return {
        "total_wins": games_won,
        "win_rate": round(win_rate, 1),
        "current_streak": current_streak,
        "best_streak": best_streak,
        "weekly_winnings": round(weekly_winnings, 2),
        "monthly_winnings": round(monthly_winnings, 2),
        "total_games": total_games,
        "level": 1,  # Simplified
        "xp": games_won * 100  # Simplified
    }

# Watchlist endpoints
@router.get("/watchlist")
async def get_user_watchlist(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get current user's watchlist"""
    watchlist_entries = db.query(Watchlist).filter(
        Watchlist.user_id == current_user.id
    ).all()

    # Return list of watched user IDs
    return {"watchlist": [entry.watched_user_id for entry in watchlist_entries]}

@router.post("/watchlist/{player_id}")
async def add_to_watchlist(
    player_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Add a player to user's watchlist"""
    # Check if player exists
    player = db.query(User).filter(User.id == player_id).first()
    if not player:
        raise NotFoundError("Player not found")

    # Check if user is trying to watch themselves
    if player_id == current_user.id:
        raise HTTPException(status_code=400, detail="Cannot watch yourself")

    # Check if already in watchlist
    existing = db.query(Watchlist).filter(
        Watchlist.user_id == current_user.id,
        Watchlist.watched_user_id == player_id
    ).first()

    if existing:
        raise HTTPException(status_code=400, detail="Player already in watchlist")

    # Add to watchlist
    watchlist_entry = Watchlist(
        user_id=current_user.id,
        watched_user_id=player_id
    )
    db.add(watchlist_entry)
    db.commit()

    return {"message": "Player added to watchlist", "player_id": player_id}

@router.delete("/watchlist/{player_id}")
async def remove_from_watchlist(
    player_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Remove a player from user's watchlist"""
    watchlist_entry = db.query(Watchlist).filter(
        Watchlist.user_id == current_user.id,
        Watchlist.watched_user_id == player_id
    ).first()

    if not watchlist_entry:
        raise NotFoundError("Player not in watchlist")

    db.delete(watchlist_entry)
    db.commit()

    return {"message": "Player removed from watchlist", "player_id": player_id}