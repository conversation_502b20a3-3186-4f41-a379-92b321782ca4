"""
Game Catalog API - Simple API for managing game catalog and basic game creation
"""
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime
import uuid

from app.database.db import get_db
from app.models.user import User
from app.core.dependencies import get_current_user

router = APIRouter()

# Pydantic models for API responses
class GameCatalogResponse(BaseModel):
    id: str
    name: str
    slug: str
    description: Optional[str]
    category: str
    min_players: int
    max_players: int
    estimated_duration_minutes: int
    skill_based: bool
    is_active: bool
    is_featured: bool
    min_wager: float
    max_wager: float
    allows_spectators: bool
    allows_tournaments: bool
    default_settings: Dict[str, Any]
    background_color: str
    text_color: str

class CreateGameRequest(BaseModel):
    game_slug: str = Field(..., description="Game slug from catalog")
    wager_amount: float = Field(default=0.0, description="Wager amount")
    settings: Dict[str, Any] = Field(default_factory=dict, description="Game settings")
    is_private: bool = Field(default=False, description="Make game private")

@router.get("/catalog")
async def get_game_catalog(
    category: Optional[str] = Query(None, description="Filter by game category"),
    active_only: bool = Query(True, description="Only return active games"),
    db: Session = Depends(get_db)
):
    """Get all available games from the catalog"""
    
    # Query the game catalog table directly
    query = "SELECT * FROM game_catalog"
    conditions = []
    params = {}
    
    if active_only:
        conditions.append("is_active = :active")
        params["active"] = True
    
    if category:
        conditions.append("category = :category")
        params["category"] = category
    
    if conditions:
        query += " WHERE " + " AND ".join(conditions)
    
    query += " ORDER BY sort_order, name"
    
    from sqlalchemy import text
    result = db.execute(text(query), params)
    games = result.fetchall()
    
    # Convert to dict format
    game_list = []
    for game in games:
        game_dict = {
            "id": game.id,
            "name": game.name,
            "slug": game.slug,
            "description": game.description,
            "category": game.category,
            "min_players": game.min_players,
            "max_players": game.max_players,
            "estimated_duration_minutes": game.estimated_duration_minutes,
            "skill_based": game.skill_based,
            "is_active": game.is_active,
            "is_featured": game.is_featured,
            "min_wager": game.min_wager,
            "max_wager": game.max_wager,
            "allows_spectators": game.allows_spectators,
            "allows_tournaments": game.allows_tournaments,
            "default_settings": game.default_settings if hasattr(game, 'default_settings') else {},
            "background_color": game.background_color,
            "text_color": game.text_color
        }
        game_list.append(game_dict)
    
    return {
        "games": game_list,
        "total": len(game_list)
    }

@router.get("/catalog/{game_slug}")
async def get_game_details(
    game_slug: str,
    db: Session = Depends(get_db)
):
    """Get detailed information about a specific game"""
    
    from sqlalchemy import text
    result = db.execute(
        text("SELECT * FROM game_catalog WHERE slug = :slug"),
        {"slug": game_slug}
    )
    game = result.fetchone()
    
    if not game:
        raise HTTPException(status_code=404, detail="Game not found")
    
    return {
        "id": game.id,
        "name": game.name,
        "slug": game.slug,
        "description": game.description,
        "category": game.category,
        "min_players": game.min_players,
        "max_players": game.max_players,
        "estimated_duration_minutes": game.estimated_duration_minutes,
        "skill_based": game.skill_based,
        "is_active": game.is_active,
        "is_featured": game.is_featured,
        "min_wager": game.min_wager,
        "max_wager": game.max_wager,
        "allows_spectators": game.allows_spectators,
        "allows_tournaments": game.allows_tournaments,
        "default_settings": game.default_settings if hasattr(game, 'default_settings') else {},
        "available_settings": game.available_settings if hasattr(game, 'available_settings') else {},
        "rules": game.rules if hasattr(game, 'rules') else {},
        "background_color": game.background_color,
        "text_color": game.text_color
    }

@router.get("/categories")
async def get_game_categories(db: Session = Depends(get_db)):
    """Get all game categories"""
    
    from sqlalchemy import text
    result = db.execute(text("SELECT DISTINCT category FROM game_catalog WHERE is_active = true"))
    categories = [row.category for row in result.fetchall()]
    
    # Add category metadata
    category_info = {
        "strategy": {"name": "Strategy Games", "icon": "🧠", "description": "Games requiring strategic thinking"},
        "casual": {"name": "Casual Games", "icon": "🎮", "description": "Quick and easy games"},
        "trivia": {"name": "Trivia & Knowledge", "icon": "🧩", "description": "Test your knowledge"},
        "card": {"name": "Card Games", "icon": "🃏", "description": "Classic and modern card games"},
        "sports": {"name": "Sports Games", "icon": "⚽", "description": "Sports-related challenges"},
        "puzzle": {"name": "Puzzle Games", "icon": "🧩", "description": "Brain teasers and logic puzzles"}
    }
    
    return {
        "categories": [
            {
                "slug": cat,
                "name": category_info.get(cat, {}).get("name", cat.title()),
                "icon": category_info.get(cat, {}).get("icon", "🎮"),
                "description": category_info.get(cat, {}).get("description", f"{cat.title()} games")
            }
            for cat in categories
        ]
    }

@router.post("/create-simple")
async def create_simple_game(
    request: CreateGameRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Create a simple game entry (placeholder for full implementation)"""
    
    # Find the game in catalog
    from sqlalchemy import text
    result = db.execute(
        text("SELECT * FROM game_catalog WHERE slug = :slug"),
        {"slug": request.game_slug}
    )
    game_catalog = result.fetchone()
    
    if not game_catalog:
        raise HTTPException(status_code=404, detail="Game not found in catalog")
    
    if not game_catalog.is_active:
        raise HTTPException(status_code=400, detail="Game is not currently available")
    
    # Validate wager amount
    if request.wager_amount < game_catalog.min_wager or request.wager_amount > game_catalog.max_wager:
        raise HTTPException(
            status_code=400, 
            detail=f"Wager must be between ${game_catalog.min_wager} and ${game_catalog.max_wager}"
        )
    
    # For now, just return success (full implementation would create game instance)
    game_id = str(uuid.uuid4())
    
    return {
        "success": True,
        "message": f"Game '{game_catalog.name}' created successfully",
        "game_id": game_id,
        "game_name": game_catalog.name,
        "game_slug": request.game_slug,
        "wager_amount": request.wager_amount,
        "max_players": game_catalog.max_players,
        "estimated_duration": game_catalog.estimated_duration_minutes,
        "creator": current_user.username,
        "status": "waiting_for_players",
        "settings": {**game_catalog.default_settings, **request.settings, "is_private": request.is_private}
    }

@router.get("/stats")
async def get_platform_stats(db: Session = Depends(get_db)):
    """Get platform game statistics"""
    
    from sqlalchemy import text
    
    # Get total games available
    total_games = db.execute(text("SELECT COUNT(*) FROM game_catalog WHERE is_active = true")).scalar()
    
    # Get games by category
    category_stats = db.execute(text("""
        SELECT category, COUNT(*) as count 
        FROM game_catalog 
        WHERE is_active = true 
        GROUP BY category
    """)).fetchall()
    
    return {
        "total_active_games": total_games,
        "games_by_category": [
            {"category": stat.category, "count": stat.count}
            for stat in category_stats
        ],
        "featured_games": 0,  # Placeholder
        "total_game_instances": 0,  # Placeholder - would query game instances table
        "active_players": 0  # Placeholder - would query active sessions
    }
