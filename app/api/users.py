from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File
from sqlalchemy.orm import Session
from sqlalchemy import func
from typing import List, Optional
from app.database.db import get_db
from app.models.user import User
from app.models.wallet import Wallet
from app.models.user_profile import UserStats, Friendship, Achievement, UserAchievement
from app.models.marketplace import Bet, BetParticipation
from app.models.game import Game
from app.models.game_management import GamePlayer
from app.schemas.user import (
    UserResponse, UserProfileResponse, UserUpdate,
    UserStatsResponse, FriendRequest, FriendResponse,
    UserSearchResponse, UserKYCSubmit, UserKYCResponse
)
from app.auth.jwt import get_current_user, get_current_active_user
from app.core.config import settings
import os
import uuid
from datetime import datetime, timedelta

router = APIRouter(tags=["users"])

# Upload directories
AVATAR_UPLOAD_DIR = "uploads/avatars"
KYC_UPLOAD_DIR = "uploads/kyc"
os.makedirs(AVATAR_UPLOAD_DIR, exist_ok=True)
os.makedirs(KYC_UPLOAD_DIR, exist_ok=True)

@router.get("/me", response_model=UserProfileResponse)
async def get_current_user_profile(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get current user's full profile."""
    # Get user stats
    stats = db.query(UserStats).filter(UserStats.user_id == current_user.id).first()

    # Get wallet info
    wallet = db.query(Wallet).filter(Wallet.user_id == current_user.id).first()

    # Get achievements count
    achievements_count = db.query(UserAchievement).filter(
        UserAchievement.user_id == current_user.id
    ).count()

    # Get friends count
    friends_count = db.query(Friendship).filter(
        (Friendship.user_id == current_user.id) | (Friendship.friend_id == current_user.id),
        Friendship.status == "accepted"
    ).count()

    # Build response
    # Calculate games lost from games played and games won
    games_lost = (stats.games_played - stats.games_won) if stats else 0

    # Create a dictionary with the user data, mapping real_name to full_name
    user_dict = current_user.__dict__.copy()
    user_dict["full_name"] = current_user.real_name or ""  # Provide empty string if None

    profile_data = {
        **user_dict,
        "games_played": stats.games_played if stats else 0,
        "games_won": stats.games_won if stats else 0,
        "games_lost": games_lost,
        "win_rate": stats.win_rate if stats else 0.0,
        "total_wagered": stats.total_wagered if stats else 0.0,
        "total_won": stats.total_earned if stats else 0.0,  # Use total_earned instead of total_won
        "balance": wallet.balance if wallet else 0.0,
        "bonus_tokens": wallet.bonus_tokens if wallet else 0,
        "achievements_earned": achievements_count,
        "friends_count": friends_count
    }

    return UserProfileResponse(**profile_data)

@router.put("/me", response_model=UserResponse)
async def update_current_user(
    update_data: UserUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Update current user profile."""
    # Update user fields
    update_dict = update_data.model_dump(exclude_unset=True)
    for field, value in update_dict.items():
        setattr(current_user, field, value)

    current_user.updated_at = datetime.utcnow()
    db.commit()
    db.refresh(current_user)

    return current_user

@router.post("/me/avatar", response_model=dict)
async def upload_avatar(
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Upload user avatar."""
    if not file:
        raise HTTPException(status_code=400, detail="No file provided")

    # Validate file type
    allowed_types = ["image/jpeg", "image/png", "image/gif", "image/webp"]
    if file.content_type not in allowed_types:
        raise HTTPException(
            status_code=400,
            detail="Invalid file type. Only JPEG, PNG, GIF, and WebP images are allowed."
        )

    # Generate filename
    file_extension = file.filename.split(".")[-1]
    filename = f"{current_user.id}_avatar_{uuid.uuid4()}.{file_extension}"
    filepath = os.path.join(AVATAR_UPLOAD_DIR, filename)

    # Save file
    with open(filepath, "wb") as buffer:
        content = await file.read()
        buffer.write(content)

    # Update user avatar
    current_user.avatar_url = f"/uploads/avatars/{filename}"
    current_user.updated_at = datetime.utcnow()
    db.commit()

    return {"avatar_url": current_user.avatar_url}

@router.get("/me/stats", response_model=UserStatsResponse)
async def get_current_user_stats(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get current user's detailed statistics."""
    stats = db.query(UserStats).filter(UserStats.user_id == current_user.id).first()

    if not stats:
        # Create default stats if not exist
        stats = UserStats(
            user_id=current_user.id,
            games_played=0,
            games_won=0,
            win_rate=0.0,
            total_wagered=0.0,
            total_earned=0.0,
            biggest_win=0.0,
            biggest_loss=0.0,
            win_streak=0,
            longest_win_streak=0
        )
        db.add(stats)
        db.commit()
        db.refresh(stats)

    return stats

@router.get("/{user_id}", response_model=UserResponse)
async def get_user_profile(
    user_id: str,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get another user's public profile."""
    user = db.query(User).filter(User.id == user_id).first()

    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    # Check if they're friends to show more info
    friendship = db.query(Friendship).filter(
        ((Friendship.user_id == current_user.id) & (Friendship.friend_id == user_id)) |
        ((Friendship.user_id == user_id) & (Friendship.friend_id == current_user.id)),
        Friendship.status == "accepted"
    ).first()

    # Return limited info if not friends
    return user

@router.get("/{user_id}/stats", response_model=UserStatsResponse)
async def get_user_stats(
    user_id: str,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get another user's statistics."""
    user = db.query(User).filter(User.id == user_id).first()

    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    stats = db.query(UserStats).filter(UserStats.user_id == user_id).first()

    if not stats:
        raise HTTPException(status_code=404, detail="User statistics not found")

    return stats

@router.post("/me/friends/request", response_model=dict)
async def send_friend_request(
    request_data: FriendRequest,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Send a friend request."""
    # Validate target user exists
    target_user = db.query(User).filter(User.id == request_data.to_user_id).first()
    if not target_user:
        raise HTTPException(status_code=404, detail="User not found")

    # Check if already friends or request exists
    existing = db.query(Friendship).filter(
        ((Friendship.user_id == current_user.id) & (Friendship.friend_id == request_data.to_user_id)) |
        ((Friendship.user_id == request_data.to_user_id) & (Friendship.friend_id == current_user.id))
    ).first()

    if existing:
        if existing.status == "accepted":
            raise HTTPException(status_code=400, detail="Already friends")
        elif existing.status == "pending":
            raise HTTPException(status_code=400, detail="Friend request already sent")

    # Create friend request
    friendship = Friendship(
        id=str(uuid.uuid4()),
        user_id=current_user.id,
        friend_id=request_data.to_user_id,
        status="pending",
        created_at=datetime.utcnow()
    )

    db.add(friendship)
    db.commit()

    return {"message": "Friend request sent"}

@router.get("/me/friends", response_model=List[FriendResponse])
async def get_friends(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get user's friends list."""
    friendships = db.query(Friendship).filter(
        ((Friendship.user_id == current_user.id) | (Friendship.friend_id == current_user.id)),
        Friendship.status == "accepted"
    ).all()

    friends = []
    for friendship in friendships:
        friend_id = friendship.friend_id if friendship.user_id == current_user.id else friendship.user_id
        friend = db.query(User).filter(User.id == friend_id).first()

        if friend:
            # Check online status (last active within 5 minutes)
            is_online = friend.last_active and friend.last_active > datetime.utcnow() - timedelta(minutes=5)

            friends.append(FriendResponse(
                id=friend.id,
                username=friend.username,
                avatar_url=friend.avatar_url,
                level=friend.level,
                status="online" if is_online else "offline",
                is_online=is_online,
                last_active=friend.last_active
            ))

    return friends

@router.get("/search", response_model=List[UserSearchResponse])
async def search_users(
    query: str,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
    limit: int = 20
):
    """Search for users by username."""
    if len(query) < 3:
        raise HTTPException(status_code=400, detail="Search query must be at least 3 characters")

    users = db.query(User).filter(
        User.username.ilike(f"%{query}%"),
        User.id != current_user.id
    ).limit(limit).all()

    results = []
    for user in users:
        # Check if already friends
        friendship = db.query(Friendship).filter(
            ((Friendship.user_id == current_user.id) & (Friendship.friend_id == user.id)) |
            ((Friendship.user_id == user.id) & (Friendship.friend_id == current_user.id)),
            Friendship.status == "accepted"
        ).first()

        is_online = user.last_active and user.last_active > datetime.utcnow() - timedelta(minutes=5)

        results.append(UserSearchResponse(
            id=user.id,
            username=user.username,
            avatar_url=user.avatar_url,
            level=user.level,
            is_friend=bool(friendship),
            is_online=is_online
        ))

    return results

@router.post("/me/kyc", response_model=UserKYCResponse)
async def submit_kyc(
    kyc_data: UserKYCSubmit,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Submit KYC verification."""
    # Update user KYC fields
    current_user.real_name = kyc_data.real_name
    current_user.kyc_status = "submitted"
    current_user.kyc_submitted_at = datetime.utcnow()

    # In a real implementation, you'd save additional KYC data
    # and trigger verification process

    db.commit()
    db.refresh(current_user)

    return UserKYCResponse(
        kyc_status=current_user.kyc_status,
        kyc_submitted_at=current_user.kyc_submitted_at,
        kyc_verified_at=current_user.kyc_verified_at,
        kyc_rejected_at=current_user.kyc_rejected_at,
        kyc_rejection_reason=current_user.kyc_rejection_reason
    )

@router.post("/me/kyc/upload/{document_type}", response_model=dict)
async def upload_kyc_document(
    document_type: str,
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Upload KYC document."""
    valid_types = ["id_front", "id_back", "selfie"]
    if document_type not in valid_types:
        raise HTTPException(
            status_code=400,
            detail=f"Invalid document type. Must be one of: {', '.join(valid_types)}"
        )

    # Validate file type
    allowed_types = ["image/jpeg", "image/png", "image/jpg"]
    if file.content_type not in allowed_types:
        raise HTTPException(
            status_code=400,
            detail="Invalid file type. Only JPEG and PNG images are allowed."
        )

    # Generate filename
    file_extension = file.filename.split(".")[-1]
    filename = f"{current_user.id}_{document_type}_{uuid.uuid4()}.{file_extension}"
    filepath = os.path.join(KYC_UPLOAD_DIR, filename)

    # Save file
    with open(filepath, "wb") as buffer:
        content = await file.read()
        buffer.write(content)

    return {"document_type": document_type, "url": f"/uploads/kyc/{filename}"}