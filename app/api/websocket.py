from fastapi import WebSocket, WebSocketDisconnect, Depends, HTTPException, status, APIRouter
from sqlalchemy.orm import Session
from typing import Dict, List, Optional
import json
from datetime import datetime
from app.database.db import get_db
from app.models.user import User
from app.models.game import Game, GameMove, GameStatus, GameType
from app.core.security import decode_token
from app.core.websocket_auth import get_current_user_ws

# Create WebSocket router
websocket_router = APIRouter()

def fen_to_2d_array(fen_board: str) -> List[List[str]]:
    """Convert FEN board notation to 2D array for frontend"""
    board = []
    rows = fen_board.split('/')

    for row in rows:
        board_row = []
        for char in row:
            if char.isdigit():
                # Add empty squares
                for _ in range(int(char)):
                    board_row.append('.')
            else:
                # Add piece
                board_row.append(char)
        board.append(board_row)

    return board

class ConnectionManager:
    def __init__(self):
        # Active connections mapped by game_id
        self.active_connections: Dict[str, Dict[str, WebSocket]] = {}
        # User to game mapping
        self.user_games: Dict[str, str] = {}
        # Game state cache
        self.game_states: Dict[str, dict] = {}

    async def connect(self, websocket: WebSocket, game_id: str, user_id: str):
        await websocket.accept()

        # Initialize game connections if not exists
        if game_id not in self.active_connections:
            self.active_connections[game_id] = {}

        # Store the connection
        self.active_connections[game_id][user_id] = websocket
        self.user_games[user_id] = game_id

        # Send current game state to the new connection
        if game_id in self.game_states:
            await self.send_personal_message(
                {"type": "game_state", "data": self.game_states[game_id]},
                websocket
            )

    def disconnect(self, game_id: str, user_id: str):
        if game_id in self.active_connections:
            self.active_connections[game_id].pop(user_id, None)
            if not self.active_connections[game_id]:
                self.active_connections.pop(game_id, None)

        self.user_games.pop(user_id, None)

    async def send_personal_message(self, message: dict, websocket: WebSocket):
        try:
            await websocket.send_json(message)
        except Exception as e:
            print(f"Error sending message: {e}")

    async def broadcast_to_game(self, message: dict, game_id: str, exclude_user: Optional[str] = None):
        if game_id in self.active_connections:
            for user_id, connection in self.active_connections[game_id].items():
                if user_id != exclude_user:
                    await self.send_personal_message(message, connection)

    async def broadcast_to_all_in_game(self, message: dict, game_id: str):
        if game_id in self.active_connections:
            for connection in self.active_connections[game_id].values():
                await self.send_personal_message(message, connection)

    def update_game_state(self, game_id: str, state: dict):
        self.game_states[game_id] = state

    def get_game_state(self, game_id: str) -> Optional[dict]:
        return self.game_states.get(game_id)

    def get_players_in_game(self, game_id: str) -> List[str]:
        if game_id in self.active_connections:
            return list(self.active_connections[game_id].keys())
        return []

manager = ConnectionManager()

async def websocket_endpoint(
    websocket: WebSocket,
    game_id: str,
    current_user: User = Depends(get_current_user_ws),
    db: Session = Depends(get_db)
):
    """WebSocket endpoint for real-time game updates"""
    await manager.connect(websocket, game_id, current_user.id)

    try:
        # Get the game
        game = db.query(Game).filter(Game.id == game_id).first()
        if not game:
            await websocket.send_json({"type": "error", "message": "Game not found"})
            await websocket.close()
            return

        # Check if user is a player or spectator
        is_player = current_user.id in [game.player1_id, game.player2_id]

        # Convert game state for frontend
        frontend_state = game.game_state
        if game.game_type == GameType.CHESS and game.game_state and "board" in game.game_state:
            # Convert FEN notation to 2D array for chess
            fen_board = game.game_state["board"].split()[0]  # Get board part of FEN
            board_2d = fen_to_2d_array(fen_board)
            frontend_state = {
                **game.game_state,
                "board": board_2d,
                "boardState": board_2d  # Also provide as boardState for compatibility
            }

        # Send initial game state
        game_state = {
            "game_id": game.id,
            "game_type": game.game_type.value,
            "status": game.status.value,
            "player1_id": game.player1_id,
            "player2_id": game.player2_id,
            "current_player_id": game.current_player_id,
            "state": frontend_state,
            "settings": game.settings,
            "is_player": is_player,
            "your_player_number": 1 if current_user.id == game.player1_id else 2 if current_user.id == game.player2_id else None
        }

        await websocket.send_json({"type": "game_state", "data": game_state})
        manager.update_game_state(game_id, game_state)

        # Notify others that a user joined
        await manager.broadcast_to_game({
            "type": "user_joined",
            "user_id": current_user.id,
            "username": current_user.username,
            "is_player": is_player
        }, game_id, exclude_user=current_user.id)

        # Listen for messages
        while True:
            data = await websocket.receive_json()
            message_type = data.get("type")

            if message_type == "make_move":
                # Only players can make moves
                if not is_player:
                    await websocket.send_json({"type": "error", "message": "Only players can make moves"})
                    continue

                # Check if it's the player's turn
                if game.current_player_id != current_user.id:
                    await websocket.send_json({"type": "error", "message": "Not your turn"})
                    continue

                # Process the move
                move_data = data.get("move")
                move_number = db.query(GameMove).filter(GameMove.game_id == game_id).count() + 1

                # Create move record
                game_move = GameMove(
                    game_id=game_id,
                    player_id=current_user.id,
                    move_number=move_number,
                    move_data=move_data
                )
                db.add(game_move)

                # Update game state based on game type
                if game.game_type == GameType.CHECKERS:
                    # Update checkers game state
                    game.state = data.get("new_state", game.state)
                    # Switch turns
                    game.current_player_id = game.player2_id if game.current_player_id == game.player1_id else game.player1_id
                elif game.game_type == GameType.ROCK_PAPER_SCISSORS:
                    # Update RPS game state
                    game.state = data.get("new_state", game.state)
                elif game.game_type == GameType.CHESS:
                    # Update chess game state
                    game.state = data.get("new_state", game.state)
                    # Switch turns
                    game.current_player_id = game.player2_id if game.current_player_id == game.player1_id else game.player1_id

                # Check for win condition
                if data.get("game_over"):
                    game.status = GameStatus.COMPLETED
                    game.winner_id = data.get("winner_id")
                    game.completed_at = datetime.utcnow()

                game.last_move_at = datetime.utcnow()
                db.commit()
                db.refresh(game)

                # Broadcast move to all players
                await manager.broadcast_to_all_in_game({
                    "type": "move_made",
                    "player_id": current_user.id,
                    "move": move_data,
                    "new_state": game.state,
                    "current_player_id": game.current_player_id,
                    "game_status": game.status.value,
                    "winner_id": game.winner_id
                }, game_id)

            elif message_type == "chat":
                # Broadcast chat message
                await manager.broadcast_to_all_in_game({
                    "type": "chat",
                    "user_id": current_user.id,
                    "username": current_user.username,
                    "message": data.get("message")
                }, game_id)

            elif message_type == "join_game":
                # Handle player 2 joining
                if not game.player2_id and current_user.id != game.player1_id:
                    game.player2_id = current_user.id
                    game.status = GameStatus.IN_PROGRESS
                    game.started_at = datetime.utcnow()
                    game.current_player_id = game.player1_id
                    db.commit()
                    db.refresh(game)

                    # Notify all users
                    await manager.broadcast_to_all_in_game({
                        "type": "player_joined",
                        "player2_id": current_user.id,
                        "player2_username": current_user.username,
                        "game_status": game.status.value
                    }, game_id)

            elif message_type == "leave_game":
                # Handle player leaving
                if is_player and game.status == GameStatus.IN_PROGRESS:
                    game.status = GameStatus.CANCELLED
                    game.winner_id = game.player2_id if current_user.id == game.player1_id else game.player1_id
                    game.completed_at = datetime.utcnow()
                    db.commit()

                    await manager.broadcast_to_all_in_game({
                        "type": "game_cancelled",
                        "leaving_player_id": current_user.id,
                        "winner_id": game.winner_id
                    }, game_id)

    except WebSocketDisconnect:
        manager.disconnect(game_id, current_user.id)
        await manager.broadcast_to_game({
            "type": "user_disconnected",
            "user_id": current_user.id,
            "username": current_user.username
        }, game_id)

# Register WebSocket endpoint
@websocket_router.websocket("/ws/game/{game_id}")
async def websocket_game_endpoint(websocket: WebSocket, game_id: str):
    """WebSocket endpoint for real-time game updates"""
    # Get token from query params
    token = websocket.query_params.get("token")
    if not token:
        await websocket.close(code=status.WS_1008_POLICY_VIOLATION)
        return

    # Verify token and get user
    try:
        payload = decode_token(token)
        user_id = payload.get("sub")
        if not user_id:
            await websocket.close(code=status.WS_1008_POLICY_VIOLATION)
            return
    except Exception:
        await websocket.close(code=status.WS_1008_POLICY_VIOLATION)
        return

    # Get database session
    from app.database.db import SessionLocal
    db = SessionLocal()

    try:
        # Get user
        user = db.query(User).filter(User.id == user_id).first()
        if not user:
            await websocket.close(code=status.WS_1008_POLICY_VIOLATION)
            return

        # Call the main websocket endpoint function
        await websocket_endpoint(websocket, game_id, user, db)
    finally:
        db.close()