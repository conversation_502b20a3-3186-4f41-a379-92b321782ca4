from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError
from app.models.user import User
from app.models.wallet import Wallet
from app.models.payment import Transaction
from app.core.exceptions import InsufficientBalanceError
import uuid
from datetime import datetime
from typing import Optional, List, Tuple

class WalletService:
    def __init__(self, db: Session):
        self.db = db

    def get_or_create_wallet(self, user: User) -> Wallet:
        """Get user's wallet or create if doesn't exist"""
        wallet = self.db.query(Wallet).filter(Wallet.user_id == user.id).first()
        if not wallet:
            wallet = Wallet(
                user_id=user.id,
                balance=0.0,
                bonus_tokens=0,
                daily_limit=500.0,
                weekly_limit=2000.0,
                monthly_limit=5000.0
            )
            self.db.add(wallet)
            self.db.commit()
            self.db.refresh(wallet)
        return wallet

    def get_balance(self, user: User) -> float:
        """Get user's current balance"""
        wallet = self.get_or_create_wallet(user)
        return wallet.balance

    def place_game_wager(self, user: User, amount: float, game_id: str, description: str = None) -> Transaction:
        """Place a wager for a game and deduct from wallet balance"""
        wallet = self.get_or_create_wallet(user)

        if wallet.is_locked:
            raise ValueError(f"Wallet is locked: {wallet.locked_reason}")

        if wallet.balance < amount:
            raise InsufficientBalanceError(
                required=amount,
                available=wallet.balance
            )

        # Create transaction
        transaction = Transaction(
            wallet_id=wallet.id,
            type="bet_placed",
            amount=amount,
            fee=0.0,
            net_amount=amount,
            balance_before=wallet.balance,
            balance_after=wallet.balance - amount,
            game_id=game_id,
            status="completed",
            description=description or f"Wager for game {game_id}",
            reference_number=f"WAGER_{uuid.uuid4().hex[:8]}",
            completed_at=datetime.utcnow()
        )

        # Update wallet balance
        wallet.balance -= amount
        wallet.last_transaction_at = datetime.utcnow()

        # Update spending limits
        wallet.daily_spent += amount
        wallet.weekly_spent += amount
        wallet.monthly_spent += amount

        self.db.add(transaction)
        self.db.commit()

        return transaction

    def process_game_win(self, user: User, amount: float, game_id: str, description: str = None) -> Transaction:
        """Process game winnings and add to wallet balance"""
        wallet = self.get_or_create_wallet(user)

        # Create transaction
        transaction = Transaction(
            wallet_id=wallet.id,
            type="bet_won",
            amount=amount,
            fee=0.0,
            net_amount=amount,
            balance_before=wallet.balance,
            balance_after=wallet.balance + amount,
            game_id=game_id,
            status="completed",
            description=description or f"Winnings from game {game_id}",
            reference_number=f"WIN_{uuid.uuid4().hex[:8]}",
            completed_at=datetime.utcnow()
        )

        # Update wallet balance
        wallet.balance += amount
        wallet.last_transaction_at = datetime.utcnow()

        self.db.add(transaction)
        self.db.commit()

        return transaction

    def process_game_loss(self, user: User, game_id: str, description: str = None) -> Transaction:
        """Record a game loss (wager already deducted)"""
        wallet = self.get_or_create_wallet(user)

        # Create transaction record (no balance change as wager was already deducted)
        transaction = Transaction(
            wallet_id=wallet.id,
            type="bet_lost",
            amount=0.0,  # No additional deduction
            fee=0.0,
            net_amount=0.0,
            balance_before=wallet.balance,
            balance_after=wallet.balance,
            game_id=game_id,
            status="completed",
            description=description or f"Lost game {game_id}",
            reference_number=f"LOSS_{uuid.uuid4().hex[:8]}",
            completed_at=datetime.utcnow()
        )

        self.db.add(transaction)
        self.db.commit()

        return transaction

    def refund_game_wager(self, user: User, amount: float, game_id: str, description: str = None) -> Transaction:
        """Refund a game wager (e.g., when game is cancelled)"""
        wallet = self.get_or_create_wallet(user)

        # Create transaction
        transaction = Transaction(
            wallet_id=wallet.id,
            type="refund",
            amount=amount,
            fee=0.0,
            net_amount=amount,
            balance_before=wallet.balance,
            balance_after=wallet.balance + amount,
            game_id=game_id,
            status="completed",
            description=description or f"Refund for cancelled game {game_id}",
            reference_number=f"REFUND_{uuid.uuid4().hex[:8]}",
            completed_at=datetime.utcnow()
        )

        # Update wallet balance
        wallet.balance += amount
        wallet.last_transaction_at = datetime.utcnow()

        # Reduce spending limits since bet was refunded
        wallet.daily_spent = max(0, wallet.daily_spent - amount)
        wallet.weekly_spent = max(0, wallet.weekly_spent - amount)
        wallet.monthly_spent = max(0, wallet.monthly_spent - amount)

        self.db.add(transaction)
        self.db.commit()

        return transaction

    def get_transaction_history(self, user: User = None, wallet: Wallet = None, limit: int = 50, offset: int = 0) -> Tuple[List[Transaction], int]:
        """Get user's transaction history"""
        if wallet is None:
            if user is None:
                raise ValueError("Either user or wallet must be provided")
            wallet = self.get_or_create_wallet(user)

        # Get total count
        total = self.db.query(Transaction).filter(
            Transaction.wallet_id == wallet.id
        ).count()

        # Get transactions with pagination
        transactions = self.db.query(Transaction).filter(
            Transaction.wallet_id == wallet.id
        ).order_by(Transaction.created_at.desc()).offset(offset).limit(limit).all()

        return transactions, total