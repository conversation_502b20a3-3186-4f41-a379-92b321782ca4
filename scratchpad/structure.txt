Games Module Structure Specification
Overview
The games module follows a strict three-tier hierarchy that provides a consistent user experience across all games on the platform. This structure must be adhered to without exception.
URL Structure & Page Hierarchy
1. Games Catalog Page - /games
Purpose: Discovery and browsing of all available games on the platform
Features:

Display all games available on the platform
Filter games by:

Category/Genre
Player count (1v1, multiplayer, etc.)
Popularity
New/Featured


Search functionality
Game thumbnails with basic info (name, active players, etc.)
Quick stats (total active sessions, players online)

User Actions:

Browse and discover games
Apply filters to find specific game types
Click on a game to view its instances


2. Game Instances Page - /games/{game-slug}
Example: /games/highlight-hero
Purpose: View all instances of a specific game and manage game sessions
Features:

Grid/List view of all game instances with status tags:

🔴 LIVE - Currently active games
📅 SCHEDULED - Upcoming games with countdown
✅ COMPLETED - Finished games (with results)


Instance information displayed:

Players/Participants
Stakes/Pot size (if applicable)
Start time
Viewer count (for live games)
Game mode/rules


Filtering options:

By status (Live/Scheduled/Completed)
By stakes level
By game mode
By friends participating



User Actions:

Create New Instance - Start a new game session

Set game parameters (stakes, rules, etc.)
Choose between instant start or scheduled
Set privacy (public/private/invite-only)


Schedule Game - Plan future game sessions

Set date/time
Configure pre-registration
Set player limits


Create/Send Invites - Invite specific players

Generate invite links
Send direct invitations


Respond to Challenges - Accept/Decline challenges from other players
Join Existing Instance - Enter available games
Watch Live Games - Spectate ongoing matches
Follow Games - Get notifications when specific games start
View Completed Games - Access replays and results


3. Game Session Page - /games/{game-slug}/session/{session-id}
Example: /games/highlight-hero/session/abc123
Purpose: The actual gameplay interface where the game is played and watched
Features:
Game Display Area

Main game interface/board/screen
Player controls and actions
Game state and scores
Timer/Turn indicators

Live Audience Features

Viewer Count - Real-time spectator numbers
Live Betting Interface

Place bets during gameplay
View odds in real-time
Bet history


Live Wagering

Side bets
Prop bets
Dynamic odds based on game state



Information Panels

Participants Panel

Player profiles
Current scores/positions
Player statistics


Pot/Stakes Display

Total pot size
Individual stakes
Prize distribution


Game Info

Rules reminder
Game mode
Time elapsed/remaining



Communication

Live Chat Window

Participant chat
Spectator chat (separate or combined)
Emoji reactions
Chat moderation tools



Game Controls

Action buttons specific to the game
Settings/Options menu
Leave/Forfeit game option
Report issues button


Consistent User Experience Elements
Navigation

Breadcrumb navigation showing: Games > {Game Name} > Session {ID}
Back button functionality preserving filters and scroll position
Consistent header with user profile, notifications, wallet/balance

Visual Indicators

Consistent status badges (Live/Scheduled/Completed)
Standardized color coding:

Red = Live/Active
Blue = Scheduled/Upcoming
Green = Available to join
Gray = Completed/Ended


Participant count icons
Stakes/Pot indicators with currency symbols

Notifications

Join reminders for followed games
Game start notifications
Challenge received alerts
Game result notifications

Responsive Design

Mobile-first approach
Consistent layouts across devices
Touch-friendly controls for mobile gaming

Implementation Notes

State Management: Each level maintains its own state but shares user preferences (filters, view preferences)
Real-time Updates:

Games catalog updates with new games
Instance page shows live status changes
Session page has real-time game state sync


Performance:

Lazy loading for game lists
WebSocket connections for live games
Efficient caching of game assets


Security:

Session validation at each level
Secure game state management
Anti-cheat measures in session pages



This structure ensures users always know:

Where they are in the platform
What actions are available
How to navigate between games and sessions
What to expect at each level
