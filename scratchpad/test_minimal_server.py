#!/usr/bin/env python3
"""
Minimal server test to identify issues
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

# Create minimal app
app = FastAPI(title="BetBet Minimal Test")

# Add CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    return {"message": "Minimal BetBet API", "status": "working"}

@app.get("/health")
async def health():
    return {"status": "healthy", "minimal": True}

@app.get("/test-db")
async def test_db():
    """Test database connection"""
    try:
        from app.database.db import SessionLocal
        db = SessionLocal()
        # Simple query to test connection
        from sqlalchemy import text
        result = db.execute(text("SELECT 1 as test")).fetchone()
        db.close()
        return {"database": "connected", "test_result": result[0]}
    except Exception as e:
        return {"database": "error", "error": str(e)}

if __name__ == "__main__":
    print("🧪 Starting minimal server...")
    uvicorn.run(
        "test_minimal_server:app",
        host="0.0.0.0",
        port=8002,
        reload=False
    )
