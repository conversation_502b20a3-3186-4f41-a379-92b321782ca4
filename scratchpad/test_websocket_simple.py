#!/usr/bin/env python3
"""
Simple WebSocket test with Socket.IO
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import socketio
import uvicorn
from datetime import datetime

# Create FastAPI app
app = FastAPI(title="BetBet WebSocket Test")

# Add CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Create Socket.IO server
sio = socketio.AsyncServer(
    async_mode='asgi',
    cors_allowed_origins="*",
    logger=True,
    engineio_logger=True
)

# Basic routes
@app.get("/")
async def root():
    return {
        "message": "BetBet WebSocket Test", 
        "status": "running",
        "websocket": "enabled"
    }

@app.get("/health")
async def health():
    return {"status": "healthy", "websocket": "socket.io"}

# WebSocket event handlers
@sio.event
async def connect(sid, environ, auth=None):
    """Handle new WebSocket connection."""
    print(f"🔗 Client connected: {sid}")
    await sio.emit('connected', {
        'message': 'Connected to BetBet WebSocket',
        'sid': sid,
        'timestamp': datetime.utcnow().isoformat()
    }, room=sid)
    return True

@sio.event
async def disconnect(sid):
    """Handle WebSocket disconnection."""
    print(f"❌ Client disconnected: {sid}")

@sio.event
async def test_message(sid, data):
    """Handle test message."""
    print(f"📨 Test message from {sid}: {data}")
    
    # Echo back the message
    await sio.emit('test_response', {
        'original': data,
        'response': f"Server received: {data.get('message', 'No message')}",
        'timestamp': datetime.utcnow().isoformat()
    }, room=sid)
    
    return {'success': True}

@sio.event
async def join_game(sid, data):
    """Join a game room."""
    game_id = data.get('game_id', 'test_game')
    print(f"🎮 {sid} joining game: {game_id}")
    
    # Join the game room
    await sio.enter_room(sid, f"game_{game_id}")
    
    # Notify others in the room
    await sio.emit('player_joined', {
        'sid': sid,
        'game_id': game_id,
        'timestamp': datetime.utcnow().isoformat()
    }, room=f"game_{game_id}", skip_sid=sid)
    
    # Confirm to the player
    await sio.emit('joined_game', {
        'game_id': game_id,
        'message': f'Joined game {game_id}',
        'timestamp': datetime.utcnow().isoformat()
    }, room=sid)
    
    return {'success': True, 'game_id': game_id}

@sio.event
async def game_move(sid, data):
    """Handle game move."""
    game_id = data.get('game_id', 'test_game')
    move = data.get('move', {})
    
    print(f"🎯 Game move from {sid} in {game_id}: {move}")
    
    # Broadcast move to all players in the game
    await sio.emit('move_made', {
        'player_sid': sid,
        'game_id': game_id,
        'move': move,
        'timestamp': datetime.utcnow().isoformat()
    }, room=f"game_{game_id}")
    
    return {'success': True}

@sio.event
async def chat_message(sid, data):
    """Handle chat message."""
    game_id = data.get('game_id', 'test_game')
    message = data.get('message', '')
    
    print(f"💬 Chat from {sid}: {message}")
    
    # Broadcast chat to game room
    await sio.emit('chat', {
        'player_sid': sid,
        'game_id': game_id,
        'message': message,
        'timestamp': datetime.utcnow().isoformat()
    }, room=f"game_{game_id}")
    
    return {'success': True}

# Create Socket.IO app
socket_app = socketio.ASGIApp(sio, app)

if __name__ == "__main__":
    print("🚀 Starting WebSocket test server...")
    uvicorn.run(
        "test_websocket_simple:socket_app",
        host="0.0.0.0",
        port=8002,
        reload=False
    )
