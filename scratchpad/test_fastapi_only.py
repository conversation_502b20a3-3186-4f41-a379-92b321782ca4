#!/usr/bin/env python3
"""
Test FastAPI only without Socket.IO
"""
from fastapi import Fast<PERSON><PERSON>
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
from datetime import datetime

# Create FastAPI app
app = FastAPI(title="BetBet FastAPI Test")

# Add CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    return {
        "message": "BetBet FastAPI Test",
        "status": "running",
        "timestamp": datetime.utcnow().isoformat()
    }

@app.get("/health")
async def health():
    return {"status": "healthy", "test": "fastapi-only"}

if __name__ == "__main__":
    print("🧪 Testing FastAPI only...")
    uvicorn.run(
        "test_fastapi_only:app",
        host="0.0.0.0",
        port=8003,
        reload=False
    )
