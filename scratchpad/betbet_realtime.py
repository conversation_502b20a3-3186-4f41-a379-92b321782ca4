#!/usr/bin/env python3
"""
BetBet Real-time Gaming Server
Day 1 Implementation: Rock Paper Scissors with WebSocket
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from fastapi import FastAP<PERSON>, Depends, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from sqlalchemy.orm import Session
from sqlalchemy import text
import socketio
import uvicorn
import json
import uuid
from datetime import datetime
from typing import Dict, Any

# Import our modules
from app.database.db import SessionLocal, get_db
from app.core.config import settings
from app.models.user import User
from app.models.game import Game, GameStatus, GameType
from app.models.wallet import Wallet
from app.auth.jwt import verify_token

# Create FastAPI app
app = FastAPI(
    title="BetBet Real-time Gaming Platform",
    version="1.0.0",
    description="Real-time multiplayer gaming with spectator betting"
)

# Add CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Create Socket.IO server
sio = socketio.AsyncServer(
    async_mode='asgi',
    cors_allowed_origins="*",
    logger=False,  # Reduce noise
    engineio_logger=False
)

# In-memory game state for real-time games
active_games: Dict[str, Dict[str, Any]] = {}
connected_users: Dict[str, str] = {}  # sid -> user_id
user_sessions: Dict[str, str] = {}    # user_id -> sid

# Basic routes
@app.get("/")
async def root():
    return {
        "message": "BetBet Real-time Gaming Platform",
        "status": "running",
        "features": ["websocket", "postgresql", "real-time-gaming"],
        "games": ["rock_paper_scissors", "chess"],
        "active_games": len(active_games)
    }

@app.get("/health")
async def health():
    return {
        "status": "healthy",
        "database": "postgresql",
        "websocket": "socket.io",
        "active_connections": len(connected_users),
        "active_games": len(active_games)
    }

@app.get("/api/games/active")
async def get_active_games():
    """Get list of active games."""
    games = []
    for game_id, game_state in active_games.items():
        games.append({
            "id": game_id,
            "type": game_state.get("type"),
            "status": game_state.get("status"),
            "players": list(game_state.get("players", {}).keys()),
            "spectators": len(game_state.get("spectators", [])),
            "created_at": game_state.get("created_at")
        })
    return {"games": games, "count": len(games)}

@app.post("/api/games/create")
async def create_game(
    game_data: dict,
    db: Session = Depends(get_db)
):
    """Create a new game."""
    try:
        # Create game in database
        game = Game(
            id=str(uuid.uuid4()),
            game_type=GameType.ROCK_PAPER_SCISSORS,
            status=GameStatus.WAITING,
            player1_id=game_data.get("player1_id"),
            wager_amount=game_data.get("wager_amount", 0),
            settings=game_data.get("settings", {"rounds": 3})
        )
        
        db.add(game)
        db.commit()
        db.refresh(game)
        
        # Add to active games
        active_games[game.id] = {
            "id": game.id,
            "type": "rock_paper_scissors",
            "status": "waiting",
            "players": {},
            "spectators": [],
            "state": {
                "round": 1,
                "scores": {},
                "moves": {},
                "settings": game.settings
            },
            "created_at": datetime.utcnow().isoformat()
        }
        
        return {"success": True, "game_id": game.id}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# WebSocket event handlers
@sio.event
async def connect(sid, environ, auth=None):
    """Handle new WebSocket connection."""
    print(f"🔗 Client connected: {sid}")
    
    # Send connection confirmation
    await sio.emit('connected', {
        'message': 'Connected to BetBet Real-time Gaming',
        'sid': sid,
        'timestamp': datetime.utcnow().isoformat(),
        'active_games': len(active_games)
    }, room=sid)
    
    return True

@sio.event
async def disconnect(sid):
    """Handle WebSocket disconnection."""
    print(f"❌ Client disconnected: {sid}")
    
    # Clean up user session
    if sid in connected_users:
        user_id = connected_users[sid]
        del connected_users[sid]
        if user_id in user_sessions:
            del user_sessions[user_id]
        
        # Remove from any games
        for game_id, game_state in active_games.items():
            if user_id in game_state.get("players", {}):
                # Handle player disconnect
                await handle_player_disconnect(game_id, user_id)
            elif sid in game_state.get("spectators", []):
                game_state["spectators"].remove(sid)

@sio.event
async def authenticate(sid, data):
    """Authenticate user for WebSocket."""
    try:
        token = data.get('token')
        if not token:
            await sio.emit('auth_error', {'message': 'No token provided'}, room=sid)
            return {'success': False}
        
        # Verify token (simplified for demo)
        # In production, use proper JWT verification
        user_id = data.get('user_id', 'demo_user')
        
        # Store user session
        connected_users[sid] = user_id
        user_sessions[user_id] = sid
        
        await sio.emit('authenticated', {
            'user_id': user_id,
            'message': 'Authentication successful'
        }, room=sid)
        
        return {'success': True}
        
    except Exception as e:
        await sio.emit('auth_error', {'message': str(e)}, room=sid)
        return {'success': False}

@sio.event
async def join_game(sid, data):
    """Join a game as player or spectator."""
    try:
        game_id = data.get('game_id')
        role = data.get('role', 'player')  # 'player' or 'spectator'
        
        if game_id not in active_games:
            await sio.emit('error', {'message': 'Game not found'}, room=sid)
            return {'success': False}
        
        game_state = active_games[game_id]
        user_id = connected_users.get(sid, f'user_{sid}')
        
        if role == 'spectator':
            # Join as spectator
            if sid not in game_state["spectators"]:
                game_state["spectators"].append(sid)
            
            await sio.enter_room(sid, f"game_{game_id}")
            await sio.emit('joined_as_spectator', {
                'game_id': game_id,
                'game_state': game_state["state"]
            }, room=sid)
            
            # Notify others
            await sio.emit('spectator_joined', {
                'spectator_count': len(game_state["spectators"])
            }, room=f"game_{game_id}", skip_sid=sid)
            
        else:
            # Join as player
            players = game_state["players"]
            if len(players) >= 2:
                await sio.emit('error', {'message': 'Game is full'}, room=sid)
                return {'success': False}
            
            # Add player
            player_num = len(players) + 1
            players[user_id] = {
                'sid': sid,
                'player_num': player_num,
                'ready': False
            }
            
            await sio.enter_room(sid, f"game_{game_id}")
            await sio.emit('joined_game', {
                'game_id': game_id,
                'player_num': player_num,
                'game_state': game_state["state"]
            }, room=sid)
            
            # Notify others
            await sio.emit('player_joined', {
                'user_id': user_id,
                'player_num': player_num,
                'players_count': len(players)
            }, room=f"game_{game_id}", skip_sid=sid)
            
            # Start game if 2 players
            if len(players) == 2:
                game_state["status"] = "in_progress"
                await sio.emit('game_started', {
                    'message': 'Game started! Make your moves!'
                }, room=f"game_{game_id}")
        
        return {'success': True}
        
    except Exception as e:
        await sio.emit('error', {'message': str(e)}, room=sid)
        return {'success': False}

@sio.event
async def make_move(sid, data):
    """Handle game move (Rock Paper Scissors)."""
    try:
        game_id = data.get('game_id')
        move = data.get('move')  # 'rock', 'paper', or 'scissors'
        
        if game_id not in active_games:
            await sio.emit('error', {'message': 'Game not found'}, room=sid)
            return {'success': False}
        
        game_state = active_games[game_id]
        user_id = connected_users.get(sid)
        
        if user_id not in game_state["players"]:
            await sio.emit('error', {'message': 'Not a player in this game'}, room=sid)
            return {'success': False}
        
        # Record move
        current_round = game_state["state"]["round"]
        if str(current_round) not in game_state["state"]["moves"]:
            game_state["state"]["moves"][str(current_round)] = {}
        
        game_state["state"]["moves"][str(current_round)][user_id] = move
        
        # Check if both players have moved
        round_moves = game_state["state"]["moves"][str(current_round)]
        players = list(game_state["players"].keys())
        
        if len(round_moves) == 2:
            # Determine winner
            result = determine_rps_winner(round_moves, players)
            
            # Update scores
            if "scores" not in game_state["state"]:
                game_state["state"]["scores"] = {p: 0 for p in players}
            
            if result["winner"]:
                game_state["state"]["scores"][result["winner"]] += 1
            
            # Broadcast round result
            await sio.emit('round_complete', {
                'round': current_round,
                'moves': round_moves,
                'result': result,
                'scores': game_state["state"]["scores"]
            }, room=f"game_{game_id}")
            
            # Check for game end
            max_rounds = game_state["state"]["settings"].get("rounds", 3)
            if current_round >= max_rounds:
                # Game over
                final_winner = max(game_state["state"]["scores"], 
                                 key=game_state["state"]["scores"].get)
                
                await sio.emit('game_complete', {
                    'winner': final_winner,
                    'final_scores': game_state["state"]["scores"]
                }, room=f"game_{game_id}")
                
                game_state["status"] = "completed"
            else:
                # Next round
                game_state["state"]["round"] += 1
                await sio.emit('next_round', {
                    'round': game_state["state"]["round"]
                }, room=f"game_{game_id}")
        else:
            # Waiting for other player
            await sio.emit('move_recorded', {
                'message': 'Move recorded, waiting for opponent'
            }, room=sid)
        
        return {'success': True}
        
    except Exception as e:
        await sio.emit('error', {'message': str(e)}, room=sid)
        return {'success': False}

def determine_rps_winner(moves: dict, players: list) -> dict:
    """Determine Rock Paper Scissors winner."""
    player1, player2 = players[0], players[1]
    move1, move2 = moves[player1], moves[player2]
    
    if move1 == move2:
        return {"winner": None, "result": "tie"}
    
    winning_moves = {
        "rock": "scissors",
        "scissors": "paper", 
        "paper": "rock"
    }
    
    if winning_moves[move1] == move2:
        return {"winner": player1, "result": "win"}
    else:
        return {"winner": player2, "result": "win"}

async def handle_player_disconnect(game_id: str, user_id: str):
    """Handle player disconnection."""
    if game_id in active_games:
        game_state = active_games[game_id]
        if user_id in game_state["players"]:
            del game_state["players"][user_id]
            
            # Notify remaining players
            await sio.emit('player_disconnected', {
                'user_id': user_id,
                'message': 'Player disconnected'
            }, room=f"game_{game_id}")

# Create Socket.IO app
socket_app = socketio.ASGIApp(sio, app)

if __name__ == "__main__":
    print("🚀 Starting BetBet Real-time Gaming Platform...")
    print(f"📍 Database: {settings.DATABASE_URL}")
    print("🎮 Games: Rock Paper Scissors (real-time)")
    print("👀 Features: Spectator mode, Live chat, PostgreSQL")
    
    uvicorn.run(
        "betbet_realtime:socket_app",
        host="0.0.0.0",
        port=8000,
        reload=False
    )
