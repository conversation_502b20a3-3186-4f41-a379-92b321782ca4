# Admin Access Guide for BetBet

## Default Admin Credentials
- **Email**: `<EMAIL>`
- **Password**: `admin123`

## Steps to Access Admin Area

1. **Start the Backend Server**:
   ```bash
   cd /Users/<USER>/PycharmProjects/BetBet
   python -m uvicorn main:app --reload
   ```

2. **Start the Frontend Development Server**:
   ```bash
   cd /Users/<USER>/PycharmProjects/BetBet/frontend
   npm run dev
   ```

3. **Login as Admin**:
   - Navigate to `http://localhost:5174/login` (or whatever port your frontend is running on)
   - Enter the admin credentials:
     - Email: `<EMAIL>`
     - Password: `admin123`
   - Click Login

4. **Access Admin Panel**:
   - After successful login, you should see the "Admin" navigation item in the header (with a shield icon)
   - Click on "Admin" or navigate directly to `http://localhost:5174/admin`
   - You'll be automatically redirected to `/admin/dashboard`

## Admin Features Available

### 1. Dashboard (`/admin/dashboard`)
- Overview of platform statistics
- Total users count
- Active users metrics
- Pending KYC verifications
- Revenue statistics
- User activity tracking

### 2. KYC Verification (`/admin/kyc`)
- View all KYC submissions
- Filter by status (pending/approved/rejected)
- Search users by username, email, or real name
- View uploaded ID documents
- Approve or reject KYC applications

### 3. User Management (`/admin/users`)
- View all users (placeholder - to be implemented)
- Enable/disable user accounts
- View user details

### 4. Settings (`/admin/settings`)
- Platform settings (placeholder - to be implemented)

## Troubleshooting

### If Admin Link Doesn't Appear:
1. Make sure you're logged in with the admin account
2. Check the browser console for errors
3. Clear browser cache and cookies
4. Restart both frontend and backend servers

### If You Can't Login:
1. Ensure the backend is running
2. Check if the database file exists (`betbet.db`)
3. Delete the database and restart to recreate admin user
4. Check backend logs for errors

### To Create Additional Admin Users:
1. Login as existing admin
2. Use the API endpoint or database directly to set `is_superuser=True` for a user
3. Or create a management command/script to promote users to admin

## Security Notes
- Change the default admin password in production
- Store credentials securely
- Use environment variables for sensitive data
- Implement proper session management
- Add two-factor authentication for admin accounts