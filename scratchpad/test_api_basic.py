#!/usr/bin/env python3
"""
Test basic API functionality with PostgreSQL
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
from sqlalchemy import text

# Import our database
from app.database.db import SessionLocal
from app.core.config import settings

# Create minimal app
app = FastAPI(title="BetBet PostgreSQL Test")

# Add CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    return {
        "message": "BetBet API with PostgreSQL", 
        "status": "running",
        "database": "postgresql"
    }

@app.get("/health")
async def health():
    return {"status": "healthy", "database": "postgresql"}

@app.get("/test-db")
async def test_db():
    """Test PostgreSQL connection and data"""
    try:
        db = SessionLocal()
        
        # Test connection
        result = db.execute(text("SELECT current_database(), current_user, version()")).fetchone()
        
        # Count users
        user_count = db.execute(text("SELECT COUNT(*) FROM users")).fetchone()[0]
        
        # Count games
        game_count = db.execute(text("SELECT COUNT(*) FROM games")).fetchone()[0]
        
        # Count wallets
        wallet_count = db.execute(text("SELECT COUNT(*) FROM wallets")).fetchone()[0]
        
        db.close()
        
        return {
            "database": result[0],
            "user": result[1], 
            "version": result[2][:50] + "...",
            "data": {
                "users": user_count,
                "games": game_count,
                "wallets": wallet_count
            }
        }
    except Exception as e:
        return {"error": str(e)}

@app.get("/test-users")
async def test_users():
    """Test user data"""
    try:
        db = SessionLocal()
        
        # Get sample users
        result = db.execute(text("""
            SELECT username, real_name, level, xp, status 
            FROM users 
            ORDER BY created_at 
            LIMIT 5
        """)).fetchall()
        
        users = []
        for row in result:
            users.append({
                "username": row[0],
                "real_name": row[1],
                "level": row[2],
                "xp": row[3],
                "status": row[4]
            })
        
        db.close()
        
        return {"users": users, "count": len(users)}
    except Exception as e:
        return {"error": str(e)}

@app.get("/test-games")
async def test_games():
    """Test game data"""
    try:
        db = SessionLocal()
        
        # Get sample games
        result = db.execute(text("""
            SELECT g.game_type, g.status, g.wager_amount, g.total_pot,
                   u1.username as player1, u2.username as player2
            FROM games g
            LEFT JOIN users u1 ON g.player1_id = u1.id
            LEFT JOIN users u2 ON g.player2_id = u2.id
            ORDER BY g.created_at
        """)).fetchall()
        
        games = []
        for row in result:
            games.append({
                "type": row[0],
                "status": row[1],
                "wager": row[2],
                "pot": row[3],
                "player1": row[4],
                "player2": row[5]
            })
        
        db.close()
        
        return {"games": games, "count": len(games)}
    except Exception as e:
        return {"error": str(e)}

if __name__ == "__main__":
    print("🧪 Starting PostgreSQL API test...")
    print(f"📍 Database: {settings.DATABASE_URL}")
    uvicorn.run(
        "test_api_basic:app",
        host="0.0.0.0",
        port=8001,
        reload=False
    )
