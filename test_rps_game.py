#!/usr/bin/env python3
"""
Test complete Rock Paper Scissors game flow
"""
import asyncio
import socketio
import json
import requests
from datetime import datetime

# Create two clients for testing
client1 = socketio.AsyncClient()
client2 = socketio.AsyncClient()
spectator = socketio.AsyncClient()

# Game state tracking
game_id = None
game_events = []

# Event handlers for Player 1 (Alice)
@client1.event
async def connect():
    print("🔗 Alice connected")

@client1.event
async def connected(data):
    print(f"👤 Alice: {data['message']}")

@client1.event
async def login_success(data):
    print(f"✅ Alice logged in: {data['username']}")

@client1.event
async def joined_game(data):
    global game_id
    game_id = data['game_id']
    print(f"🎮 Alice joined game {game_id} as player {data['player_num']}")

@client1.event
async def game_started(data):
    print(f"🚀 Alice: {data['message']}")

@client1.event
async def move_recorded(data):
    print(f"📝 Alice: {data['message']}")

@client1.event
async def round_complete(data):
    print(f"🏁 Alice sees round {data['round']}: {data['message']}")
    print(f"   Scores: {data['scores']}")

@client1.event
async def game_complete(data):
    print(f"🏆 Alice: {data['message']}")
    print(f"   Final scores: {data['final_scores']}")

# Event handlers for Player 2 (Bob)
@client2.event
async def connect():
    print("🔗 Bob connected")

@client2.event
async def connected(data):
    print(f"👤 Bob: {data['message']}")

@client2.event
async def login_success(data):
    print(f"✅ Bob logged in: {data['username']}")

@client2.event
async def joined_game(data):
    print(f"🎮 Bob joined game {data['game_id']} as player {data['player_num']}")

@client2.event
async def game_started(data):
    print(f"🚀 Bob: {data['message']}")

@client2.event
async def move_recorded(data):
    print(f"📝 Bob: {data['message']}")

@client2.event
async def round_complete(data):
    print(f"🏁 Bob sees round {data['round']}: {data['message']}")

@client2.event
async def game_complete(data):
    print(f"🏆 Bob: {data['message']}")

# Event handlers for Spectator (Eve)
@spectator.event
async def connect():
    print("🔗 Eve (spectator) connected")

@spectator.event
async def connected(data):
    print(f"👀 Eve: {data['message']}")

@spectator.event
async def login_success(data):
    print(f"✅ Eve logged in: {data['username']}")

@spectator.event
async def joined_as_spectator(data):
    print(f"👁️ Eve joined as spectator in game {data['game_id']}")
    print(f"   Players: {data['players']}")

@spectator.event
async def spectator_joined(data):
    print(f"👀 New spectator joined: {data['username']}")

@spectator.event
async def game_started(data):
    print(f"👁️ Eve watching: {data['message']}")

@spectator.event
async def round_complete(data):
    print(f"👁️ Eve watches round {data['round']}: {data['message']}")

@spectator.event
async def game_complete(data):
    print(f"👁️ Eve watched game end: {data['message']}")

async def test_complete_game():
    """Test a complete Rock Paper Scissors game."""
    print("🎮 Testing Complete Rock Paper Scissors Game")
    print("=" * 50)
    
    try:
        # 1. Create a game via API
        print("\n1️⃣ Creating game...")
        response = requests.post('http://localhost:8003/api/games/create', json={
            'wager': 100,
            'rounds': 3
        })
        if response.status_code == 200:
            game_data = response.json()
            game_id = game_data['game_id']
            print(f"✅ Game created: {game_id}")
        else:
            print(f"❌ Failed to create game: {response.text}")
            return
        
        # 2. Connect players
        print("\n2️⃣ Connecting players...")
        await client1.connect('http://localhost:8003')
        await client2.connect('http://localhost:8003')
        await spectator.connect('http://localhost:8003')
        
        await asyncio.sleep(1)
        
        # 3. Login players
        print("\n3️⃣ Logging in players...")
        await client1.emit('login', {'username': 'alice_gamer'})
        await client2.emit('login', {'username': 'bob_player'})
        await spectator.emit('login', {'username': 'eve_spectator'})
        
        await asyncio.sleep(1)
        
        # 4. Join game
        print("\n4️⃣ Joining game...")
        await client1.emit('join_game', {'game_id': game_id, 'role': 'player'})
        await asyncio.sleep(0.5)
        await client2.emit('join_game', {'game_id': game_id, 'role': 'player'})
        await asyncio.sleep(0.5)
        await spectator.emit('join_game', {'game_id': game_id, 'role': 'spectator'})
        
        await asyncio.sleep(2)
        
        # 5. Play 3 rounds
        print("\n5️⃣ Playing rounds...")
        
        # Round 1: Alice=rock, Bob=scissors (Alice wins)
        print("\n🎯 Round 1:")
        await client1.emit('make_move', {'game_id': game_id, 'move': 'rock'})
        await asyncio.sleep(0.5)
        await client2.emit('make_move', {'game_id': game_id, 'move': 'scissors'})
        await asyncio.sleep(2)
        
        # Round 2: Alice=paper, Bob=rock (Alice wins)
        print("\n🎯 Round 2:")
        await client1.emit('make_move', {'game_id': game_id, 'move': 'paper'})
        await asyncio.sleep(0.5)
        await client2.emit('make_move', {'game_id': game_id, 'move': 'rock'})
        await asyncio.sleep(2)
        
        # Round 3: Alice=scissors, Bob=paper (Alice wins)
        print("\n🎯 Round 3:")
        await client1.emit('make_move', {'game_id': game_id, 'move': 'scissors'})
        await asyncio.sleep(0.5)
        await client2.emit('make_move', {'game_id': game_id, 'move': 'paper'})
        await asyncio.sleep(3)
        
        print("\n✅ Game completed successfully!")
        
        # 6. Check final state
        print("\n6️⃣ Checking final state...")
        response = requests.get('http://localhost:8003/api/games')
        if response.status_code == 200:
            games = response.json()
            print(f"📊 Active games: {games['count']}")
            for game in games['games']:
                if game['id'] == game_id:
                    print(f"   Game {game_id}: {game}")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
    
    finally:
        # Cleanup
        await client1.disconnect()
        await client2.disconnect()
        await spectator.disconnect()

async def test_spectator_betting():
    """Test spectator betting functionality."""
    print("\n\n🎰 Testing Spectator Betting")
    print("=" * 30)
    
    # This would be implemented in the next phase
    print("💡 Spectator betting will be implemented in the next phase")
    print("   Features planned:")
    print("   - Bet on round winners")
    print("   - Bet on game winners")
    print("   - Live odds updates")
    print("   - Real-time payout calculations")

if __name__ == "__main__":
    print("🚀 BetBet Rock Paper Scissors Test Suite")
    print("Testing real-time multiplayer functionality...")
    
    asyncio.run(test_complete_game())
    asyncio.run(test_spectator_betting())
