#!/usr/bin/env python3
"""
Test WebSocket client for Socket.IO
"""
import asyncio
import socketio
import json
from datetime import datetime

# Create Socket.IO client
sio = socketio.AsyncClient()

# Event handlers
@sio.event
async def connect():
    print("✅ Connected to WebSocket server")

    # Test basic message
    await sio.emit('test_message', {
        'message': 'Hello from test client!',
        'timestamp': datetime.utcnow().isoformat()
    })

@sio.event
async def disconnect():
    print("❌ Disconnected from WebSocket server")

@sio.event
async def connected(data):
    print(f"📡 Connection confirmed: {data}")

@sio.event
async def test_response(data):
    print(f"📨 Test response: {data}")

@sio.event
async def joined_game(data):
    print(f"🎮 Joined game: {data}")

@sio.event
async def player_joined(data):
    print(f"👤 Player joined: {data}")

@sio.event
async def move_made(data):
    print(f"🎯 Move made: {data}")

@sio.event
async def chat(data):
    print(f"💬 Chat: {data}")

async def test_websocket():
    """Test WebSocket functionality."""
    print("🧪 Testing WebSocket functionality...")

    try:
        # Connect to server
        await sio.connect('http://localhost:8003')

        # Wait for connection
        await asyncio.sleep(1)

        # Test joining a game
        print("🎮 Testing game join...")
        await sio.emit('join_game', {
            'game_id': 'test_rps_game'
        })

        await asyncio.sleep(1)

        # Test game move
        print("🎯 Testing game move...")
        await sio.emit('game_move', {
            'game_id': 'test_rps_game',
            'move': {
                'type': 'choice',
                'choice': 'rock'
            }
        })

        await asyncio.sleep(1)

        # Test chat
        print("💬 Testing chat...")
        await sio.emit('chat_message', {
            'game_id': 'test_rps_game',
            'message': 'Hello from Rock Paper Scissors!'
        })

        await asyncio.sleep(2)

        print("✅ WebSocket test completed successfully!")

    except Exception as e:
        print(f"❌ WebSocket test failed: {e}")

    finally:
        await sio.disconnect()

if __name__ == "__main__":
    asyncio.run(test_websocket())
