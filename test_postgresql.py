#!/usr/bin/env python3
"""
Test PostgreSQL connection directly
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# Direct PostgreSQL connection
DATABASE_URL = "postgresql://postgres:123Bubblegums@localhost:5432/betbet"

print("🔗 Testing PostgreSQL connection...")
print(f"📍 Database URL: {DATABASE_URL}")

try:
    # Create engine
    engine = create_engine(
        DATABASE_URL,
        pool_size=5,
        max_overflow=10,
        pool_pre_ping=True,
        echo=True  # Show SQL queries
    )
    
    # Test connection
    with engine.connect() as conn:
        result = conn.execute(text('SELECT version()'))
        version = result.fetchone()[0]
        print(f"✅ PostgreSQL Version: {version[:100]}...")
        
        # Test database info
        result = conn.execute(text('SELECT current_database(), current_user'))
        db_info = result.fetchone()
        print(f"✅ Database: {db_info[0]}, User: {db_info[1]}")
        
        # Test table creation
        conn.execute(text('''
            CREATE TABLE IF NOT EXISTS test_migration (
                id SERIAL PRIMARY KEY,
                name VARCHAR(100),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        '''))
        conn.commit()
        
        # Insert test data
        conn.execute(text("INSERT INTO test_migration (name) VALUES ('PostgreSQL Migration Test')"))
        conn.commit()
        
        # Query test data
        result = conn.execute(text('SELECT * FROM test_migration ORDER BY id DESC LIMIT 1'))
        test_row = result.fetchone()
        print(f"✅ Test data: ID={test_row[0]}, Name={test_row[1]}, Created={test_row[2]}")
        
        # Clean up
        conn.execute(text('DROP TABLE test_migration'))
        conn.commit()
        
    print("✅ PostgreSQL migration test successful!")
    
except Exception as e:
    print(f"❌ PostgreSQL connection failed: {e}")
    print("💡 Make sure PostgreSQL is running and the database 'betbet' exists")
    print("💡 Create database with: createdb -U postgres betbet")
