#!/usr/bin/env python3
"""
Database Cleanup Script
Removes all test data and users to start fresh
"""
import sys
import os
sys.path.append('.')

from app.database.db import SessionLocal, engine
from app.models.user import User
from app.models.game import Game, GameMove, GameSpectator, GameInvite
from app.models.game_management import GamePlayer, GameChat, GameTournament, TournamentParticipant
from app.models.wallet import Wallet
from app.models.payment import Transaction, PaymentMethod
from app.models.user_profile import UserStats, Notification, Achievement, UserAchievement, Friendship, LeaderboardEntry
from app.models.session import UserSession
from app.models.marketplace import Bet, BetParticipation, CounterBet, BetInvite
from app.models.watchlist import Watchlist
from app.models.expert_picks import ExpertProfile, ExpertPick, PickPack, PickPackItem, PickPurchase, PickPackPurchase, ExpertFollower
from sqlalchemy.orm import Session

def cleanup_database():
    """Clean all data from the database"""
    print("🧹 Starting database cleanup...")

    db = SessionLocal()
    try:
        # Get counts before cleanup
        user_count = db.query(User).count()
        game_count = db.query(Game).count()
        wallet_count = db.query(Wallet).count()

        print(f"📊 Current database state:")
        print(f"   Users: {user_count}")
        print(f"   Games: {game_count}")
        print(f"   Wallets: {wallet_count}")

        if user_count == 0 and game_count == 0:
            print("✅ Database is already clean!")
            return

        # Confirm cleanup
        confirm = input("\n⚠️  This will DELETE ALL DATA. Continue? (yes/no): ")
        if confirm.lower() != 'yes':
            print("❌ Cleanup cancelled.")
            return

        print("\n🗑️  Deleting all data...")

        # Delete in correct order to avoid foreign key constraints

        # 1. Game-related data (delete in dependency order)
        print("   🎮 Deleting game data...")
        db.query(GameMove).delete()
        db.query(GameChat).delete()
        db.query(GameSpectator).delete()
        db.query(GameInvite).delete()
        db.query(GamePlayer).delete()
        db.query(TournamentParticipant).delete()
        db.query(GameTournament).delete()
        db.query(Game).delete()

        # 2. Marketplace/Betting data
        print("   🎯 Deleting marketplace data...")
        db.query(BetParticipation).delete()
        db.query(CounterBet).delete()
        db.query(BetInvite).delete()
        db.query(Bet).delete()

        # 3. Wallet-related data
        print("   💰 Deleting wallet data...")
        db.query(Transaction).delete()
        db.query(PaymentMethod).delete()
        db.query(Wallet).delete()

        # 4. Expert picks data
        print("   🎯 Deleting expert picks data...")
        db.query(PickPackPurchase).delete()
        db.query(PickPurchase).delete()
        db.query(PickPackItem).delete()
        db.query(PickPack).delete()
        db.query(ExpertPick).delete()
        db.query(ExpertFollower).delete()
        db.query(ExpertProfile).delete()

        # 5. Watchlist data
        print("   👀 Deleting watchlist data...")
        db.query(Watchlist).delete()

        # 6. User-related data
        print("   👤 Deleting user data...")
        db.query(Notification).delete()
        db.query(UserSession).delete()
        db.query(UserAchievement).delete()
        db.query(Friendship).delete()
        db.query(LeaderboardEntry).delete()
        db.query(UserStats).delete()
        db.query(Achievement).delete()

        # 7. Users (last)
        print("   🧑‍💼 Deleting users...")
        db.query(User).delete()

        # Commit all deletions
        db.commit()

        # Verify cleanup
        final_user_count = db.query(User).count()
        final_game_count = db.query(Game).count()
        final_wallet_count = db.query(Wallet).count()

        print(f"\n✅ Database cleanup completed!")
        print(f"📊 Final database state:")
        print(f"   Users: {final_user_count}")
        print(f"   Games: {final_game_count}")
        print(f"   Wallets: {final_wallet_count}")

        if final_user_count == 0 and final_game_count == 0:
            print("🎉 Database is now completely clean!")
        else:
            print("⚠️  Some data may still remain.")

    except Exception as e:
        print(f"❌ Error during cleanup: {e}")
        db.rollback()
        raise
    finally:
        db.close()

def reset_auto_increment():
    """Reset auto-increment counters (if any)"""
    print("\n🔄 Resetting auto-increment counters...")

    try:
        # For PostgreSQL, reset sequences
        with engine.connect() as conn:
            # Note: Most of our tables use UUIDs, so this may not be necessary
            # But we'll reset any sequences that exist
            result = conn.execute("""
                SELECT sequence_name FROM information_schema.sequences
                WHERE sequence_schema = 'public'
            """)

            sequences = result.fetchall()
            for seq in sequences:
                seq_name = seq[0]
                conn.execute(f"ALTER SEQUENCE {seq_name} RESTART WITH 1")
                print(f"   ↻ Reset sequence: {seq_name}")

            conn.commit()

        if len(sequences) == 0:
            print("   ℹ️  No sequences to reset (using UUIDs)")
        else:
            print(f"   ✅ Reset {len(sequences)} sequences")

    except Exception as e:
        print(f"   ⚠️  Could not reset sequences: {e}")

def create_fresh_test_user():
    """Create a fresh test user for immediate testing"""
    print("\n👤 Creating fresh test user...")

    db = SessionLocal()
    try:
        from app.core.security import get_password_hash

        # Create thecodking user
        hashed_password = get_password_hash("TestPass123!")

        user = User(
            username="thecodking",
            email="<EMAIL>",
            hashed_password=hashed_password,
            is_active=True,
            is_verified=False
        )

        db.add(user)
        db.commit()
        db.refresh(user)

        print(f"✅ Created user: {user.username} ({user.id})")
        print(f"   Email: {user.email}")
        print(f"   Password: TestPass123!")

        # Create wallet for user
        from app.models.wallet import Wallet
        wallet = Wallet(
            user_id=user.id,
            balance=1000.0  # Give some starting balance
        )

        db.add(wallet)
        db.commit()

        print(f"💰 Created wallet with $1000 balance")

        return user

    except Exception as e:
        print(f"❌ Error creating test user: {e}")
        db.rollback()
        raise
    finally:
        db.close()

def main():
    """Main cleanup function"""
    print("🚀 Database Cleanup Tool")
    print("=" * 50)

    try:
        # Step 1: Clean database
        cleanup_database()

        # Step 2: Reset sequences
        reset_auto_increment()

        # Step 3: Create fresh test user
        create_fresh_test_user()

        print("\n" + "=" * 50)
        print("🎉 Database cleanup completed successfully!")
        print("\n📋 Next steps:")
        print("   1. Restart the server: python main.py")
        print("   2. Clear frontend cache (F12 → Storage → Clear)")
        print("   3. Login with: thecodking / TestPass123!")
        print("   4. Create games and test WebSocket connections")

    except Exception as e:
        print(f"\n❌ Cleanup failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
