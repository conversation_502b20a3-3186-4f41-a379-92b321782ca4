#!/usr/bin/env python3
"""
Test basic server functionality without Socket.IO
"""
import uvicorn
from fastapi import FastAP<PERSON>

# Create a simple test app
app = FastAPI(title="BetBet Test")

@app.get("/")
async def root():
    return {"message": "BetBet Test Server", "status": "running"}

@app.get("/health")
async def health():
    return {"status": "healthy", "test": True}

if __name__ == "__main__":
    print("🧪 Starting test server...")
    uvicorn.run(
        "test_basic_server:app",
        host="0.0.0.0",
        port=8001,  # Use different port
        reload=False
    )
