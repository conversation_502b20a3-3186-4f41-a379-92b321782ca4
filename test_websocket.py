#!/usr/bin/env python3
"""
Test WebSocket connection to our game endpoint
"""
import asyncio
import websockets
import json

async def test_websocket():
    # Game ID from successful creation
    game_id = "97267877-3f79-412c-87e5-924503b49278"

    # Frontend token (from error message)
    token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI2MmVkNjM4ZS1lNWJlLTRmODgtYTQ2Yi1jODg2NzZlNDIyZDIiLCJ1c2VybmFtZSI6InRhcGl3YW5hc2hlIiwic2Vzc2lvbl9pZCI6ImE0ZmVkY2EzLWZjMDEtNGNiYy05ZTY0LWU4MjRiZjI3ZTlmYSIsImV4cCI6MTc0ODI4NjM4MSwiaWF0IjoxNzQ4MTk5OTgxLCJqdGkiOiIzY2E5MjNhYi04ZDdkLTQ0MWQtYjA4ZS1jOTlmYzFkNWNlM2IiLCJ0eXBlIjoiYWNjZXNzIn0.UnNcGLSRpmtNCCziO4E8OydLYhw-4tJ5DU-1-e15WZc"

    uri = f"ws://localhost:8000/ws/game/{game_id}?token={token}"

    try:
        print(f"🔗 Connecting to: {uri}")

        async with websockets.connect(uri) as websocket:
            print("✅ WebSocket connected successfully!")

            # Wait for initial game state
            try:
                message = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                data = json.loads(message)
                print(f"📨 Received initial message: {data['type']}")

                if data['type'] == 'game_state':
                    game_data = data['data']
                    print(f"🎮 Game Type: {game_data['game_type']}")
                    print(f"🎯 Game Status: {game_data['status']}")
                    print(f"👤 Player 1: {game_data['player1_id']}")
                    print(f"👤 Player 2: {game_data.get('player2_id', 'None')}")
                    print(f"🎲 Is Player: {game_data['is_player']}")
                    print(f"🔢 Player Number: {game_data['your_player_number']}")

                    # Debug: Print full state structure
                    print(f"🔍 Full state keys: {list(game_data.get('state', {}).keys()) if game_data.get('state') else 'No state'}")
                    if game_data.get('state'):
                        state = game_data['state']
                        print(f"🔍 State type: {type(state)}")
                        if isinstance(state, dict):
                            for key, value in state.items():
                                if key == 'board':
                                    if isinstance(value, list):
                                        print(f"✅ Board is 2D array: {len(value)}x{len(value[0]) if value else 0}")
                                        print(f"📋 First row: {value[0] if value else 'Empty'}")
                                    else:
                                        print(f"❌ Board is FEN: {str(value)[:50]}...")
                                else:
                                    print(f"🔍 {key}: {type(value)} = {str(value)[:50]}...")
                        else:
                            print(f"⚠️ State is not a dict: {str(state)[:100]}...")

            except asyncio.TimeoutError:
                print("⏰ Timeout waiting for initial message")

            # Send a test chat message
            chat_message = {
                "type": "chat",
                "message": "Hello from WebSocket test!"
            }

            print(f"📤 Sending chat message...")
            await websocket.send(json.dumps(chat_message))

            # Wait for any responses
            try:
                while True:
                    message = await asyncio.wait_for(websocket.recv(), timeout=2.0)
                    data = json.loads(message)
                    print(f"📨 Received: {data}")
            except asyncio.TimeoutError:
                print("⏰ No more messages received")

    except websockets.exceptions.ConnectionClosed as e:
        print(f"❌ Connection closed: {e}")
    except websockets.exceptions.InvalidStatusCode as e:
        print(f"❌ Invalid status code: {e}")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    print("🚀 Testing WebSocket connection...")
    asyncio.run(test_websocket())
