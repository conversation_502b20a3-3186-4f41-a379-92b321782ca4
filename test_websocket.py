#!/usr/bin/env python3
"""
Quick test script to verify WebSocket functionality.
Run this after starting the server to test basic connectivity.
"""
import asyncio
import socketio
import json
from datetime import datetime

# Test configuration
SERVER_URL = "http://localhost:8000"
TEST_TOKEN = "test_token_123"  # We'll need a real token for actual testing

async def test_websocket_connection():
    """Test basic WebSocket connection and events."""
    print("🧪 Testing WebSocket Connection...")
    
    # Create Socket.IO client
    sio = socketio.AsyncClient()
    
    # Event handlers
    @sio.event
    async def connect():
        print("✅ Connected to WebSocket server")
        
        # Test joining a game
        await sio.emit('join_game', {
            'game_id': 'test_game_123'
        })
    
    @sio.event
    async def disconnect():
        print("❌ Disconnected from WebSocket server")
    
    @sio.event
    async def connected(data):
        print(f"📡 Connection confirmed: {data}")
    
    @sio.event
    async def game_update(data):
        print(f"🎮 Game update received: {data}")
    
    @sio.event
    async def error(data):
        print(f"❌ Error received: {data}")
    
    @sio.event
    async def player_joined(data):
        print(f"👤 Player joined: {data}")
    
    try:
        # Connect with authentication
        await sio.connect(SERVER_URL, auth={'token': TEST_TOKEN})
        
        # Wait a bit for connection to establish
        await asyncio.sleep(2)
        
        # Test sending a game move
        print("🎯 Testing game move...")
        await sio.emit('game_move', {
            'game_id': 'test_game_123',
            'move': {
                'type': 'choice',
                'choice': 'rock'
            }
        })
        
        # Wait for responses
        await asyncio.sleep(3)
        
        # Test chat message
        print("💬 Testing chat message...")
        await sio.emit('chat_message', {
            'game_id': 'test_game_123',
            'message': 'Hello from test script!'
        })
        
        # Wait for responses
        await asyncio.sleep(2)
        
        print("✅ WebSocket test completed")
        
    except Exception as e:
        print(f"❌ WebSocket test failed: {e}")
    
    finally:
        await sio.disconnect()

def test_server_running():
    """Test if the server is running."""
    import requests
    try:
        response = requests.get(f"{SERVER_URL}/health", timeout=5)
        if response.status_code == 200:
            print("✅ Server is running")
            return True
        else:
            print(f"❌ Server returned status {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Server is not running: {e}")
        return False

if __name__ == "__main__":
    print("🚀 BetBet WebSocket Test Suite")
    print("=" * 40)
    
    # Test server
    if test_server_running():
        # Test WebSocket
        asyncio.run(test_websocket_connection())
    else:
        print("❌ Cannot test WebSocket - server is not running")
        print("💡 Start the server with: python main.py")
