#!/usr/bin/env python3
"""
Test WebSocket connection to our game endpoint
"""
import asyncio
import websockets
import json

async def test_websocket():
    # Real game ID from thecodking user (latest)
    game_id = "4dcc4d78-cc2a-417c-a4c0-85689768513a"

    # Real token from thecodking user (matches frontend)
    token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIyNTdiMWI0OS1jNTA3LTQxNDUtOGI4Ny0wMWQwMTZiMzA1YzciLCJ1c2VybmFtZSI6InRoZWNvZGtpbmciLCJzZXNzaW9uX2lkIjoiZDE5MmI0MmItN2M0ZC00NDY4LWFmOTYtOGE3MTk2MDBhM2NiIiwiZXhwIjoxNzQ4Mjg0NjgxLCJpYXQiOjE3NDgxOTgyODEsImp0aSI6IjEwM2Y2MGY1LTJhNjYtNDQyNC05M2M4LTZiNjEyYzNkZDNjZiIsInR5cGUiOiJhY2Nlc3MifQ.ZVHpqDC6rPn8GseV0pp9cWuUB9hekFo7A_yRwt7wlxE"

    uri = f"ws://localhost:8000/ws/game/{game_id}?token={token}"

    try:
        print(f"🔗 Connecting to: {uri}")

        async with websockets.connect(uri) as websocket:
            print("✅ WebSocket connected successfully!")

            # Wait for initial game state
            try:
                message = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                data = json.loads(message)
                print(f"📨 Received initial message: {data['type']}")

                if data['type'] == 'game_state':
                    game_data = data['data']
                    print(f"🎮 Game Type: {game_data['game_type']}")
                    print(f"🎯 Game Status: {game_data['status']}")
                    print(f"👤 Player 1: {game_data['player1_id']}")
                    print(f"👤 Player 2: {game_data.get('player2_id', 'None')}")
                    print(f"🎲 Is Player: {game_data['is_player']}")
                    print(f"🔢 Player Number: {game_data['your_player_number']}")

            except asyncio.TimeoutError:
                print("⏰ Timeout waiting for initial message")

            # Send a test chat message
            chat_message = {
                "type": "chat",
                "message": "Hello from WebSocket test!"
            }

            print(f"📤 Sending chat message...")
            await websocket.send(json.dumps(chat_message))

            # Wait for any responses
            try:
                while True:
                    message = await asyncio.wait_for(websocket.recv(), timeout=2.0)
                    data = json.loads(message)
                    print(f"📨 Received: {data}")
            except asyncio.TimeoutError:
                print("⏰ No more messages received")

    except websockets.exceptions.ConnectionClosed as e:
        print(f"❌ Connection closed: {e}")
    except websockets.exceptions.InvalidStatusCode as e:
        print(f"❌ Invalid status code: {e}")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    print("🚀 Testing WebSocket connection...")
    asyncio.run(test_websocket())
