// Type verification test to ensure all imports work correctly
import type { 
  League, 
  Sport, 
  Team, 
  Match, 
  Bet, 
  SportEvent, 
  GameOdds 
} from './types/sports';

// Test that all types are properly imported and can be used
const testLeague: League = {
  id: 'test-league',
  name: 'Test League',
  sport: 'football',
  country: 'Test Country',
  logo: 'test-logo.png',
  season: '2024',
  isActive: true
};

const testSport: Sport = {
  id: 'test-sport',
  name: 'Test Sport',
  category: 'team',
  isActive: true,
  leagues: [testLeague]
};

console.log('✅ All sports types imported successfully!');
console.log('League:', testLeague);
console.log('Sport:', testSport);

export { testLeague, testSport };
