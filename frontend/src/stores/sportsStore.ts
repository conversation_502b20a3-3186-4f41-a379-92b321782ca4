import { create } from 'zustand';
import type { SportEvent, Sport } from '../types';
import sportsAPI from '../services/sports';

interface Bet {
  id?: string;
  matchId: string;
  type: 'home' | 'draw' | 'away';
  amount: number;
  odds: number;
  timestamp?: Date;
  status?: 'pending' | 'won' | 'lost';
}

interface SportsState {
  sports: Sport[];
  liveEvents: SportEvent[];
  upcomingEvents: SportEvent[];
  allEvents: SportEvent[];
  activeBets: Bet[];
  isLoading: boolean;
  error: string | null;
  selectedSport: string | null;

  // Actions
  setSports: (sports: Sport[]) => void;
  setLiveEvents: (events: SportEvent[]) => void;
  setUpcomingEvents: (events: SportEvent[]) => void;
  setAllEvents: (events: SportEvent[]) => void;
  setActiveBets: (bets: Bet[]) => void;
  placeBet: (bet: Bet) => void;
  setLoading: (isLoading: boolean) => void;
  setError: (error: string | null) => void;
  setSelectedSport: (sport: string | null) => void;

  // API calls
  fetchSports: () => Promise<void>;
  fetchLiveEvents: (sportId?: string) => Promise<void>;
  fetchUpcomingEvents: (sportId?: string, limit?: number) => Promise<void>;
  fetchAllEvents: (filters?: { sport?: string; status?: 'live' | 'upcoming'; limit?: number }) => Promise<void>;
  refreshData: () => Promise<void>;
}

const useSportsStore = create<SportsState>((set, get) => ({
  sports: [],
  liveEvents: [],
  upcomingEvents: [],
  allEvents: [],
  activeBets: [],
  isLoading: false,
  error: null,
  selectedSport: null,

  // Basic setters
  setSports: (sports) => set({ sports }),
  setLiveEvents: (events) => set({ liveEvents: events }),
  setUpcomingEvents: (events) => set({ upcomingEvents: events }),
  setAllEvents: (events) => set({ allEvents: events }),
  setActiveBets: (bets) => set({ activeBets: bets }),
  placeBet: (bet) =>
    set((state) => ({
      activeBets: [...state.activeBets, { ...bet, timestamp: new Date(), status: 'pending' }],
    })),
  setLoading: (isLoading) => set({ isLoading }),
  setError: (error) => set({ error }),
  setSelectedSport: (sport) => set({ selectedSport: sport }),

  // API calls
  fetchSports: async () => {
    set({ isLoading: true, error: null });
    try {
      const sports = await sportsAPI.getSports();
      set({ sports, isLoading: false });
    } catch (error) {
      console.error('Failed to fetch sports:', error);
      set({ error: 'Failed to load sports', isLoading: false });
    }
  },

  fetchLiveEvents: async (sportId?: string) => {
    set({ isLoading: true, error: null });
    try {
      const events = await sportsAPI.getLiveGames(sportId);
      set({ liveEvents: events, isLoading: false });
    } catch (error) {
      console.error('Failed to fetch live events:', error);
      set({ error: 'Failed to load live events', isLoading: false });
    }
  },

  fetchUpcomingEvents: async (sportId?: string, limit: number = 20) => {
    set({ isLoading: true, error: null });
    try {
      const events = await sportsAPI.getUpcomingGames(sportId, undefined, limit);
      set({ upcomingEvents: events, isLoading: false });
    } catch (error) {
      console.error('Failed to fetch upcoming events:', error);
      set({ error: 'Failed to load upcoming events', isLoading: false });
    }
  },

  fetchAllEvents: async (filters?: { sport?: string; status?: 'live' | 'upcoming'; limit?: number }) => {
    set({ isLoading: true, error: null });
    try {
      const events = await sportsAPI.getAllEvents(filters);
      set({ allEvents: events, isLoading: false });

      // Also update live and upcoming events based on status
      const liveEvents = events.filter(e => e.status === 'live');
      const upcomingEvents = events.filter(e => e.status === 'upcoming');
      set({ liveEvents, upcomingEvents });
    } catch (error) {
      console.error('Failed to fetch events:', error);
      set({ error: 'Failed to load events', isLoading: false });
    }
  },

  refreshData: async () => {
    const { selectedSport } = get();
    await Promise.all([
      get().fetchSports(),
      get().fetchAllEvents({ sport: selectedSport || undefined })
    ]);
  }
}));

export default useSportsStore;
