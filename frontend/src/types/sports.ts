export interface Match {
  id: string;
  sport: string;
  league: string;
  homeTeam: string;
  awayTeam: string;
  homeScore?: number;
  awayScore?: number;
  startTime: string;
  status: MatchStatus;
  odds: MatchOdds;
  venue?: string;
  weather?: string;
  createdAt: string;
  updatedAt: string;
}

export type MatchStatus = 'scheduled' | 'live' | 'halftime' | 'completed' | 'cancelled' | 'postponed';

export interface MatchOdds {
  home: number;
  draw: number;
  away: number;
  overUnder?: {
    over: number;
    under: number;
    line: number;
  };
  spread?: {
    home: number;
    away: number;
    line: number;
  };
  updatedAt?: string;
}

export interface Bet {
  id?: string;
  userId: string;
  matchId: string;
  type: BetType;
  selection: BetSelection;
  amount: number;
  odds: number;
  potentialWinnings: number;
  status: BetStatus;
  settledAt?: string;
  createdAt: string;
}

export type BetType = 'moneyline' | 'spread' | 'over_under' | 'parlay';

export type BetSelection = 'home' | 'away' | 'draw' | 'over' | 'under';

export type BetStatus = 'pending' | 'won' | 'lost' | 'cancelled' | 'refunded';

export interface Sport {
  id: string;
  name: string;
  icon: string;
  active: boolean;
}

export interface League {
  id: string;
  sportId: string;
  name: string;
  country: string;
  logoUrl?: string;
  activeMatches: number;
}

export interface Team {
  id: string;
  name: string;
  shortName: string;
  country: string;
  logoUrl?: string;
  sport: string;
  league: string;
}

export interface MatchEvent {
  id: string;
  matchId: string;
  type: EventType;
  minute: number;
  teamId: string;
  playerId?: string;
  playerName?: string;
  description?: string;
  timestamp: string;
}

export type EventType = 'goal' | 'yellow_card' | 'red_card' | 'substitution' | 'penalty' | 'var' | 'injury' | 'other';

// Explicit re-export to ensure proper module resolution
export type {
  League,
  Sport,
  Team,
  Match,
  Bet,
  MatchStatus,
  MatchOdds,
  BetType,
  BetSelection,
  BetStatus,
  EventType,
  MatchEvent
};

// New interfaces for API integration
export interface SportEvent {
  id: string;
  sport: string;
  league: string;
  homeTeam: {
    name: string;
    logo?: string | null;
  };
  awayTeam: {
    name: string;
    logo?: string | null;
  };
  startTime: Date;
  status: 'upcoming' | 'live' | 'finished';
  odds: {
    home: number;
    away: number;
    draw?: number | null;
  };
  minute?: string | null;
  score?: string | null;
  viewers?: number;
  featured: boolean;
  trending: boolean;
}

export interface GameOdds {
  gameId: string;
  moneyline?: {
    home: number;
    away: number;
    draw?: number;
  };
  spread?: {
    home: number;
    away: number;
    line: number;
  };
  total?: {
    over: number;
    under: number;
    line: number;
  };
  updatedAt: Date;
}
