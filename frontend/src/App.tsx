import { BrowserRouter as Router, Routes, Route, Navigate, useLocation, Outlet } from 'react-router-dom';
import { useState, useEffect } from 'react';
import useUserStore from './stores/userStore';
import { startNotificationPolling, stopNotificationPolling } from './services/notifications';

// Components
import CompactHeader from './components/shared/CompactHeader';
import LoadingSpinner from './components/shared/LoadingSpinner';
import LandingPage from './components/landing/LandingPage';

// Auth components
import LoginForm from './components/auth/LoginForm';
import RegisterForm from './components/auth/RegisterForm';
import ProtectedRoute from './components/auth/ProtectedRoute';

// Import Dashboard component
import DashboardComponent from './components/dashboard/Dashboard';

// Import Marketplace components
import { BetMarketplace, BetDetailsPage } from './components/marketplace';

// Import Games components
import Games from './components/games';
import GameSessionRouter from './components/games/GameSessionRouter';
import GameLobby from './components/games/GameLobby';

// Import Wallet component
import Wallet from './components/wallet';

// Import Sports component
import { SportsPageLive, MobileSportsPage } from './components/sports';

// Import Leaderboard component
import Leaderboard from './components/leaderboard';

// Import Profile components
import { ProfilePage, MobileProfilePage } from './components/profile';

// Import Admin components
import AdminLayout from './components/admin/AdminLayout';
import AdminDashboard from './components/admin/AdminDashboard';
import KYCDashboard from './components/admin/KYCDashboard';

// Import Expert Picks component
import { ExpertPicks, MobileExpertPicks } from './components/expert-picks';

// Import route configuration
import { ROUTES } from './config/routes';

// Wrapper components for responsive views
const Dashboard = () => <DashboardComponent />;
const Sports = ({ sport }: { sport?: string }) => {
  const isMobile = window.innerWidth < 768;
  return isMobile ? <MobileSportsPage sport={sport} /> : <SportsPageLive />;
};
const Profile = () => {
  const isMobile = window.innerWidth < 768;
  return isMobile ? <MobileProfilePage onBack={() => window.history.back()} /> : <ProfilePage />;
};
const Marketplace = () => <BetMarketplace />;
const ExpertPicksWrapper = () => {
  const isMobile = window.innerWidth < 768;
  return isMobile ? <MobileExpertPicks /> : <ExpertPicks />;
};

// Layout wrapper that includes header and footer
const AppLayout = ({ children }: { children: React.ReactNode }) => {
  const { isAuthenticated, user, logout } = useUserStore();
  const location = useLocation();

  // Don't show header/footer on landing page
  const isLandingPage = location.pathname === '/';

  if (isLandingPage) {
    return <>{children}</>;
  }

  return (
    <div className="min-h-screen bg-slate-900 flex flex-col">
      <CompactHeader
        isAuthenticated={isAuthenticated}
        username={user?.username}
        balance={1250.00}
        betslipCount={3}
        onLogout={logout}
      />
      <main className="flex-1">
        {children}
      </main>
    </div>
  );
};

function App() {
  const { isAuthenticated, user } = useUserStore();
  const [isLoading, setIsLoading] = useState(true);

  // Check auth status on app load
  useEffect(() => {
    const checkAuth = async () => {
      // In a real app, you would verify the token with the backend
      // For now, we just read from the store which is already hydrated from localStorage
      setTimeout(() => {
        setIsLoading(false);
      }, 1000);
    };

    checkAuth();
  }, []);

  // Start/stop notification polling based on auth status
  useEffect(() => {
    if (isAuthenticated) {
      startNotificationPolling();
    } else {
      stopNotificationPolling();
    }

    // Cleanup on unmount
    return () => {
      stopNotificationPolling();
    };
  }, [isAuthenticated]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen bg-slate-900">
        <LoadingSpinner size="large" centered text="Loading BetBet..." />
      </div>
    );
  }

  return (
    <Router>
      <Routes>
        {/* Landing page (public) */}
        <Route
          path={ROUTES.HOME}
          element={isAuthenticated ? <Navigate to={ROUTES.DASHBOARD} /> : <LandingPage />}
        />

        {/* Auth routes */}
        <Route
          path={ROUTES.LOGIN}
          element={
            isAuthenticated ?
            <Navigate to={ROUTES.DASHBOARD} /> :
            <AppLayout><LoginForm /></AppLayout>
          }
        />
        <Route
          path={ROUTES.REGISTER}
          element={
            isAuthenticated ?
            <Navigate to={ROUTES.DASHBOARD} /> :
            <AppLayout><RegisterForm /></AppLayout>
          }
        />

        {/* Public routes with layout */}
        <Route
          path={ROUTES.LEADERBOARD}
          element={
            <AppLayout><Leaderboard /></AppLayout>
          }
        />

        {/* Protected routes */}
        <Route
          path={ROUTES.DASHBOARD}
          element={
            <ProtectedRoute isAuthenticated={isAuthenticated}>
              <AppLayout><Dashboard /></AppLayout>
            </ProtectedRoute>
          }
        />

        {/* Games routes - Three-tier hierarchy */}
        <Route
          path="/games"
          element={
            <ProtectedRoute isAuthenticated={isAuthenticated}>
              <AppLayout><Outlet /></AppLayout>
            </ProtectedRoute>
          }
        >
          {/* Tier 1: Games Catalog */}
          <Route index element={<Games />} />

          {/* Tier 2: Game Instances Pages */}
          <Route path=":gameSlug" element={<Games />} />

          {/* Tier 3: Game Sessions */}
          <Route path=":gameSlug/session/:sessionId" element={<GameSessionRouter />} />
          <Route path=":gameSlug/spectate/:sessionId" element={<Games />} />

          {/* Legacy routes for backward compatibility */}
          <Route path="lobby/:gameType" element={<GameLobby />} />
        </Route>

        {/* Sports routes with nested structure */}
        <Route
          path="/sports"
          element={
            <ProtectedRoute isAuthenticated={isAuthenticated}>
              <AppLayout><Outlet /></AppLayout>
            </ProtectedRoute>
          }
        >
          <Route index element={<Sports />} />
          <Route path="football" element={<Sports sport="football" />} />
          <Route path="basketball" element={<Sports sport="basketball" />} />
          <Route path="tennis" element={<Sports sport="tennis" />} />
          <Route path="cricket" element={<Sports sport="cricket" />} />
          <Route path=":sport/:matchId" element={<Sports />} />
          <Route path=":sport/:matchId/bet/:betId" element={<Sports />} />
        </Route>

        {/* Marketplace routes with nested structure */}
        <Route
          path="/marketplace"
          element={
            <ProtectedRoute isAuthenticated={isAuthenticated}>
              <AppLayout><Outlet /></AppLayout>
            </ProtectedRoute>
          }
        >
          <Route index element={<Marketplace />} />
          <Route path="bet/:betId" element={<BetDetailsPage />} />
          <Route path="create" element={<Marketplace />} />
          <Route path="user/:userId" element={<Marketplace />} />
          <Route path="category/:category" element={<Marketplace />} />
        </Route>

        {/* Expert picks routes with nested structure */}
        <Route
          path="/expert-picks"
          element={
            <ProtectedRoute isAuthenticated={isAuthenticated}>
              <AppLayout><Outlet /></AppLayout>
            </ProtectedRoute>
          }
        >
          <Route index element={<ExpertPicksWrapper />} />
          <Route path="expert/:expertId" element={<ExpertPicksWrapper />} />
          <Route path="category/:category" element={<ExpertPicksWrapper />} />
          <Route path="pick/:pickId" element={<ExpertPicksWrapper />} />
        </Route>

        {/* Profile route */}
        <Route
          path={ROUTES.PROFILE}
          element={
            <ProtectedRoute isAuthenticated={isAuthenticated}>
              <AppLayout><Profile /></AppLayout>
            </ProtectedRoute>
          }
        />

        {/* Wallet route */}
        <Route
          path={ROUTES.WALLET}
          element={
            <ProtectedRoute isAuthenticated={isAuthenticated}>
              <AppLayout><Wallet /></AppLayout>
            </ProtectedRoute>
          }
        />

        {/* Admin routes with nested structure */}
        <Route
          path="/admin"
          element={
            <ProtectedRoute isAuthenticated={isAuthenticated}>
              {user?.is_superuser ? <AdminLayout /> : <Navigate to={ROUTES.DASHBOARD} />}
            </ProtectedRoute>
          }
        >
          <Route index element={<Navigate to="/admin/dashboard" />} />
          <Route path="dashboard" element={<AdminDashboard />} />
          <Route path="kyc" element={<KYCDashboard />} />
          <Route path="kyc/user/:userId" element={<KYCDashboard />} />
          <Route path="users" element={<div>User Management - Coming Soon</div>} />
          <Route path="users/:userId" element={<div>User Detail - Coming Soon</div>} />
          <Route path="settings" element={<div>Settings - Coming Soon</div>} />
          <Route path="games" element={<div>Game Management - Coming Soon</div>} />
          <Route path="games/sessions" element={<div>Game Sessions - Coming Soon</div>} />
          <Route path="reports" element={<div>Reports - Coming Soon</div>} />
        </Route>

        {/* Fallback route */}
        <Route path="*" element={<Navigate to={ROUTES.HOME} replace />} />
      </Routes>
    </Router>
  );
}

export default App;