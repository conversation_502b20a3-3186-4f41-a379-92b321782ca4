import api from './api';

export interface GamePlayer {
  user_id: string;
  username: string;
  player_number: number;
  amount_wagered: number;
  status: string;
  joined_at?: string;
  is_host: boolean;
}

export interface GameInstance {
  id: string;
  game_type: string;
  status: string;
  wager_amount: number;
  total_pot: number;
  max_players: number;
  current_players: number;
  game_state: any;
  settings: any;
  current_player_id?: string;
  host_id?: string;
  host_username?: string;
  created_at: string;
  started_at?: string;
  completed_at?: string;
  players: GamePlayer[];
  winner_id?: string;
  is_player?: boolean;
}

export interface CreateGameRequest {
  game_type: string;
  wager_amount: number;
  max_players?: number;
  settings?: any;
  is_private?: boolean;
  invited_user_ids?: string[];
}

export interface JoinGameRequest {
  amount_wagered: number;
}

export interface MakeMoveRequest {
  move_data: any;
}

export const gameInstancesService = {
  // Get all game instances (available to join)
  getGames: async (game_type?: string, status?: string): Promise<GameInstance[]> => {
    const params = new URLSearchParams();
    if (game_type) params.append('game_type', game_type);
    if (status) params.append('status', status);

    const queryString = params.toString();
    const url = `/api/game-sessions/available${queryString ? `?${queryString}` : ''}`;

    const response = await api.get(url);
    return response.data.games || response.data;
  },

  // Get a specific game instance
  getGame: async (gameId: string): Promise<GameInstance> => {
    const response = await api.get(`/api/game-sessions/${gameId}`);
    return response.data;
  },

  // Create a new game instance
  createGame: async (data: CreateGameRequest): Promise<GameInstance> => {
    const response = await api.post('/api/game-sessions/create', {
      game_type: data.game_type,
      wager_amount: data.wager_amount,
      max_players: data.max_players || 2,
      settings: data.settings || {},
      is_private: data.is_private || false
    });
    return response.data;
  },

  // Join a game instance
  joinGame: async (gameId: string, data?: JoinGameRequest): Promise<any> => {
    const response = await api.post('/api/game-sessions/join', {
      game_id: gameId
    });
    return response.data;
  },

  // Make a move in a game
  makeMove: async (gameId: string, data: MakeMoveRequest): Promise<any> => {
    const response = await api.post(`/api/game-sessions/${gameId}/move`, {
      move_data: data.move_data
    });
    return response.data;
  },

  // Get my active games (placeholder - would need new endpoint)
  getMyGames: async (): Promise<GameInstance[]> => {
    // For now, get all available games and filter
    // TODO: Create dedicated endpoint for user's games
    const response = await api.get('/api/game-sessions/available');
    return response.data.games || [];
  }
};