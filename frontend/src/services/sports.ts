import api from './api';
import { Sport, SportEvent, League, GameOdds } from '../types/sports';

export const sportsAPI = {
  // Get all available sports from the live API
  getSports: async (): Promise<Sport[]> => {
    try {
      const response = await api.get('/api/sports/sports-live');
      // Transform the API response to match our frontend format
      return response.data.map((sport: any) => ({
        id: sport.sportID.toLowerCase(),
        name: sport.name,
        icon: getIconForSport(sport.sportID),
        active: sport.enabled
      }));
    } catch (error) {
      console.error('Failed to fetch sports:', error);
      // Fallback to mock data if API fails
      return getMockSports();
    }
  },

  // Get leagues for a specific sport
  getLeagues: async (sportId: string): Promise<League[]> => {
    try {
      const response = await api.get(`/api/sports/leagues-live/${sportId.toUpperCase()}`);
      return response.data;
    } catch (error) {
      console.error('Failed to fetch leagues:', error);
      return [];
    }
  },

  // Get upcoming games
  getUpcomingGames: async (sportId?: string, leagueId?: string, limit: number = 20): Promise<SportEvent[]> => {
    try {
      const params = new URLSearchParams();
      if (sportId) params.append('sport_id', sportId.toUpperCase());
      if (leagueId) params.append('league_id', leagueId);
      params.append('limit', limit.toString());
      
      const response = await api.get(`/api/sports/games-live/upcoming?${params}`);
      return transformGamesToEvents(response.data);
    } catch (error) {
      console.error('Failed to fetch upcoming games:', error);
      return [];
    }
  },

  // Get live games
  getLiveGames: async (sportId?: string): Promise<SportEvent[]> => {
    try {
      const params = sportId ? `?sport_id=${sportId.toUpperCase()}` : '';
      const response = await api.get(`/api/sports/games-live/live${params}`);
      return transformGamesToEvents(response.data);
    } catch (error) {
      console.error('Failed to fetch live games:', error);
      return [];
    }
  },

  // Get odds for a specific game
  getGameOdds: async (gameId: string): Promise<GameOdds | null> => {
    try {
      const response = await api.get(`/api/sports/games-live/${gameId}/odds`);
      return response.data;
    } catch (error) {
      console.error('Failed to fetch game odds:', error);
      return null;
    }
  },

  // Get all events (combines live and upcoming)
  getAllEvents: async (filters?: {
    sport?: string;
    status?: 'live' | 'upcoming';
    league?: string;
    limit?: number;
  }): Promise<SportEvent[]> => {
    const allEvents: SportEvent[] = [];
    
    // Fetch live games if needed
    if (!filters?.status || filters.status === 'live') {
      const liveGames = await sportsAPI.getLiveGames(filters?.sport);
      allEvents.push(...liveGames);
    }
    
    // Fetch upcoming games if needed
    if (!filters?.status || filters.status === 'upcoming') {
      const upcomingGames = await sportsAPI.getUpcomingGames(
        filters?.sport,
        filters?.league,
        filters?.limit || 20
      );
      allEvents.push(...upcomingGames);
    }
    
    // Sort by start time
    return allEvents.sort((a, b) => 
      new Date(a.startTime).getTime() - new Date(b.startTime).getTime()
    );
  }
};

// Helper function to transform API game data to frontend event format
function transformGamesToEvents(games: any[]): SportEvent[] {
  return games.map((game: any) => ({
    id: game.gameID || game.id,
    sport: game.sport?.toLowerCase() || 'unknown',
    league: game.league || game.competition || 'Unknown League',
    homeTeam: {
      name: game.homeTeam || game.home_team || 'Home Team',
      logo: game.homeTeamLogo || null
    },
    awayTeam: {
      name: game.awayTeam || game.away_team || 'Away Team',
      logo: game.awayTeamLogo || null
    },
    startTime: new Date(game.startTime || game.start_time || Date.now()),
    status: determineGameStatus(game),
    odds: {
      home: game.odds?.home || game.homeOdds || -110,
      away: game.odds?.away || game.awayOdds || +100,
      draw: game.odds?.draw || game.drawOdds || null
    },
    minute: game.minute || game.period || null,
    score: game.score || (game.homeScore && game.awayScore ? `${game.homeScore}-${game.awayScore}` : null),
    viewers: game.viewers || Math.floor(Math.random() * 10000), // Mock viewers if not provided
    featured: game.featured || false,
    trending: game.trending || game.betVolume > 50000 || false
  }));
}

// Helper function to determine game status
function determineGameStatus(game: any): 'upcoming' | 'live' | 'finished' {
  if (game.status) {
    const status = game.status.toLowerCase();
    if (status.includes('live') || status.includes('in_progress')) return 'live';
    if (status.includes('finished') || status.includes('completed')) return 'finished';
  }
  
  // Check if game has started based on time
  const startTime = new Date(game.startTime || game.start_time);
  const now = new Date();
  
  if (game.homeScore !== undefined || game.awayScore !== undefined) {
    return game.finished ? 'finished' : 'live';
  }
  
  return startTime > now ? 'upcoming' : 'live';
}

// Helper function to get icon for sport
function getIconForSport(sportId: string): string {
  const icons: Record<string, string> = {
    'FOOTBALL': '🏈',
    'BASKETBALL': '🏀',
    'BASEBALL': '⚾',
    'SOCCER': '⚽',
    'HOCKEY': '🏒',
    'TENNIS': '🎾',
    'MMA': '🥊',
    'BOXING': '🥊',
    'GOLF': '⛳',
    'CRICKET': '🏏',
    'RUGBY': '🏉',
    'MOTORSPORTS': '🏎️',
    'ESPORTS': '🎮'
  };
  
  return icons[sportId.toUpperCase()] || '🏆';
}

// Mock data fallback
function getMockSports(): Sport[] {
  return [
    { id: 'football', name: 'Football', icon: '🏈', active: true },
    { id: 'basketball', name: 'Basketball', icon: '🏀', active: true },
    { id: 'baseball', name: 'Baseball', icon: '⚾', active: true },
    { id: 'soccer', name: 'Soccer', icon: '⚽', active: true },
    { id: 'hockey', name: 'Hockey', icon: '🏒', active: true },
    { id: 'tennis', name: 'Tennis', icon: '🎾', active: true },
    { id: 'mma', name: 'MMA / UFC', icon: '🥊', active: true },
    { id: 'boxing', name: 'Boxing', icon: '🥊', active: true },
    { id: 'motorsports', name: 'Motor Sports', icon: '🏎️', active: false },
  ];
}

export default sportsAPI;