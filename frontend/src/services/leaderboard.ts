import api from './api';

export interface Badge {
  id: string;
  name: string;
  description: string;
  icon: string;
  color: string;
}

export interface RecentGame {
  game: string;
  result: 'win' | 'loss';
  profit: number;
  opponent?: string;
  time: string;
}

export interface Player {
  id: string;
  username: string;
  rank: number;
  winrate: number;
  earnings: number;
  streak: number;
  level: number;
  xp: number;
  badges: string[];
  favoriteGame: string;
  achievements: number;
  trophies: number;
  isOnline: boolean;
  country: string;
  winStreakDays: number;
  totalGamesPlayed: number;
  gamesWon: number;
  bestGame: string;
  dailyProfit: number;
  weeklyProfit: number;
  monthlyProfit: number;
  recentPerformance: 'improving' | 'declining' | 'stable';
  recentGames: RecentGame[];
}

export interface MatchPot {
  id: string;
  game: string;
  players: number;
  totalPot: number;
  entryFee: number;
  startTime: string;
  status: 'waiting' | 'in-progress' | 'completed';
  format: string;
  prizesDistribution: number[];
  currentPlayers: string[];
  gameType: string;
  duration: string;
}

export interface LeaderboardResponse {
  entries: Player[];
  matchPots: MatchPot[];
  stats: {
    totalPlayers: number;
    prizePool: number;
    activeGames: number;
    avgBet: number;
  };
}

export interface LeaderboardFilters {
  game?: string;
  timeframe?: 'alltime' | 'monthly' | 'weekly' | 'daily';
  league?: 'all' | 'titan' | 'masters' | 'diamond';
  limit?: number;
}

export const leaderboardService = {
  async getLeaderboard(filters: LeaderboardFilters = {}): Promise<LeaderboardResponse> {
    const params = new URLSearchParams();
    
    if (filters.game) params.append('game', filters.game);
    if (filters.timeframe) params.append('timeframe', filters.timeframe);
    if (filters.league) params.append('league', filters.league);
    if (filters.limit) params.append('limit', filters.limit.toString());
    
    const response = await api.get(`/leaderboard?${params.toString()}`);
    return response.data;
  },

  async getPlayerDetails(playerId: string): Promise<Player> {
    const response = await api.get(`/leaderboard/player/${playerId}`);
    return response.data;
  },
};

export default leaderboardService;