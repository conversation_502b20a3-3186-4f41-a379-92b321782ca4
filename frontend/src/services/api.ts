import axios, { type InternalAxiosRequestConfig, type AxiosResponse, type AxiosError } from 'axios';
import useUserStore from '../stores/userStore';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000';

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for adding token
api.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    const state = useUserStore.getState();
    const token = state.token;
    // console.log('Axios Interceptor - Token exists:', !!token);
    // console.log('Axios Interceptor - URL:', config.url);
    // console.log('Axios Interceptor - Method:', config.method);
    // console.log('Axios Interceptor - Headers before:', config.headers);

    if (token && config.headers) {
      config.headers.Authorization = `Bearer ${token}`;
      // console.log('Axios Interceptor - Added auth header, headers after:', config.headers);
    } else {
      // console.log('Axios Interceptor - No token:', !token, 'or no headers:', !config.headers);
    }
    return config;
  },
  (error: AxiosError) => {
    // console.error('Axios Request Interceptor Error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor for handling token refresh
api.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  async (error: AxiosError) => {
    const originalRequest = error.config;

    // If error is 401 and we have a refresh token, try to refresh
    if (
      error.response?.status === 401 &&
      useUserStore.getState().refreshToken &&
      originalRequest &&
      !(originalRequest as any)._retry
    ) {
      (originalRequest as any)._retry = true;

      try {
        // Call refresh token endpoint
        const response = await axios.post(`${API_BASE_URL}/api/auth/refresh`, {
          refresh_token: useUserStore.getState().refreshToken,
        });

        // Update tokens in store
        const { access_token, refresh_token } = response.data;
        useUserStore.getState().setTokens(access_token, refresh_token);

        // Retry original request with new token
        if (originalRequest.headers) {
          originalRequest.headers.Authorization = `Bearer ${access_token}`;
        }
        return axios(originalRequest);
      } catch (refreshError) {
        // If refresh fails, logout user
        useUserStore.getState().logout();
        return Promise.reject(refreshError);
      }
    }

    return Promise.reject(error);
  }
);

// API endpoints
export const authAPI = {
  register: (userData: any) => {
    // Extract only the fields the backend expects
    const { username, email, password } = userData;
    return api.post('/api/auth/register', { username, email, password });
  },
  login: (credentials: any) =>
    api.post('/api/auth/login',
      new URLSearchParams({
        'username': credentials.email,
        'password': credentials.password
      }),
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      }
    ),
  logout: () => api.post('/api/auth/logout'),
  refreshToken: (refreshToken: string) =>
    api.post('/api/auth/refresh', { refresh_token: refreshToken }),
};

export const userAPI = {
  getProfile: () => api.get('/api/users/me'),
  updateProfile: (userData: any) => api.put('/api/users/profile', userData),
};

export const walletAPI = {
  getBalance: () => api.get('/api/wallet/balance'),
  deposit: (amount: number, payment_method: string = 'mock') =>
    api.post('/api/wallet/deposit', { amount, payment_method }),
  withdraw: (amount: number, payment_method: string = 'mock') =>
    api.post('/api/wallet/withdraw', { amount, payment_method }),
};

// Test function to verify auth is working
export const testAuth = async () => {
  try {
    // console.log('Testing auth - Getting current user...');
    const response = await api.get('/api/users/me');
    // console.log('Test auth successful:', response.data);
    return response.data;
  } catch (error: any) {
    // console.error('Test auth failed:', error.response?.status, error.response?.data);
    throw error;
  }
};

export default api;