import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Skeleton } from '@/components/ui/skeleton';
import useSportsStore from '@/stores/sportsStore';
import type { SportEvent } from '@/types';
import {
  Search,
  Star,
  Clock,
  TrendingUp,
  Globe,
  ChevronDown,
  Flame,
  AlertCircle,
  RefreshCw
} from 'lucide-react';

const SportsPageLive = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [showDetailView, setShowDetailView] = useState<string | null>(null);
  const [oddsType, setOddsType] = useState('american');
  const [refreshInterval, setRefreshInterval] = useState<NodeJS.Timeout | null>(null);

  const {
    sports,
    liveEvents,
    upcomingEvents,
    allEvents,
    isLoading,
    error,
    selectedSport,
    fetchSports,
    fetchAllEvents,
    setSelectedSport,
    refreshData
  } = useSportsStore();

  // Fetch initial data
  useEffect(() => {
    fetchSports();
    fetchAllEvents();

    // Set up auto-refresh every 30 seconds for live data
    const interval = setInterval(() => {
      refreshData();
    }, 30000);

    setRefreshInterval(interval);

    return () => {
      if (interval) clearInterval(interval);
    };
  }, []);

  // Filter events based on search and selected sport
  const filteredEvents = React.useMemo(() => {
    let events = [...allEvents];

    // Filter by tab
    if (activeTab === 'live') {
      events = liveEvents;
    } else if (activeTab === 'upcoming') {
      events = upcomingEvents;
    }

    // Filter by selected sport
    if (selectedSport && selectedSport !== 'all') {
      events = events.filter(event =>
        event.sport.toLowerCase() === selectedSport.toLowerCase()
      );
    }

    // Filter by search query
    if (searchQuery) {
      events = events.filter(event =>
        event.homeTeam.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        event.awayTeam.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        event.league.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    return events;
  }, [allEvents, liveEvents, upcomingEvents, activeTab, selectedSport, searchQuery]);

  // Format odds based on selected type
  const formatOdds = (odds: number) => {
    if (oddsType === 'american') {
      return odds > 0 ? `+${odds}` : odds.toString();
    } else if (oddsType === 'decimal') {
      // Convert American to Decimal
      if (odds > 0) {
        return ((odds / 100) + 1).toFixed(2);
      } else {
        return ((-100 / odds) + 1).toFixed(2);
      }
    } else if (oddsType === 'fractional') {
      // Convert American to Fractional
      if (odds > 0) {
        return `${odds}/100`;
      } else {
        return `100/${Math.abs(odds)}`;
      }
    }
    return odds.toString();
  };

  // Render loading skeleton
  const renderLoadingSkeleton = () => (
    <div className="space-y-2">
      {[1, 2, 3].map(i => (
        <Card key={i} className="bg-slate-900 border-slate-800">
          <CardHeader className="p-3">
            <Skeleton className="h-4 w-32" />
          </CardHeader>
          <CardContent className="p-3">
            <div className="flex justify-between items-center">
              <div className="space-y-2">
                <Skeleton className="h-4 w-40" />
                <Skeleton className="h-4 w-40" />
              </div>
              <div className="space-y-2">
                <Skeleton className="h-8 w-24" />
                <Skeleton className="h-8 w-24" />
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );

  // Render event card
  const renderEventCard = (event: SportEvent) => {
    const isExpanded = showDetailView === event.id;

    return (
      <Card
        key={event.id}
        className={`mb-2 bg-slate-900 border-slate-800 hover:border-slate-700 transition-all cursor-pointer ${
          event.featured ? 'border-l-4 border-l-amber-500' : ''
        }`}
        onClick={() => navigate(`/sports/event/${event.id}`)}
      >
        <CardHeader className={`p-3 ${event.status === 'live' ? 'bg-slate-800/50' : ''}`}>
          <div className="flex justify-between items-center">
            <div className="flex items-center">
              <span className="text-xs font-medium text-slate-300">{event.league}</span>
              {event.status === 'live' && (
                <Badge className="ml-2 bg-red-500 animate-pulse text-[10px] py-0">LIVE</Badge>
              )}
              {event.trending && (
                <Badge className="ml-2 bg-orange-500 text-[10px] py-0">
                  <Flame className="h-3 w-3 mr-1" /> Hot
                </Badge>
              )}
            </div>
            <div className="flex items-center space-x-1">
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0"
                onClick={(e) => {
                  e.stopPropagation();
                  setShowDetailView(isExpanded ? null : event.id);
                }}
              >
                <ChevronDown className={`h-3.5 w-3.5 transition-transform ${isExpanded ? 'rotate-180' : ''}`} />
              </Button>
            </div>
          </div>
        </CardHeader>

        <CardContent className="p-3">
          <div className="flex justify-between items-center">
            <div className="flex-1">
              <div className="font-medium text-white">{event.homeTeam.name}</div>
              <div className="font-medium text-white">{event.awayTeam.name}</div>
            </div>

            <div className="flex-shrink-0 w-16 text-center">
              {event.status === 'live' && event.score ? (
                <div className="font-bold text-white">{event.score}</div>
              ) : (
                <div className="text-xs text-slate-400">
                  {new Date(event.startTime).toLocaleTimeString([], {
                    hour: '2-digit',
                    minute: '2-digit'
                  })}
                </div>
              )}
              {event.minute && (
                <div className="text-xs text-red-400">{event.minute}</div>
              )}
            </div>

            <div className="flex-shrink-0 ml-4">
              <Button
                variant="outline"
                size="sm"
                className="h-8 px-3 text-xs mb-1 justify-between w-28"
                onClick={(e) => e.stopPropagation()}
              >
                <span className="truncate">{event.homeTeam.name.split(' ').pop()}</span>
                <span className={event.odds.home > 0 ? "text-green-400" : "text-red-400"}>
                  {formatOdds(event.odds.home)}
                </span>
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="h-8 px-3 text-xs justify-between w-28"
                onClick={(e) => e.stopPropagation()}
              >
                <span className="truncate">{event.awayTeam.name.split(' ').pop()}</span>
                <span className={event.odds.away > 0 ? "text-green-400" : "text-red-400"}>
                  {formatOdds(event.odds.away)}
                </span>
              </Button>
            </div>
          </div>

          {isExpanded && (
            <div className="mt-3 pt-3 border-t border-slate-800">
              {event.odds.draw && (
                <Button
                  variant="outline"
                  size="sm"
                  className="h-8 text-xs w-full mb-2"
                  onClick={(e) => e.stopPropagation()}
                >
                  <span>Draw</span>
                  <span className="ml-auto text-green-400">{formatOdds(event.odds.draw)}</span>
                </Button>
              )}
              <div className="text-xs text-slate-400 flex items-center justify-between">
                <div>
                  <TrendingUp className="h-3 w-3 inline mr-1" />
                  {event.viewers ? `${(event.viewers / 1000).toFixed(1)}K viewers` : 'View details'}
                </div>
                {event.startTime && (
                  <div>
                    <Clock className="h-3 w-3 inline mr-1" />
                    {new Date(event.startTime).toLocaleDateString()}
                  </div>
                )}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="min-h-screen bg-slate-950 text-white pt-16">
      {/* Sub-Navigation */}
      <div className="border-b border-slate-800 bg-slate-900 sticky top-16 z-20">
        <div className="flex p-2">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1">
            <TabsList className="bg-transparent">
              <TabsTrigger value="all" className="text-xs">All Events</TabsTrigger>
              <TabsTrigger value="live" className="text-xs">
                Live Now
                <span className="ml-1 h-2 w-2 rounded-full bg-red-500 animate-pulse"></span>
              </TabsTrigger>
              <TabsTrigger value="upcoming" className="text-xs">Upcoming</TabsTrigger>
            </TabsList>
          </Tabs>

          <div className="ml-auto flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => refreshData()}
              disabled={isLoading}
            >
              <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            </Button>

            <div className="relative">
              <Search className="absolute top-2 left-2 h-4 w-4 text-slate-400" />
              <Input
                className="h-8 bg-slate-800 border-slate-700 pl-8 text-xs w-48"
                placeholder="Search teams, leagues..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-4">
        <div className="flex flex-col lg:flex-row gap-4">
          {/* Sports Filter Sidebar */}
          <div className="lg:w-64">
            <Card className="bg-slate-900 border-slate-800">
              <CardHeader className="p-3">
                <h3 className="text-sm font-medium">Sports</h3>
              </CardHeader>
              <CardContent className="p-3 pt-0">
                <div className="space-y-1">
                  <Button
                    variant={selectedSport === 'all' || !selectedSport ? 'default' : 'ghost'}
                    size="sm"
                    className="w-full justify-start text-xs h-8"
                    onClick={() => setSelectedSport('all')}
                  >
                    <Globe className="w-3.5 h-3.5 mr-2" /> All Sports
                  </Button>

                  {sports.filter(sport => sport.active).map(sport => (
                    <Button
                      key={sport.id}
                      variant={selectedSport === sport.id ? 'default' : 'ghost'}
                      size="sm"
                      className="w-full justify-start text-xs h-8"
                      onClick={() => setSelectedSport(sport.id)}
                    >
                      <span className="mr-2">{sport.icon}</span> {sport.name}
                    </Button>
                  ))}
                </div>

                <div className="mt-4 pt-4 border-t border-slate-800">
                  <div className="text-xs text-slate-400 mb-2">Odds Format</div>
                  <div className="space-y-1">
                    {['american', 'decimal', 'fractional'].map(type => (
                      <Button
                        key={type}
                        variant={oddsType === type ? 'default' : 'ghost'}
                        size="sm"
                        className="w-full justify-start text-xs h-7"
                        onClick={() => setOddsType(type)}
                      >
                        {type.charAt(0).toUpperCase() + type.slice(1)}
                      </Button>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Events List */}
          <div className="flex-1">
            {error && (
              <div className="mb-4 p-4 bg-red-900/20 border border-red-800 rounded-md flex items-center">
                <AlertCircle className="h-4 w-4 mr-2 text-red-400" />
                <span className="text-sm text-red-400">{error}</span>
              </div>
            )}

            {isLoading ? (
              renderLoadingSkeleton()
            ) : filteredEvents.length === 0 ? (
              <Card className="bg-slate-900 border-slate-800">
                <CardContent className="p-8 text-center">
                  <AlertCircle className="h-8 w-8 mx-auto mb-2 text-slate-400" />
                  <p className="text-sm text-slate-400">No events found</p>
                  <p className="text-xs text-slate-500 mt-1">
                    {searchQuery ? 'Try adjusting your search' : 'Check back later for more events'}
                  </p>
                </CardContent>
              </Card>
            ) : (
              <div className="space-y-2">
                {filteredEvents.map(event => renderEventCard(event))}
              </div>
            )}
          </div>

          {/* Live Stats Sidebar (optional) */}
          <div className="lg:w-80">
            <Card className="bg-slate-900 border-slate-800">
              <CardHeader className="p-3">
                <h3 className="text-sm font-medium">Live Statistics</h3>
              </CardHeader>
              <CardContent className="p-3 pt-0">
                <div className="space-y-3">
                  <div className="flex justify-between items-center text-xs">
                    <span className="text-slate-400">Live Events</span>
                    <Badge className="bg-red-500">{liveEvents.length}</Badge>
                  </div>
                  <div className="flex justify-between items-center text-xs">
                    <span className="text-slate-400">Upcoming Today</span>
                    <Badge className="bg-blue-500">
                      {upcomingEvents.filter(e => {
                        const today = new Date().toDateString();
                        return new Date(e.startTime).toDateString() === today;
                      }).length}
                    </Badge>
                  </div>
                  <div className="flex justify-between items-center text-xs">
                    <span className="text-slate-400">Total Events</span>
                    <Badge className="bg-slate-600">{allEvents.length}</Badge>
                  </div>
                </div>

                {sports.length > 0 && (
                  <div className="mt-4 pt-4 border-t border-slate-800">
                    <div className="text-xs text-slate-400 mb-2">Active Sports</div>
                    <div className="space-y-2">
                      {sports.filter(s => s.active).slice(0, 5).map(sport => {
                        const sportEvents = allEvents.filter(e =>
                          e.sport.toLowerCase() === sport.id.toLowerCase()
                        );
                        return (
                          <div key={sport.id} className="flex items-center justify-between text-xs">
                            <div className="flex items-center">
                              <span className="mr-2">{sport.icon}</span>
                              <span>{sport.name}</span>
                            </div>
                            <span className="text-slate-400">{sportEvents.length}</span>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SportsPageLive;