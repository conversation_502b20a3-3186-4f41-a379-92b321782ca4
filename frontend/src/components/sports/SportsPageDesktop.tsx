import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Skeleton } from '@/components/ui/skeleton';
import useSportsStore from '@/stores/sportsStore';
import type { SportEvent } from '@/types/sports';
import {
  Search,
  Home,
  BarChart2,
  Trophy,
  Wallet,
  Bell,
  User,
  Calendar,
  Filter,
  Star,
  Clock,
  TrendingUp,
  Globe,
  Menu,
  ChevronDown,
  ChevronRight,
  ArrowRight,
  ExternalLink,
  MessageSquare,
  MoreHorizontal,
  Bookmark,
  Share2,
  Plus,
  AlertCircle,
  X,
  Check,
  DollarSign,
  Flame,
  Target,
  Sword,
  Activity,
  RefreshCw
} from 'lucide-react';

// Main Sports Platform Component
interface SportsPageDesktopProps {
  sport?: string;
}

const SportsPageDesktop = ({ sport: propSport }: SportsPageDesktopProps) => {
  const params = useParams();
  const navigate = useNavigate();
  const sport = propSport || params.sport;
  const [activeTab, setActiveTab] = useState('all-sports');
  const [activeSport, setActiveSport] = useState('all');
  const [dateFilter, setDateFilter] = useState('today');
  const [oddsType, setOddsType] = useState('american');
  const [marketType, setMarketType] = useState('all');
  const [viewMode, setViewMode] = useState('detail');
  const [searchQuery, setSearchQuery] = useState('');
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState<SportEvent | null>(null);
  const [activeBetsTab, setActiveBetsTab] = useState('active');
  const [showDetailView, setShowDetailView] = useState<string | null>(null);

  const {
    sports,
    liveEvents,
    upcomingEvents,
    allEvents,
    isLoading,
    error,
    fetchSports,
    fetchAllEvents,
    refreshData
  } = useSportsStore();

  // Fetch data on mount and set up auto-refresh
  useEffect(() => {
    fetchSports();
    fetchAllEvents();

    // Auto-refresh every 30 seconds
    const interval = setInterval(() => {
      refreshData();
    }, 30000);

    return () => clearInterval(interval);
  }, []);

  // Filter events based on active filters
  const filteredEvents = React.useMemo(() => {
    let events = [...allEvents];

    // Filter by tab
    if (activeTab === 'live-now') {
      events = liveEvents;
    } else if (activeTab === 'upcoming') {
      events = upcomingEvents;
    } else if (activeTab === 'favorites') {
      // In real app, would filter by user's favorites
      events = events.filter(e => e.featured);
    }

    // Filter by sport
    if (activeSport !== 'all') {
      events = events.filter(event => 
        event.sport.toLowerCase() === activeSport.toLowerCase()
      );
    }

    // Filter by search
    if (searchQuery) {
      events = events.filter(event =>
        event.homeTeam.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        event.awayTeam.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        event.league.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Filter by date
    if (dateFilter === 'today') {
      const today = new Date().toDateString();
      events = events.filter(event => {
        const eventDate = new Date(event.startTime).toDateString();
        return event.status === 'live' || eventDate === today;
      });
    } else if (dateFilter === 'tomorrow') {
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      const tomorrowStr = tomorrow.toDateString();
      events = events.filter(event => 
        new Date(event.startTime).toDateString() === tomorrowStr
      );
    }

    return events;
  }, [allEvents, liveEvents, upcomingEvents, activeTab, activeSport, searchQuery, dateFilter]);

  // Format odds based on selected type
  const formatOdds = (odds: number) => {
    if (oddsType === 'american') {
      return odds > 0 ? `+${odds}` : odds.toString();
    } else if (oddsType === 'decimal') {
      if (odds > 0) {
        return ((odds / 100) + 1).toFixed(2);
      } else {
        return ((-100 / odds) + 1).toFixed(2);
      }
    } else if (oddsType === 'fractional') {
      if (odds > 0) {
        const decimal = odds / 100;
        return `${decimal}/1`;
      } else {
        const decimal = -100 / odds;
        return `1/${decimal.toFixed(1)}`;
      }
    }
    return odds.toString();
  };

  // Open event detail modal
  const handleEventClick = (event: SportEvent) => {
    setSelectedEvent(event);
    setShowDetailModal(true);
  };

  // Toggle event in favorites
  const toggleFavorite = (event: SportEvent, e: React.MouseEvent) => {
    e.stopPropagation();
    // In a real app, this would update the state and API
  };

  // Place a bet
  const placeBet = (event: SportEvent, selection: string, odds: string, e: React.MouseEvent) => {
    e.stopPropagation();
    // In a real app, this would open a bet slip or navigate to bet placement
  };

  // Render sport filters in left sidebar
  const renderSportFilters = () => {
    return (
      <Accordion type="single" collapsible defaultValue="sports" className="w-full">
        <AccordionItem value="sports">
          <AccordionTrigger className="py-2 text-sm font-medium text-white">
            Sports
          </AccordionTrigger>
          <AccordionContent>
            <div className="space-y-1">
              <Button
                variant={activeSport === 'all' ? 'default' : 'ghost'}
                size="sm"
                className="w-full justify-start text-xs h-8"
                onClick={() => setActiveSport('all')}
              >
                <Globe className="w-3.5 h-3.5 mr-2" /> All Sports
              </Button>

              {sports.filter(sport => sport.active).map(sport => (
                <Button
                  key={sport.id}
                  variant={activeSport === sport.id ? 'default' : 'ghost'}
                  size="sm"
                  className="w-full justify-start text-xs h-8"
                  onClick={() => setActiveSport(sport.id)}
                >
                  <span className="mr-2">{sport.icon}</span> {sport.name}
                </Button>
              ))}

              <Button
                variant="ghost"
                size="sm"
                className="w-full justify-start text-xs h-8 text-slate-400"
                onClick={() => fetchSports()}
              >
                <RefreshCw className="w-3.5 h-3.5 mr-2" /> Refresh Sports
              </Button>
            </div>
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="date">
          <AccordionTrigger className="py-2 text-sm font-medium text-white">
            Date & Time
          </AccordionTrigger>
          <AccordionContent>
            <div className="flex flex-col space-y-1">
              {['today', 'tomorrow', 'week', 'custom'].map(option => (
                <Button
                  key={option}
                  variant={dateFilter === option ? 'default' : 'ghost'}
                  size="sm"
                  className="justify-start text-xs h-8"
                  onClick={() => setDateFilter(option)}
                >
                  {option.charAt(0).toUpperCase() + option.slice(1)}
                  {option === 'week' && ' This Week'}
                  {option === 'custom' && ' Range'}
                </Button>
              ))}
            </div>
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="odds">
          <AccordionTrigger className="py-2 text-sm font-medium text-white">
            Odds Type
          </AccordionTrigger>
          <AccordionContent>
            <div className="flex flex-col space-y-1">
              <Button
                variant={oddsType === 'american' ? 'default' : 'ghost'}
                size="sm"
                className="justify-start text-xs h-8"
                onClick={() => setOddsType('american')}
              >
                American (+100/-110)
              </Button>
              <Button
                variant={oddsType === 'decimal' ? 'default' : 'ghost'}
                size="sm"
                className="justify-start text-xs h-8"
                onClick={() => setOddsType('decimal')}
              >
                Decimal (2.00)
              </Button>
              <Button
                variant={oddsType === 'fractional' ? 'default' : 'ghost'}
                size="sm"
                className="justify-start text-xs h-8"
                onClick={() => setOddsType('fractional')}
              >
                Fractional (1/1)
              </Button>
            </div>
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="market">
          <AccordionTrigger className="py-2 text-sm font-medium text-white">
            Market Type
          </AccordionTrigger>
          <AccordionContent>
            <div className="flex flex-col space-y-1">
              {['all', 'spread', 'moneyline', 'totals', 'futures', 'props'].map(type => (
                <Button
                  key={type}
                  variant={marketType === type ? 'default' : 'ghost'}
                  size="sm"
                  className="justify-start text-xs h-8"
                  onClick={() => setMarketType(type)}
                >
                  {type === 'all' && 'All Markets'}
                  {type === 'spread' && 'Spreads'}
                  {type === 'moneyline' && 'Moneylines'}
                  {type === 'totals' && 'Over/Unders'}
                  {type === 'futures' && 'Futures'}
                  {type === 'props' && 'Props'}
                </Button>
              ))}
            </div>
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="view">
          <AccordionTrigger className="py-2 text-sm font-medium text-white">
            View Mode
          </AccordionTrigger>
          <AccordionContent>
            <div className="flex flex-col space-y-1">
              <Button
                variant={viewMode === 'compact' ? 'default' : 'ghost'}
                size="sm"
                className="justify-start text-xs h-8"
                onClick={() => setViewMode('compact')}
              >
                Compact View
              </Button>
              <Button
                variant={viewMode === 'detail' ? 'default' : 'ghost'}
                size="sm"
                className="justify-start text-xs h-8"
                onClick={() => setViewMode('detail')}
              >
                Detail View
              </Button>
            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    );
  };

  // Render event cards in center content
  const renderEventCard = (event: SportEvent, isFeatured = false) => {
    const isExpanded = showDetailView === event.id;

    return (
      <Card
        key={event.id}
        className={`w-full mb-2 bg-slate-900 border-slate-800 hover:border-slate-700 transition-all overflow-hidden cursor-pointer ${isFeatured ? 'border-l-4 border-l-amber-500' : ''}`}
        onClick={() => handleEventClick(event)}
      >
        <CardHeader className={`p-3 ${event.status === 'live' ? 'bg-slate-800/50' : ''}`}>
          <div className="flex justify-between items-center">
            <div className="flex items-center">
              <span className="text-xs font-medium text-slate-300">{event.league}</span>
              {event.status === 'live' && (
                <Badge className="ml-2 bg-red-500 animate-pulse text-[10px] py-0">LIVE</Badge>
              )}
              {event.trending && (
                <Badge className="ml-2 bg-orange-500 text-[10px] py-0">
                  <Flame className="h-3 w-3 mr-1" /> Trending
                </Badge>
              )}
            </div>
            <div className="flex items-center space-x-1">
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0"
                onClick={(e) => toggleFavorite(event, e)}
              >
                <Star className={`h-3.5 w-3.5 ${event.featured ? 'text-yellow-400 fill-yellow-400' : 'text-slate-400'}`} />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0"
                onClick={(e) => {
                  e.stopPropagation();
                  setShowDetailView(isExpanded ? null : event.id);
                }}
              >
                <ChevronDown className={`h-3.5 w-3.5 transition-transform ${isExpanded ? 'rotate-180' : ''}`} />
              </Button>
            </div>
          </div>
        </CardHeader>

        <CardContent className="p-3">
          <div className="flex justify-between items-center">
            <div className="flex-1">
              <div className="font-medium text-white">{event.homeTeam.name}</div>
              <div className="font-medium text-white">{event.awayTeam.name}</div>
            </div>

            <div className="flex-shrink-0 w-12 text-center">
              {event.status === 'live' && event.score ? (
                <div className="font-bold text-white">{event.score}</div>
              ) : (
                <div className="text-xs text-slate-400">
                  {new Date(event.startTime).toLocaleTimeString([], { 
                    hour: '2-digit', 
                    minute: '2-digit' 
                  })}
                </div>
              )}
            </div>

            <div className="flex-shrink-0 ml-2">
              <Button
                variant="outline"
                size="sm"
                className="h-8 px-2 text-xs mb-1 justify-between w-24"
                onClick={(e) => placeBet(event, `${event.homeTeam.name} ML`, event.odds.home.toString(), e)}
              >
                <span>{event.homeTeam.name.split(' ').pop()}</span>
                <span className={event.odds.home > 0 ? "text-green-400" : "text-red-400"}>
                  {formatOdds(event.odds.home)}
                </span>
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="h-8 px-2 text-xs justify-between w-24"
                onClick={(e) => placeBet(event, `${event.awayTeam.name} ML`, event.odds.away.toString(), e)}
              >
                <span>{event.awayTeam.name.split(' ').pop()}</span>
                <span className={event.odds.away > 0 ? "text-green-400" : "text-red-400"}>
                  {formatOdds(event.odds.away)}
                </span>
              </Button>
            </div>
          </div>

          {event.status === 'live' && event.minute && (
            <div className="mt-2 text-xs text-red-400 font-medium flex items-center">
              <Clock className="h-3 w-3 mr-1" />
              {event.minute}
            </div>
          )}

          {isExpanded && (
            <div className="mt-3 border-t border-slate-800 pt-3">
              <div className="grid grid-cols-3 gap-2 mb-3">
                {event.odds.draw && (
                  <Button
                    variant="outline"
                    size="sm"
                    className="h-8 text-xs justify-between"
                    onClick={(e) => placeBet(event, 'Draw', event.odds.draw.toString(), e)}
                  >
                    <span>Draw</span>
                    <span className="text-green-400">{formatOdds(event.odds.draw)}</span>
                  </Button>
                )}
              </div>

              <div className="flex space-x-2">
                <Button
                  variant="default"
                  size="sm"
                  className="h-8 text-xs bg-gradient-to-r from-purple-500 to-pink-500"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleEventClick(event);
                  }}
                >
                  View All Odds
                  <ExternalLink className="h-3 w-3 ml-1" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="h-8 text-xs"
                  onClick={(e) => {
                    e.stopPropagation();
                  }}
                >
                  <Plus className="h-3 w-3 mr-1" />
                  Add to Bet Slip
                </Button>
              </div>

              <div className="mt-3 text-xs text-slate-400 flex items-center justify-between">
                <div>
                  <TrendingUp className="h-3 w-3 inline mr-1" />
                  {event.viewers ? `${(event.viewers / 1000).toFixed(1)}K viewers` : 'View details'}
                </div>
                <div className="flex space-x-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0"
                    onClick={(e) => {
                      e.stopPropagation();
                    }}
                  >
                    <Share2 className="h-3 w-3" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0"
                    onClick={(e) => {
                      e.stopPropagation();
                    }}
                  >
                    <MoreHorizontal className="h-3 w-3" />
                  </Button>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    );
  };

  // Render trending leagues in the right sidebar
  const renderTrendingLeagues = () => {
    return (
      <div className="space-y-2">
        {sports.filter(s => s.active).slice(0, 5).map(sport => {
          const sportEvents = allEvents.filter(e => 
            e.sport.toLowerCase() === sport.id.toLowerCase()
          );
          const liveCount = sportEvents.filter(e => e.status === 'live').length;
          
          return (
            <div
              key={sport.id}
              className="flex justify-between items-center p-2 bg-slate-800 rounded-sm hover:bg-slate-700 cursor-pointer"
              onClick={() => setActiveSport(sport.id)}
            >
              <div className="flex items-center">
                <span className="text-xl mr-2">{sport.icon}</span>
                <div>
                  <div className="text-xs font-medium text-white">{sport.name}</div>
                  <div className="text-xs text-slate-400">
                    {sportEvents.length} events {liveCount > 0 && `(${liveCount} live)`}
                  </div>
                </div>
              </div>
              <Badge className="bg-green-500/20 text-green-400">
                {sportEvents.length}
              </Badge>
            </div>
          );
        })}
      </div>
    );
  };

  // Main render function
  return (
    <div className="min-h-screen bg-slate-950 text-white pt-16">
      {/* Sub-Navigation Tabs */}
      <div className="border-b border-slate-800 bg-slate-900 overflow-x-auto sticky top-16 z-20">
        <div className="flex p-1 min-w-max">
          <Button
            variant={activeTab === 'all-sports' ? 'default' : 'ghost'}
            size="sm"
            className="h-8 text-xs"
            onClick={() => setActiveTab('all-sports')}
          >
            All Sports
          </Button>
          <Button
            variant={activeTab === 'live-now' ? 'default' : 'ghost'}
            size="sm"
            className="h-8 text-xs"
            onClick={() => setActiveTab('live-now')}
          >
            <span className="relative">
              Live Now
              {liveEvents.length > 0 && (
                <span className="absolute -top-1 -right-1 h-2 w-2 rounded-full bg-red-500 animate-pulse"></span>
              )}
            </span>
          </Button>
          <Button
            variant={activeTab === 'upcoming' ? 'default' : 'ghost'}
            size="sm"
            className="h-8 text-xs"
            onClick={() => setActiveTab('upcoming')}
          >
            Upcoming
          </Button>
          <Button
            variant={activeTab === 'popular' ? 'default' : 'ghost'}
            size="sm"
            className="h-8 text-xs"
            onClick={() => setActiveTab('popular')}
          >
            Popular Leagues
          </Button>
          <Button
            variant={activeTab === 'favorites' ? 'default' : 'ghost'}
            size="sm"
            className="h-8 text-xs"
            onClick={() => setActiveTab('favorites')}
          >
            <Star className="h-3.5 w-3.5 mr-1" />
            My Favorites
          </Button>
          <Button
            variant={activeTab === 'exotics' ? 'default' : 'ghost'}
            size="sm"
            className="h-8 text-xs"
            onClick={() => setActiveTab('exotics')}
          >
            Exotics / Non-Sports
          </Button>

          <div className="ml-auto relative flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => refreshData()}
              disabled={isLoading}
            >
              <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            </Button>
            <Search className="absolute top-2 left-14 h-4 w-4 text-slate-400" />
            <Input
              className="h-8 bg-slate-800 border-slate-700 pl-8 text-xs w-48"
              placeholder="Search teams, leagues, events..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="h-[calc(100vh-112px)]">
        <div className="h-full grid grid-cols-12 gap-2 p-2">
          {/* Left Sidebar - Filters */}
          <div className="col-span-2 flex flex-col gap-2 h-full overflow-hidden">
            <div className="bg-slate-900 border border-slate-800 rounded-sm flex-1 overflow-auto p-2">
              {renderSportFilters()}
            </div>
          </div>

          {/* Center Content - Main Feed */}
          <div className="col-span-7 flex flex-col gap-2 h-full overflow-hidden">
            {/* Featured Event */}
            {filteredEvents.length > 0 && filteredEvents[0]?.trending && (
              <div className="bg-slate-900 border border-slate-800 rounded-sm p-3">
                <div className="flex justify-between items-center mb-2">
                  <div className="flex items-center">
                    <Flame className="h-4 w-4 text-orange-500 mr-1" />
                    <h3 className="text-sm font-medium text-white">Featured Match</h3>
                  </div>
                  <Badge className="bg-gradient-to-r from-orange-500 to-pink-500">
                    HOT BET
                  </Badge>
                </div>

                {renderEventCard(filteredEvents[0], true)}
              </div>
            )}

            {/* Events Grid */}
            <div className="bg-slate-900 border border-slate-800 rounded-sm flex-1 overflow-hidden flex flex-col">
              <div className="p-2 border-b border-slate-800 flex justify-between items-center">
                <h3 className="text-sm font-medium text-white">
                  {activeTab === 'all-sports' && 'All Sports'}
                  {activeTab === 'live-now' && 'Live Games'}
                  {activeTab === 'upcoming' && 'Upcoming Events'}
                  {activeTab === 'popular' && 'Popular Leagues'}
                  {activeTab === 'favorites' && 'My Favorites'}
                  {activeTab === 'exotics' && 'Exotic Bets'}
                </h3>
                <div className="flex items-center space-x-1">
                  <Badge className="bg-slate-800 text-slate-300 text-xs">
                    {filteredEvents.length} events
                  </Badge>
                  <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                    <Filter className="h-3.5 w-3.5" />
                  </Button>
                </div>
              </div>

              <div className="flex-1 overflow-auto p-2">
                {isLoading ? (
                  <div className="space-y-2">
                    {[1, 2, 3].map(i => (
                      <Card key={i} className="bg-slate-900 border-slate-800">
                        <CardHeader className="p-3">
                          <Skeleton className="h-4 w-32" />
                        </CardHeader>
                        <CardContent className="p-3">
                          <Skeleton className="h-20 w-full" />
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                ) : filteredEvents.length === 0 ? (
                  <div className="h-full flex flex-col items-center justify-center text-slate-400">
                    <AlertCircle className="h-8 w-8 mb-2" />
                    <p className="text-sm mb-1">No events found</p>
                    <p className="text-xs">Try changing your filters or search query</p>
                  </div>
                ) : (
                  <div className="space-y-2">
                    {filteredEvents.map((event, index) => {
                      // Skip the first item if it's featured
                      if (index === 0 && event.trending && activeTab === 'all-sports') {
                        return null;
                      }
                      return renderEventCard(event);
                    })}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Right Sidebar - Dynamic Widgets */}
          <div className="col-span-3 flex flex-col gap-2 h-full overflow-hidden">
            {/* My Bets */}
            <div className="bg-slate-900 border border-slate-800 rounded-sm p-2">
              <div className="flex justify-between items-center mb-2">
                <h3 className="text-sm font-medium text-white">My Bets</h3>
                <Button
                  variant="outline"
                  size="sm"
                  className="h-6 text-xs"
                >
                  <Plus className="h-3.5 w-3.5 mr-1" />
                  New Bet
                </Button>
              </div>

              <div className="flex border-b border-slate-800 mb-2">
                <Button
                  variant={activeBetsTab === 'active' ? 'default' : 'ghost'}
                  size="sm"
                  className="h-7 text-xs rounded-none flex-1"
                  onClick={() => setActiveBetsTab('active')}
                >
                  Active
                </Button>
                <Button
                  variant={activeBetsTab === 'pending' ? 'default' : 'ghost'}
                  size="sm"
                  className="h-7 text-xs rounded-none flex-1"
                  onClick={() => setActiveBetsTab('pending')}
                >
                  Pending
                </Button>
                <Button
                  variant={activeBetsTab === 'history' ? 'default' : 'ghost'}
                  size="sm"
                  className="h-7 text-xs rounded-none flex-1"
                  onClick={() => setActiveBetsTab('history')}
                >
                  History
                </Button>
              </div>

              <div className="h-40 overflow-auto">
                <div className="text-center py-4 text-xs text-slate-400">
                  No {activeBetsTab} bets
                </div>
              </div>
            </div>

            {/* Trending Leagues */}
            <div className="bg-slate-900 border border-slate-800 rounded-sm p-2">
              <div className="flex justify-between items-center mb-2">
                <h3 className="text-sm font-medium text-white">Trending Sports</h3>
                <Badge className="bg-gradient-to-r from-green-500 to-emerald-600">
                  <TrendingUp className="h-3 w-3 mr-1" /> LIVE
                </Badge>
              </div>

              <div className="h-32 overflow-auto">
                {renderTrendingLeagues()}
              </div>
            </div>

            {/* Watchlist */}
            <div className="bg-slate-900 border border-slate-800 rounded-sm p-2 flex-1 overflow-hidden flex flex-col">
              <div className="flex justify-between items-center mb-2">
                <h3 className="text-sm font-medium text-white">My Watchlist</h3>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0"
                >
                  <Bookmark className="h-3.5 w-3.5" />
                </Button>
              </div>

              <div className="flex-1 overflow-auto">
                <div className="space-y-2">
                  {filteredEvents.filter(event => event.featured).slice(0, 3).map(event => (
                    <div
                      key={event.id}
                      className="flex justify-between items-center p-2 bg-slate-800 rounded-sm hover:bg-slate-700 cursor-pointer"
                      onClick={() => handleEventClick(event)}
                    >
                      <div>
                        <div className="text-xs font-medium text-white">
                          {event.homeTeam.name} vs {event.awayTeam.name}
                        </div>
                        <div className="text-xs text-slate-400">
                          {event.league} • {event.status === 'live' ? 'LIVE' : new Date(event.startTime).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                        </div>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-6 w-6 p-0"
                        onClick={(e) => toggleFavorite(event, e)}
                      >
                        <Star className="h-3.5 w-3.5 text-yellow-400 fill-yellow-400" />
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Live Stats */}
            <div className="bg-slate-900 border border-slate-800 rounded-sm p-2">
              <div className="flex justify-between items-center mb-2">
                <h3 className="text-sm font-medium text-white">Live Stats</h3>
                <Badge className="bg-red-500 text-xs">LIVE</Badge>
              </div>

              <div className="space-y-2 text-xs">
                <div className="flex justify-between items-center">
                  <span className="text-slate-400">Live Events</span>
                  <Badge className="bg-red-500">{liveEvents.length}</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-slate-400">Upcoming Today</span>
                  <Badge className="bg-blue-500">
                    {upcomingEvents.filter(e => {
                      const today = new Date().toDateString();
                      return new Date(e.startTime).toDateString() === today;
                    }).length}
                  </Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-slate-400">Total Events</span>
                  <Badge className="bg-slate-600">{allEvents.length}</Badge>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Event Detail Modal */}
      {showDetailModal && selectedEvent && (
        <div className="fixed inset-0 bg-black bg-opacity-80 z-50 flex items-center justify-center p-4">
          <div className="bg-slate-900 border border-slate-800 rounded-md w-full max-w-4xl max-h-[90vh] overflow-auto">
            <div className="p-4 border-b border-slate-800 flex justify-between items-center sticky top-0 bg-slate-900 z-10">
              <div className="flex items-center">
                <h2 className="text-lg font-bold text-white">{selectedEvent.league}</h2>
                {selectedEvent.status === 'live' && (
                  <Badge className="ml-2 bg-red-500 animate-pulse">LIVE</Badge>
                )}
              </div>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0"
                onClick={() => setShowDetailModal(false)}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>

            <div className="p-4">
              <div className="flex justify-between items-center mb-4">
                <div className="text-2xl font-bold text-white">{selectedEvent.homeTeam.name}</div>
                <div className="text-4xl font-bold">
                  {selectedEvent.status === 'live' && selectedEvent.score ? (
                    <span className="text-white">{selectedEvent.score}</span>
                  ) : (
                    <span className="text-slate-600">VS</span>
                  )}
                </div>
                <div className="text-2xl font-bold text-white">{selectedEvent.awayTeam.name}</div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SportsPageDesktop;