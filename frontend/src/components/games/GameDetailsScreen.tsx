import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Card } from '@/components/ui/card';
import DesktopGameDetailsLayout from './DesktopGameDetailsLayout';
import { gameInstancesService } from '@/services/gameInstances';
import type { GameInstance as APIGameInstance } from '@/services/gameInstances';
import useAuth from '@/hooks/useAuth';
import CreateGameDialog from './CreateGameDialog';
import BlurDetectiveSetupWizard from './BlurDetectiveSetupWizard';
import {
  ArrowLeft,
  Users,
  DollarSign,
  Clock,
  Eye,
  Star,
  PlayCircle,
  Radio,
  Trophy,
  Timer,
  Zap,
  Shield,
  Crown,
  Layers,
  Grid3x3,
  Hand,
  Brain,
  Plus,
  Film
} from 'lucide-react';

interface GameDetailsScreenProps {
  gameId: string;
  gameName: string;
  gameType: string;
  onBack: () => void;
  onJoinGame: (instanceId: string) => void;
}

interface GameInstance {
  id: string;
  players: {
    player1: { id: string; name: string; avatar?: string };
    player2?: { id: string; name: string; avatar?: string };
  };
  stake: number;
  status: 'waiting' | 'live' | 'finished';
  currentTurn?: string;
  timeRemaining?: string;
  viewers: number;
  boardState?: any; // This would be the actual game state for thumbnail preview
  playerCount?: number; // For games like blur-detective that support more than 2 players
  maxPlayers?: number;
}

const GameDetailsScreen: React.FC<GameDetailsScreenProps> = ({
  gameId,
  gameName,
  gameType,
  onBack,
  onJoinGame
}) => {
  const [filter, setFilter] = useState<'all' | 'waiting' | 'live'>('all');
  const [isFavorited, setIsFavorited] = useState(false);
  const [isDesktop, setIsDesktop] = useState(false);
  const [gameInstances, setGameInstances] = useState<GameInstance[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showCreateDialog, setShowCreateDialog] = useState(false);

  // Debug logging for dialog state
  React.useEffect(() => {
    console.log('🔄 showCreateDialog state changed:', showCreateDialog);
  }, [showCreateDialog]);
  const [showBlurDetectiveWizard, setShowBlurDetectiveWizard] = useState(false);
  const { user } = useAuth();

  useEffect(() => {
    const checkIsDesktop = () => {
      setIsDesktop(window.innerWidth >= 1024); // lg breakpoint
    };

    checkIsDesktop();
    window.addEventListener('resize', checkIsDesktop);
    return () => window.removeEventListener('resize', checkIsDesktop);
  }, []);

  // Fetch game instances from the API
  useEffect(() => {
    const fetchGames = async () => {
      try {
        setIsLoading(true);
        // Convert game type to snake_case for API
        const apiGameType = gameType.replace(/-/g, '_');
        const apiGames = await gameInstancesService.getGames(apiGameType);

        // Transform API games to UI format
        const transformedGames: GameInstance[] = apiGames.map((game: any, index) => {
          // New API returns players array with detailed info
          const players = game.players || [];
          const player1 = players.find((p: any) => p.is_host) || players[0];
          const player2 = players.find((p: any) => !p.is_host && p.player_number === 2);

          return {
            id: game.id,
            players: {
              player1: player1 ? {
                id: player1.user_id,
                name: player1.username || `Player${player1.user_id.slice(0, 4)}`
              } : {
                id: game.host_id || 'unknown',
                name: game.host_username || 'Unknown Host'
              },
              player2: player2 ? {
                id: player2.user_id,
                name: player2.username || `Player${player2.user_id.slice(0, 4)}`
              } : undefined
            },
            stake: game.wager_amount,
            status: game.status === 'waiting' ? 'waiting' :
                    game.status === 'in_progress' ? 'live' :
                    'finished',
            currentTurn: game.current_player_id === (player1?.user_id || game.host_id) ? '1' : '2',
            timeRemaining: game.status === 'in_progress' ? '5:00' : undefined,
            viewers: Math.floor(Math.random() * 100) + 10,
            boardState: game.game_state,
            playerCount: game.current_players || players.length,
            maxPlayers: game.max_players || 2
          };
        });

        setGameInstances(transformedGames);
      } catch (error) {
        console.error('Failed to fetch games:', error);
        // Fallback to mock data if API fails
        setGameInstances(getGameInstances());
      } finally {
        setIsLoading(false);
      }
    };

    fetchGames();

    // Refresh games every 5 seconds
    const interval = setInterval(fetchGames, 5000);
    return () => clearInterval(interval);
  }, [gameType]);

  const handleGameCreated = (gameId: string) => {
    // Refresh the games list
    const fetchGames = async () => {
      try {
        // Convert game type to snake_case for API
        const apiGameType = gameType.replace(/-/g, '_');
        const apiGames = await gameInstancesService.getGames(apiGameType);
        const transformedGames: GameInstance[] = apiGames.map((game: any, index) => {
          // New API returns players array with detailed info
          const players = game.players || [];
          const player1 = players.find((p: any) => p.is_host) || players[0];
          const player2 = players.find((p: any) => !p.is_host && p.player_number === 2);

          return {
            id: game.id,
            players: {
              player1: player1 ? {
                id: player1.user_id,
                name: player1.username || `Player${player1.user_id.slice(0, 4)}`
              } : {
                id: game.host_id || 'unknown',
                name: game.host_username || 'Unknown Host'
              },
              player2: player2 ? {
                id: player2.user_id,
                name: player2.username || `Player${player2.user_id.slice(0, 4)}`
              } : undefined
            },
            stake: game.wager_amount,
            status: game.status === 'waiting' ? 'waiting' :
                    game.status === 'in_progress' ? 'live' :
                    'finished',
            currentTurn: game.current_player_id === (player1?.user_id || game.host_id) ? '1' : '2',
            timeRemaining: game.status === 'in_progress' ? '5:00' : undefined,
            viewers: Math.floor(Math.random() * 100) + 10,
            boardState: game.game_state,
            playerCount: game.current_players || players.length,
            maxPlayers: game.max_players || 2
          };
        });

        setGameInstances(transformedGames);
      } catch (error) {
        console.error('Failed to fetch games:', error);
      }
    };

    fetchGames();
    // Optionally, join the created game
    onJoinGame(gameId);
  };

  // Generate appropriate game instances based on game type
  const getGameInstances = (): GameInstance[] => {
    // For blur-detective, show multiplayer games
    if (gameType === 'blur-detective') {
      return [
        {
          id: '1',
          players: {
            player1: { id: '1', name: 'QuizMaster' },
          },
          stake: 100,
          status: 'waiting',
          viewers: 45,
          playerCount: 3,
          maxPlayers: 8
        },
        {
          id: '2',
          players: {
            player1: { id: '2', name: 'DetectivePro' },
            player2: { id: '3', name: 'CelebExpert' }
          },
          stake: 50,
          status: 'live',
          timeRemaining: '3:20',
          viewers: 234,
          playerCount: 5,
          maxPlayers: 6
        },
        {
          id: '3',
          players: {
            player1: { id: '4', name: 'BlurHunter' },
          },
          stake: 200,
          status: 'waiting',
          viewers: 78,
          playerCount: 1,
          maxPlayers: 8
        },
        {
          id: '4',
          players: {
            player1: { id: '5', name: 'ImageSleuth' },
            player2: { id: '6', name: 'PixelDetective' }
          },
          stake: 150,
          status: 'live',
          timeRemaining: '1:45',
          viewers: 567,
          playerCount: 8,
          maxPlayers: 8
        },
        {
          id: '5',
          players: {
            player1: { id: '7', name: 'CelebSpotter' },
          },
          stake: 25,
          status: 'waiting',
          viewers: 12,
          playerCount: 2,
          maxPlayers: 4
        }
      ];
    }

    // Default instances for other games
    return [
    {
      id: '1',
      players: {
        player1: { id: '1', name: 'AlexTheGreat' },
        player2: { id: '2', name: 'ChessMaster99' }
      },
      stake: 50,
      status: 'live',
      currentTurn: '1',
      timeRemaining: '2:45',
      viewers: 234
    },
    {
      id: '2',
      players: {
        player1: { id: '3', name: 'ProGamer' },
      },
      stake: 25,
      status: 'waiting',
      viewers: 12
    },
    {
      id: '3',
      players: {
        player1: { id: '4', name: 'QuickDraw' },
        player2: { id: '5', name: 'SpeedDemon' }
      },
      stake: 100,
      status: 'live',
      currentTurn: '2',
      timeRemaining: '1:15',
      viewers: 567
    },
    {
      id: '4',
      players: {
        player1: { id: '6', name: 'Strategist' },
      },
      stake: 75,
      status: 'waiting',
      viewers: 45
    },
    {
      id: '5',
      players: {
        player1: { id: '7', name: 'NightHawk' },
        player2: { id: '8', name: 'Phoenix' }
      },
      stake: 200,
      status: 'live',
      currentTurn: '1',
      timeRemaining: '4:20',
      viewers: 892
    },
    {
      id: '6',
      players: {
        player1: { id: '9', name: 'Warrior' },
      },
      stake: 30,
      status: 'waiting',
      viewers: 8
    },
    {
      id: '7',
      players: {
        player1: { id: '10', name: 'TacticalGenius' },
        player2: { id: '11', name: 'MindReader' }
      },
      stake: 150,
      status: 'live',
      currentTurn: '2',
      timeRemaining: '0:45',
      viewers: 445
    },
    {
      id: '8',
      players: {
        player1: { id: '12', name: 'Rookie123' },
      },
      stake: 10,
      status: 'waiting',
      viewers: 3
    },
    {
      id: '9',
      players: {
        player1: { id: '13', name: 'ElitePlayer' },
        player2: { id: '14', name: 'Champion' }
      },
      stake: 500,
      status: 'live',
      currentTurn: '1',
      timeRemaining: '3:30',
      viewers: 1234
    }
    ];
  };

  // const gameInstances = getGameInstances(); // Now using state from API

  const filteredInstances = gameInstances.filter(instance => {
    if (filter === 'all') return true;
    return instance.status === filter;
  });

  const getGameIcon = () => {
    switch(gameType) {
      case 'chess':
      case 'chess-blitz':
        return <Crown className="h-5 w-5" />;
      case 'checkers':
        return <Grid3x3 className="h-5 w-5" />;
      case 'rock-paper-scissors':
        return <Hand className="h-5 w-5" />;
      case 'highlight-hero':
        return <Film className="h-5 w-5" />;
      case 'blur-detective':
        return <Eye className="h-5 w-5" />;
      case 'trivia':
      case 'speed-trivia':
      case 'quiz-arena':
        return <Brain className="h-5 w-5" />;
      case 'crazy-eights':
        return <span className="text-lg">🃏</span>;
      case 'strategy':
        return <Layers className="h-5 w-5" />;
      default:
        return <Zap className="h-5 w-5" />;
    }
  };

  // Sample chess positions for thumbnails
  const getChessPieces = (instanceId: string) => {
    // Different positions for different games to show variety
    const positions: { [key: string]: { square: number; piece: string; color: 'white' | 'black' }[] } = {
      '1': [
        { square: 0, piece: 'rook', color: 'black' },
        { square: 4, piece: 'king', color: 'black' },
        { square: 7, piece: 'rook', color: 'black' },
        { square: 12, piece: 'pawn', color: 'black' },
        { square: 35, piece: 'knight', color: 'white' },
        { square: 52, piece: 'pawn', color: 'white' },
        { square: 60, piece: 'king', color: 'white' },
      ],
      '3': [
        { square: 3, piece: 'queen', color: 'black' },
        { square: 6, piece: 'king', color: 'black' },
        { square: 27, piece: 'bishop', color: 'black' },
        { square: 36, piece: 'pawn', color: 'white' },
        { square: 42, piece: 'knight', color: 'white' },
        { square: 61, piece: 'king', color: 'white' },
      ],
      '5': [
        { square: 2, piece: 'king', color: 'black' },
        { square: 18, piece: 'queen', color: 'black' },
        { square: 45, piece: 'queen', color: 'white' },
        { square: 58, piece: 'king', color: 'white' },
      ],
      '7': [
        { square: 11, piece: 'king', color: 'black' },
        { square: 19, piece: 'rook', color: 'black' },
        { square: 43, piece: 'rook', color: 'white' },
        { square: 51, piece: 'king', color: 'white' },
      ],
      '9': [
        { square: 4, piece: 'king', color: 'black' },
        { square: 13, piece: 'pawn', color: 'black' },
        { square: 14, piece: 'pawn', color: 'black' },
        { square: 20, piece: 'pawn', color: 'black' },
        { square: 27, piece: 'queen', color: 'white' },
        { square: 43, piece: 'pawn', color: 'white' },
        { square: 44, piece: 'pawn', color: 'white' },
        { square: 60, piece: 'king', color: 'white' },
      ]
    };
    return positions[instanceId] || [];
  };

  const getPieceImage = (piece: string, color: 'white' | 'black') => {
    return `/images/chess/${color}-${piece}.png`;
  };

  const renderGameThumbnail = (instance: GameInstance) => {
    const isChess = gameType === 'chess' || gameType === 'chess-blitz';
    const isCheckers = gameType === 'checkers';
    const isHighlightHero = gameType === 'highlight-hero';
    const isBlurDetective = gameType === 'blur-detective';
    const isCrazyEights = gameType === 'crazy-eights';

    if (isBlurDetective) {
      return (
        <div className="relative h-full w-full bg-gradient-to-br from-purple-900/30 to-blue-900/30 rounded overflow-hidden flex items-center justify-center">
          <div className="absolute inset-0 bg-black/20 backdrop-blur-sm" />
          <Eye className="h-12 sm:h-8 w-12 sm:w-8 text-blue-400 z-10" />
          {instance.status === 'live' && (
            <div className="absolute top-1 right-1 z-20">
              <Badge className="bg-green-500 text-white text-[9px] h-3.5 px-1 animate-pulse">
                <Radio className="h-2 w-2 mr-0.5" />
                LIVE
              </Badge>
            </div>
          )}
          <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-2">
            <p className="text-[10px] text-white/80 text-center">Celebrity Detective</p>
          </div>
        </div>
      );
    }

    if (isHighlightHero) {
      return (
        <div className="relative h-full w-full bg-gradient-to-br from-purple-900/30 to-pink-900/30 rounded overflow-hidden flex items-center justify-center">
          <div className="absolute inset-0 bg-black/20" />
          <Film className="h-12 sm:h-8 w-12 sm:w-8 text-purple-400 z-10" />
          {instance.status === 'live' && (
            <div className="absolute top-1 right-1 z-20">
              <Badge className="bg-green-500 text-white text-[9px] h-3.5 px-1 animate-pulse">
                <Radio className="h-2 w-2 mr-0.5" />
                LIVE
              </Badge>
            </div>
          )}
          <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-2">
            <p className="text-[10px] text-white/80 text-center">Video Quiz</p>
          </div>
        </div>
      );
    }

    if (isCrazyEights) {
      return (
        <div className="relative h-full w-full bg-gradient-to-br from-red-800 to-red-700 rounded overflow-hidden flex items-center justify-center">
          <div className="relative flex space-x-1">
            {/* Playing cards */}
            <div className="w-3 h-4 bg-white rounded-sm border border-slate-300 flex items-center justify-center">
              <span className="text-xs text-red-500">♥</span>
            </div>
            <div className="w-3 h-4 bg-white rounded-sm border border-slate-300 flex items-center justify-center">
              <span className="text-xs text-black">♠</span>
            </div>
            <div className="w-3 h-4 bg-white rounded-sm border border-slate-300 flex items-center justify-center">
              <span className="text-xs text-red-500">8</span>
            </div>
          </div>
          {instance.status === 'live' && (
            <div className="absolute top-1 right-1 z-20">
              <Badge className="bg-green-500 text-white text-[9px] h-3.5 px-1 animate-pulse">
                <Radio className="h-2 w-2 mr-0.5" />
                LIVE
              </Badge>
            </div>
          )}
        </div>
      );
    }

    if (isChess) {
      // Use actual board state if available, otherwise use mock pieces
      const boardState = instance.boardState?.board;
      const pieces = boardState ? [] : getChessPieces(instance.id);

      return (
        <div className="relative h-full w-full bg-slate-800 rounded overflow-hidden">
          <div className="absolute inset-0 grid grid-cols-8 grid-rows-8">
            {Array.from({ length: 64 }).map((_, i) => {
              const row = Math.floor(i / 8);
              const col = i % 8;
              const isLight = (row + col) % 2 === 0;

              // Get piece from actual board state
              let piece = null;
              if (boardState && boardState[row] && boardState[row][col]) {
                const boardPiece = boardState[row][col];
                if (boardPiece !== null) {
                  const [color, pieceType] = boardPiece.split('_');
                  piece = {
                    square: i,
                    piece: pieceType,
                    color: color as 'white' | 'black'
                  };
                }
              } else {
                // Use mock pieces if no board state
                piece = pieces.find(p => p.square === i);
              }

              return (
                <div
                  key={i}
                  className={`relative ${isLight ? 'bg-slate-700' : 'bg-slate-600'}`}
                >
                  {piece && (
                    <img
                      src={getPieceImage(piece.piece, piece.color)}
                      alt={`${piece.color} ${piece.piece}`}
                      className="absolute inset-0 w-full h-full p-0.5"
                    />
                  )}
                </div>
              );
            })}
          </div>
          {instance.status === 'live' && (
            <div className="absolute top-1 right-1">
              <Badge className="bg-green-500 text-white text-[9px] h-3.5 px-1 animate-pulse">
                <Radio className="h-2 w-2 mr-0.5" />
                LIVE
              </Badge>
            </div>
          )}
        </div>
      );
    }

    if (isCheckers) {
      return (
        <div className="relative h-full w-full bg-slate-800 rounded overflow-hidden">
          <div className="absolute inset-0 grid grid-cols-8 grid-rows-8">
            {Array.from({ length: 64 }).map((_, i) => {
              const row = Math.floor(i / 8);
              const col = i % 8;
              const isPlayable = (row + col) % 2 === 1;

              return (
                <div
                  key={i}
                  className={`relative ${isPlayable ? 'bg-slate-600' : 'bg-slate-700'}`}
                >
                  {/* Sample checker pieces */}
                  {isPlayable && i < 24 && i % 4 !== 3 && (
                    <div className="absolute inset-1 rounded-full bg-red-600 border border-red-700" />
                  )}
                  {isPlayable && i > 39 && i % 4 !== 0 && (
                    <div className="absolute inset-1 rounded-full bg-slate-900 border border-slate-800" />
                  )}
                </div>
              );
            })}
          </div>
          {instance.status === 'live' && (
            <div className="absolute top-1 right-1">
              <Badge className="bg-green-500 text-white text-[9px] h-3.5 px-1 animate-pulse">
                <Radio className="h-2 w-2 mr-0.5" />
                LIVE
              </Badge>
            </div>
          )}
        </div>
      );
    }

    return (
      <div className="relative h-full w-full bg-gradient-to-br from-slate-800 to-slate-700 rounded flex items-center justify-center">
        <div className="text-slate-600">
          {getGameIcon()}
        </div>
        {instance.status === 'live' && (
          <div className="absolute top-1 right-1">
            <Badge className="bg-green-500 text-white text-[9px] h-3.5 px-1 animate-pulse">
              <Radio className="h-2 w-2 mr-0.5" />
              LIVE
            </Badge>
          </div>
        )}
      </div>
    );
  };

  // Use desktop layout for large screens
  if (isDesktop) {
    return (
      <DesktopGameDetailsLayout
        gameId={gameId}
        gameName={gameName}
        gameType={gameType}
        onBack={onBack}
        onJoinGame={onJoinGame}
        gameInstances={filteredInstances}
        renderGameThumbnail={renderGameThumbnail}
      />
    );
  }

  // Mobile/tablet layout
  return (
    <div className="h-screen bg-slate-950 text-white overflow-hidden">
      <div className="h-full flex flex-col pt-20 pb-4">
      {/* Header */}
      <div className="h-auto sm:h-[88px] bg-slate-900 border-b border-slate-800 p-4 sm:p-3">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center">
            <Button
              variant="ghost"
              size="sm"
              onClick={onBack}
              className="h-7 w-7 p-0 mr-2"
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <div className="flex items-center">
              <div className="h-10 w-10 sm:h-8 sm:w-8 rounded bg-gradient-to-br from-slate-700 to-slate-600 flex items-center justify-center mr-3 sm:mr-2 flex-shrink-0">
                {getGameIcon()}
              </div>
              <div className="flex-1">
                <h1 className="text-lg sm:text-base font-semibold">{gameName}</h1>
                <div className="flex items-center space-x-2 mt-1 sm:mt-0">
                  <Badge className="bg-slate-700 text-slate-300 text-[10px] h-4 px-1.5">
                    <Users className="h-2.5 w-2.5 mr-0.5" />
                    {filteredInstances.length} active
                  </Badge>
                  <Badge className="bg-slate-700 text-slate-300 text-[10px] h-4 px-1.5">
                    <Eye className="h-2.5 w-2.5 mr-0.5" />
                    {filteredInstances.reduce((sum, inst) => sum + inst.viewers, 0)} watching
                  </Badge>
                </div>
              </div>
            </div>
          </div>
          <Button
            variant={isFavorited ? "default" : "outline"}
            size="sm"
            onClick={() => setIsFavorited(!isFavorited)}
            className={`h-7 text-xs ${isFavorited ? "bg-yellow-600 hover:bg-yellow-700" : "border-slate-700"}`}
          >
            <Star className={`h-3 w-3 ${isFavorited ? "fill-current" : ""}`} />
            <span className="ml-1">{isFavorited ? "Favorited" : "Favorite"}</span>
          </Button>
        </div>

        {/* Filter Tabs */}
        <div className="flex space-x-1 overflow-x-auto mt-3 sm:mt-0">
          <Button
            variant={filter === 'all' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setFilter('all')}
            className={`h-6 text-xs px-3 ${filter === 'all' ? 'bg-purple-600' : 'text-slate-400 hover:text-white'}`}
          >
            All ({gameInstances.length})
          </Button>
          <Button
            variant={filter === 'waiting' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setFilter('waiting')}
            className={`h-6 text-xs px-3 ${filter === 'waiting' ? 'bg-purple-600' : 'text-slate-400 hover:text-white'}`}
          >
            Waiting ({gameInstances.filter(g => g.status === 'waiting').length})
          </Button>
          <Button
            variant={filter === 'live' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setFilter('live')}
            className={`h-6 text-xs px-3 ${filter === 'live' ? 'bg-purple-600' : 'text-slate-400 hover:text-white'}`}
          >
            Live ({gameInstances.filter(g => g.status === 'live').length})
          </Button>
        </div>
      </div>

      {/* Main Content - Desktop Layout */}
      <div className="flex flex-1 min-h-0">
        {/* Left Sidebar - Desktop Only */}
        <div className="hidden lg:flex w-64 flex-col gap-3 p-3 bg-slate-950">
          {/* Game Stats */}
          <div className="bg-slate-900 border border-slate-800 rounded-lg p-4">
            <h3 className="text-sm font-semibold text-white mb-3 flex items-center">
              <Trophy className="h-4 w-4 mr-2 text-yellow-500" />
              Game Stats
            </h3>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-xs text-slate-400">Active Games</span>
                <span className="text-sm font-bold text-white">{filteredInstances.length}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-xs text-slate-400">Avg. Stake</span>
                <span className="text-sm font-bold text-white">$62.50</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-xs text-slate-400">Your Wins</span>
                <span className="text-sm font-bold text-green-500">23</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-xs text-slate-400">Win Rate</span>
                <span className="text-sm font-bold text-white">68%</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-xs text-slate-400">Total Earned</span>
                <span className="text-sm font-bold text-yellow-500">$1,245</span>
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="bg-slate-900 border border-slate-800 rounded-lg p-4">
            <h3 className="text-sm font-semibold text-white mb-3">Quick Actions</h3>
            <div className="space-y-2">
              <Button
                className="w-full bg-gradient-to-r from-green-500 to-cyan-500 hover:opacity-90 text-sm"
                onClick={(e) => {
                  alert('Button clicked!'); // Simple test
                  console.log('🎮 Create Game button clicked!');
                  console.log('Event:', e);
                  console.log('Game type:', gameType);
                  console.log('Show create dialog state:', showCreateDialog);
                  if (gameType === 'blur-detective') {
                    console.log('Opening Blur Detective wizard...');
                    setShowBlurDetectiveWizard(true);
                  } else {
                    console.log('Opening Create Game dialog...');
                    setShowCreateDialog(true);
                  }
                }}
              >
                <Plus className="h-4 w-4 mr-2" />
                Create Game
              </Button>
              <Button variant="outline" className="w-full border-slate-700 text-slate-300 hover:bg-slate-800">
                <Star className="h-4 w-4 mr-2" />
                Favorites
              </Button>
            </div>
          </div>

          {/* Recent Activity */}
          <div className="bg-slate-900 border border-slate-800 rounded-lg p-4 flex-1">
            <h3 className="text-sm font-semibold text-white mb-3">Recent Activity</h3>
            <div className="space-y-2 text-xs">
              <div className="flex items-center justify-between py-1">
                <span className="text-slate-400">Won vs Player X</span>
                <span className="text-green-500">+$50</span>
              </div>
              <div className="flex items-center justify-between py-1">
                <span className="text-slate-400">Lost vs Player Y</span>
                <span className="text-red-500">-$25</span>
              </div>
              <div className="flex items-center justify-between py-1">
                <span className="text-slate-400">Won vs Player Z</span>
                <span className="text-green-500">+$75</span>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content Area */}
        <div className="flex-1 flex flex-col">
          {/* Mobile Stats - Show on smaller screens */}
          <div className="lg:hidden bg-slate-900 border-b border-slate-800 p-3">
            <div className="grid grid-cols-3 gap-4 text-center">
              <div>
                <p className="text-xs text-slate-400">Active</p>
                <p className="text-sm font-bold text-white">{filteredInstances.length}</p>
              </div>
              <div>
                <p className="text-xs text-slate-400">Avg. Stake</p>
                <p className="text-sm font-bold text-white">$62.50</p>
              </div>
              <div>
                <p className="text-xs text-slate-400">Win Rate</p>
                <p className="text-sm font-bold text-white">68%</p>
              </div>
            </div>
          </div>

          {/* Game Instances Grid */}
          <div className="flex-1 bg-slate-900 border border-slate-800 lg:border-l-0 overflow-hidden">
            <div className="h-full overflow-auto p-3">
              {isLoading ? (
                <div className="flex items-center justify-center h-64">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
                    <p className="text-slate-400">Loading games...</p>
                  </div>
                </div>
              ) : filteredInstances.length === 0 ? (
                <div className="flex items-center justify-center h-64">
                  <div className="text-center">
                    <p className="text-slate-400 mb-4">No games available</p>
                    <Button
                      className="bg-gradient-to-r from-green-500 to-cyan-500"
                      onClick={() => {
                        console.log('🎮 Create First Game button clicked!');
                        console.log('Game type:', gameType);
                        console.log('Show create dialog state:', showCreateDialog);
                        if (gameType === 'blur-detective') {
                          console.log('Opening Blur Detective wizard...');
                          setShowBlurDetectiveWizard(true);
                        } else {
                          console.log('Opening Create Game dialog...');
                          setShowCreateDialog(true);
                        }
                      }}
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Create First Game
                    </Button>
                  </div>
                </div>
              ) : (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 auto-rows-max">
                {/* REDESIGNED CARDS - 4 COLUMNS ON DESKTOP */}
                {filteredInstances.map((instance) => (
                  <div
                    key={instance.id}
                    className="bg-slate-800 border border-slate-700 rounded-lg p-3 hover:border-slate-600 transition-all cursor-pointer group h-fit"
                    onClick={() => gameType === 'blur-detective' && instance.status === 'waiting' ? setShowBlurDetectiveWizard(true) : onJoinGame(instance.id)}
                  >
                    {/* Game Thumbnail */}
                    <div className="aspect-square mb-3 relative">
                      {renderGameThumbnail(instance)}
                    </div>

                    {/* Game Info */}
                    <div className="space-y-2">
                      {/* Header with stake and status */}
                      <div className="flex items-center justify-between">
                        <Badge className="bg-green-600 text-white text-xs px-1.5 py-0.5">
                          <DollarSign className="h-3 w-3 mr-1" />
                          ${instance.stake}
                        </Badge>
                        {instance.status === 'live' && instance.timeRemaining && (
                          <Badge className="bg-orange-600 text-white text-xs px-1.5 py-0.5">
                            <Clock className="h-3 w-3 mr-1" />
                            {instance.timeRemaining}
                          </Badge>
                        )}
                      </div>

                      {/* Players Section */}
                      {gameType === 'blur-detective' && instance.playerCount !== undefined ? (
                        // Multiplayer display for blur-detective
                        <div>
                          <div className="flex items-center justify-between mb-1">
                            <div className="flex items-center text-xs text-white">
                              <Users className="h-3 w-3 mr-1 text-slate-400" />
                              <span>{instance.playerCount}/{instance.maxPlayers || 8}</span>
                            </div>
                            {instance.status === 'waiting' && (
                              <Badge className="bg-blue-600 text-white text-xs px-1 py-0.5">
                                Open
                              </Badge>
                            )}
                          </div>
                          <p className="text-xs text-slate-400 truncate">
                            Host: {instance.players.player1.name}
                          </p>
                        </div>
                      ) : (
                        // Regular 2-player display
                        <div className="space-y-1">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center">
                              <div className="h-3 w-3 rounded-full bg-slate-600 mr-1.5" />
                              <span className="text-xs text-white truncate">{instance.players.player1.name}</span>
                            </div>
                            {instance.currentTurn === '1' && instance.status === 'live' && (
                              <Badge className="bg-green-500 text-white text-xs px-1 py-0.5 animate-pulse">
                                Turn
                              </Badge>
                            )}
                          </div>
                          {instance.players.player2 ? (
                            <div className="flex items-center justify-between">
                              <div className="flex items-center">
                                <div className="h-3 w-3 rounded-full bg-slate-600 mr-1.5" />
                                <span className="text-xs text-white truncate">{instance.players.player2.name}</span>
                              </div>
                              {instance.currentTurn === '2' && instance.status === 'live' && (
                                <Badge className="bg-green-500 text-white text-xs px-1 py-0.5 animate-pulse">
                                  Turn
                                </Badge>
                              )}
                            </div>
                          ) : (
                            <div className="flex items-center">
                              <div className="h-3 w-3 rounded-full border border-dashed border-slate-600 mr-1.5" />
                              <span className="text-xs text-slate-500">Waiting...</span>
                            </div>
                          )}
                        </div>
                      )}

                      {/* Additional Info */}
                      <div className="flex items-center justify-between text-xs text-slate-400">
                        <div className="flex items-center">
                          <Eye className="h-3 w-3 mr-1" />
                          <span>{instance.viewers} watching</span>
                        </div>
                        {instance.status === 'live' && (
                          <Badge className="bg-red-500 text-white text-xs animate-pulse">
                            LIVE
                          </Badge>
                        )}
                      </div>

                      {/* Action Button */}
                      {instance.status === 'waiting' ? (
                        <Button
                          size="sm"
                          className="w-full bg-gradient-to-r from-green-500 to-cyan-500 hover:opacity-90 text-xs font-medium h-7"
                          onClick={(e) => {
                            e.stopPropagation();
                            if (gameType === 'blur-detective') {
                              setShowBlurDetectiveWizard(true);
                            } else {
                              onJoinGame(instance.id);
                            }
                          }}
                        >
                          <PlayCircle className="h-3 w-3 mr-1" />
                          Join
                        </Button>
                      ) : (
                        <Button
                          variant="outline"
                          size="sm"
                          className="w-full border-slate-600 text-slate-300 hover:bg-slate-700 text-xs font-medium h-7"
                          onClick={(e) => {
                            e.stopPropagation();
                            onJoinGame(instance.id);
                          }}
                        >
                          <Eye className="h-3 w-3 mr-1" />
                          Watch
                        </Button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Create Game Button */}
      <div className="lg:hidden fixed bottom-0 left-0 right-0 p-4 bg-slate-950 border-t border-slate-800">
        <Button
          className="w-full h-12 bg-gradient-to-r from-green-500 to-cyan-500 hover:opacity-90 text-sm font-medium"
          onClick={() => {
            console.log('📱 Mobile Create New Game button clicked!');
            console.log('Game type:', gameType);
            console.log('Show create dialog state:', showCreateDialog);
            if (gameType === 'blur-detective') {
              console.log('Opening Blur Detective wizard...');
              setShowBlurDetectiveWizard(true);
            } else {
              console.log('Opening Create Game dialog...');
              setShowCreateDialog(true);
            }
          }}
        >
          <Plus className="h-5 w-5 mr-2" />
          Create New Game
        </Button>
      </div>

      {/* Create Game Dialog */}
      <CreateGameDialog
        open={showCreateDialog}
        onOpenChange={setShowCreateDialog}
        gameType={gameType}
        gameName={gameName}
        onGameCreated={handleGameCreated}
      />

      {/* Blur Detective Setup Wizard */}
      <BlurDetectiveSetupWizard
        open={showBlurDetectiveWizard}
        onOpenChange={setShowBlurDetectiveWizard}
        mode="create"
      />
      </div>
    </div>
  );
};

export default GameDetailsScreen;