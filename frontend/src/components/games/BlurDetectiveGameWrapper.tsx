import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useGameSocket } from '../../hooks/useGameSocket';
import useAuth from '../../hooks/useAuth';
import BlurDetectiveGame from './BlurDetectiveGame';
import { Button } from '../ui/button';
import { Card } from '../ui/card';
import { Badge } from '../ui/badge';
import { Loader2, Brain, Users, Trophy } from 'lucide-react';
import { type GameData } from './gameTypes';

interface GameSettings {
  gameMode: 'practice' | 'live';
  category: string;
  playerCount: number;
  betAmount: number;
  rounds: number;
  timeLimit: number;
  privateRoom: boolean;
  roomCode?: string;
  isHost?: boolean;
}

interface Player {
  id: string;
  name: string;
  score: number;
  isReady: boolean;
  guessesLeft: number;
  hintsUsed: number;
  avatar?: string;
  isHost?: boolean;
  timeBonus?: number;
  streak?: number;
}

interface ChatMessage {
  id: string;
  playerId: string;
  playerName: string;
  message: string;
  timestamp: Date;
  isSystem?: boolean;
  isGuess?: boolean;
  isCorrect?: boolean;
}

interface GameState {
  status: 'waiting' | 'starting' | 'playing' | 'round-end' | 'game-over';
  currentRound: number;
  totalRounds: number;
  currentImage?: string;
  answer?: string;
  blurLevel: number;
  timeRemaining: number;
  winner?: string;
  roundWinner?: string;
  correctGuesses: number;
  leaderboard?: Player[];
  nextRoundIn?: number;
  revealProgress?: number;
}

interface BlurDetectiveGameWrapperProps {
  game?: GameData;
  onBack?: () => void;
}

export default function BlurDetectiveGameWrapper({ game, onBack }: BlurDetectiveGameWrapperProps = {}) {
  const navigate = useNavigate();
  const location = useLocation();
  const { user } = useAuth();
  
  // Get game settings from location state (passed from wizard)
  const gameSettings = location.state as GameSettings || {
    gameMode: 'live',
    category: 'mixed',
    playerCount: 2,
    betAmount: 10,
    rounds: 3,
    timeLimit: 120,
    privateRoom: false,
    isHost: true
  };
  
  const [gameMode, setGameMode] = useState<'menu' | 'setup' | 'simple' | 'multiplayer'>('multiplayer');
  const [isCreatingGame, setIsCreatingGame] = useState(false);
  const [gameData, setGameData] = useState<GameData | null>({
    id: game?.id || 'demo-game',
    name: 'Blur Detective',
    type: 'blur-detective',
    players: { player1: { id: user?.id || '1', name: user?.username || 'Player1' } },
    currentRound: 1,
    totalRounds: gameSettings.rounds,
    prize: gameSettings.gameMode === 'live' ? gameSettings.betAmount * gameSettings.playerCount : 0,
    viewers: 0,
    status: 'waiting',
    betAmount: gameSettings.betAmount,
    settings: { 
      category: gameSettings.category, 
      maxPlayers: gameSettings.playerCount, 
      rounds: gameSettings.rounds, 
      timeLimit: gameSettings.timeLimit 
    }
  });

  // Game state for multiplayer
  const [players, setPlayers] = useState<Player[]>([
    { 
      id: user?.id || '1', 
      name: user?.username || 'Player1', 
      score: 0, 
      isReady: gameSettings.isHost || false, 
      guessesLeft: 3, 
      hintsUsed: 0, 
      isHost: gameSettings.isHost || false,
      streak: 0 
    }
  ]);
  const [gameState, setGameState] = useState<GameState>({
    status: 'waiting',
    currentRound: 1,
    totalRounds: gameSettings.rounds,
    blurLevel: 40,
    timeRemaining: gameSettings.timeLimit,
    correctGuesses: 0,
    currentImage: '/games/bd/images/celebrity1.jpg'
  });
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([
    { 
      id: '1', 
      playerId: 'system', 
      playerName: 'System', 
      message: gameSettings.isHost ? 'You created the game. Waiting for players...' : 'You joined the game!', 
      timestamp: new Date(), 
      isSystem: true 
    }
  ]);

  // Temporarily disable WebSocket to prevent errors
  const socket = null;
  const connected = true; // Mock as connected for UI demo
  const joinGame = () => {};
  const leaveGame = () => {};
  const sendMessage = () => {};
  const sendGameAction = () => {};
  const error = null;

  // Set game mode based on settings from wizard
  useEffect(() => {
    if (gameSettings.gameMode === 'practice') {
      setGameMode('simple');
    } else {
      setGameMode('multiplayer');
    }
  }, [gameSettings.gameMode]);

  // Socket event handlers
  useEffect(() => {
    if (!socket) return;

    socket.on('gameUpdate', (data: any) => {
      if (data.players) setPlayers(data.players);
      if (data.gameState) setGameState(data.gameState);
      if (data.gameData) setGameData(data.gameData);
    });

    socket.on('chatMessage', (message: ChatMessage) => {
      setChatMessages(prev => [...prev, message]);
    });

    socket.on('playerJoined', (player: Player) => {
      setPlayers(prev => [...prev, player]);
      setChatMessages(prev => [...prev, {
        id: Date.now().toString(),
        playerId: 'system',
        playerName: 'System',
        message: `${player.name} joined the game`,
        timestamp: new Date(),
        isSystem: true
      }]);
    });

    socket.on('playerLeft', (playerId: string) => {
      const player = players.find(p => p.id === playerId);
      setPlayers(prev => prev.filter(p => p.id !== playerId));
      if (player) {
        setChatMessages(prev => [...prev, {
          id: Date.now().toString(),
          playerId: 'system',
          playerName: 'System',
          message: `${player.name} left the game`,
          timestamp: new Date(),
          isSystem: true
        }]);
      }
    });

    socket.on('gameStarted', () => {
      setGameState(prev => ({ ...prev, status: 'starting' }));
    });

    socket.on('roundStart', (data: any) => {
      setGameState(prev => ({
        ...prev,
        status: 'playing',
        currentRound: data.round,
        currentImage: data.image,
        answer: data.answer,
        blurLevel: data.blurLevel,
        timeRemaining: data.timeLimit
      }));
    });

    socket.on('guessResult', (data: any) => {
      const { playerId, playerName, guess, isCorrect, score } = data;
      setChatMessages(prev => [...prev, {
        id: Date.now().toString(),
        playerId,
        playerName,
        message: guess,
        timestamp: new Date(),
        isGuess: true,
        isCorrect
      }]);

      if (isCorrect) {
        setPlayers(prev => prev.map(p => 
          p.id === playerId ? { ...p, score: p.score + score } : p
        ));
      }
    });

    socket.on('roundEnd', (data: any) => {
      setGameState(prev => ({
        ...prev,
        status: 'round-end',
        roundWinner: data.winner,
        answer: data.answer,
        nextRoundIn: data.nextRoundIn
      }));
    });

    socket.on('gameOver', (data: any) => {
      setGameState(prev => ({
        ...prev,
        status: 'game-over',
        winner: data.winner,
        leaderboard: data.leaderboard
      }));
    });

    return () => {
      socket.off('gameUpdate');
      socket.off('chatMessage');
      socket.off('playerJoined');
      socket.off('playerLeft');
      socket.off('gameStarted');
      socket.off('roundStart');
      socket.off('guessResult');
      socket.off('roundEnd');
      socket.off('gameOver');
    };
  }, [socket, players]);

  const handleCreateGame = async (settings: GameSettings) => {
    if (!user) return;
    
    setIsCreatingGame(true);
    
    // Mock game creation for demo - no API call
    setTimeout(() => {
      const mockGameData = {
        id: 'demo-game-' + Date.now(),
        name: 'Blur Detective Game',
        type: 'blur-detective',
        players: { player1: { id: user.id, name: user.username } },
        currentRound: 1,
        totalRounds: settings.rounds,
        prize: settings.betAmount * 4, // Mock prize pool
        viewers: Math.floor(Math.random() * 50) + 10,
        status: 'live' as const,
        betAmount: settings.betAmount,
        settings
      };
      
      setGameData(mockGameData);
      setGameMode('multiplayer');
      setIsCreatingGame(false);
    }, 1000); // Simulate API delay
  };

  const handleJoinGame = async (gameId: string) => {
    if (!user) return;

    // Mock join game for demo - no API call
    const mockGameData = {
      id: gameId,
      name: 'Blur Detective Game',
      type: 'blur-detective',
      players: { 
        player1: { id: '1', name: 'HostPlayer' },
        player2: { id: user.id, name: user.username }
      },
      currentRound: 2,
      totalRounds: 5,
      prize: 400,
      viewers: 24,
      status: 'live' as const,
      betAmount: 100,
      settings: { category: 'actors', maxPlayers: 8, rounds: 5, timeLimit: 60 }
    };
    
    setGameData(mockGameData);
    setGameMode('multiplayer');
  };

  const handleSendMessage = (message: string) => {
    // Mock message sending for demo
    const newMessage: ChatMessage = {
      id: Date.now().toString(),
      playerId: user?.id || 'player1',
      playerName: user?.username || 'You',
      message,
      timestamp: new Date(),
      isGuess: message.toLowerCase().includes('dicaprio') || message.toLowerCase().includes('leonardo')
    };
    setChatMessages(prev => [...prev, newMessage]);
  };

  const handleStartGame = () => {
    // Mock game start for demo
    setGameState(prev => ({
      ...prev,
      status: 'starting'
    }));
    
    setTimeout(() => {
      setGameState(prev => ({
        ...prev,
        status: 'playing',
        currentRound: 1,
        blurLevel: 40,
        timeRemaining: 60
      }));
    }, 2000);
  };

  const handleMakeGuess = (guess: string) => {
    // Mock guess handling for demo
    const isCorrect = guess.toLowerCase().includes('leonardo') || guess.toLowerCase().includes('dicaprio');
    
    // Add guess as chat message
    const guessMessage: ChatMessage = {
      id: Date.now().toString(),
      playerId: user?.id || 'player1',
      playerName: user?.username || 'You',
      message: guess,
      timestamp: new Date(),
      isGuess: true,
      isCorrect
    };
    setChatMessages(prev => [...prev, guessMessage]);
    
    // Update player guesses
    setPlayers(prev => prev.map(p => 
      p.id === (user?.id || 'player1') 
        ? { ...p, guessesLeft: Math.max(0, p.guessesLeft - 1), score: isCorrect ? p.score + 100 : p.score }
        : p
    ));
    
    // If correct, simulate round end
    if (isCorrect) {
      setTimeout(() => {
        setGameState(prev => ({
          ...prev,
          status: 'round-end',
          answer: 'Leonardo DiCaprio',
          roundWinner: user?.id || 'player1',
          revealProgress: 100
        }));
        
        // Start next round after delay
        setTimeout(() => {
          if (gameState.currentRound < gameState.totalRounds) {
            setGameState(prev => ({
              ...prev,
              status: 'playing',
              currentRound: prev.currentRound + 1,
              blurLevel: 40,
              timeRemaining: 60,
              currentImage: prev.currentRound % 2 === 0 ? '/games/bd/images/celebrity1.jpg' : '/games/bd/images/celebrity2.jpg'
            }));
            setPlayers(prev => prev.map(p => ({ ...p, guessesLeft: 3 })));
          } else {
            setGameState(prev => ({
              ...prev,
              status: 'game-over',
              winner: user?.id || 'player1'
            }));
          }
        }, 3000);
      }, 1000);
    }
  };

  const handleUseHint = (hintId: string) => {
    // Mock hint usage for demo
    const hintMessages: { [key: string]: string } = {
      '1': 'Hint: This is a famous Hollywood actor',
      '2': 'Hint: Known for Titanic and Inception',
      '3': 'Hint: Won Oscar for The Revenant'
    };
    
    if (hintMessages[hintId]) {
      const hintMessage: ChatMessage = {
        id: Date.now().toString(),
        playerId: 'system',
        playerName: 'System',
        message: hintMessages[hintId],
        timestamp: new Date(),
        isSystem: true
      };
      setChatMessages(prev => [...prev, hintMessage]);
      
      // Deduct points for hint
      const hintCost = hintId === '1' ? 10 : hintId === '2' ? 20 : 30;
      setPlayers(prev => prev.map(p => 
        p.id === (user?.id || 'player1') 
          ? { ...p, score: Math.max(0, p.score - hintCost), hintsUsed: p.hintsUsed + 1 }
          : p
      ));
    }
  };

  const handleLeaveGame = () => {
    if (gameData?.id) {
      leaveGame(gameData.id);
    }
    if (onBack) {
      onBack();
    } else {
      navigate('/games');
    }
  };

  const handleSimpleGameEnd = (score: number) => {
    // Could save practice score or show results
    if (onBack) {
      onBack();
    } else {
      navigate('/games');
    }
  };

  // Render loading state
  if (isCreatingGame || (gameMode === 'multiplayer' && !connected)) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="p-8">
          <div className="flex flex-col items-center space-y-4">
            <Loader2 className="w-8 h-8 animate-spin" />
            <p className="text-muted-foreground">
              {isCreatingGame ? 'Creating game...' : 'Connecting...'}
            </p>
          </div>
        </Card>
      </div>
    );
  }

  // Render error state
  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="p-8">
          <div className="flex flex-col items-center space-y-4 text-center">
            <div className="text-red-500 text-xl">⚠️</div>
            <p className="font-semibold">Connection Error</p>
            <p className="text-muted-foreground">{error}</p>
            <Button onClick={() => onBack ? onBack() : navigate('/games')}>Back to Games</Button>
          </div>
        </Card>
      </div>
    );
  }

  // Skip menu and setup - we're coming from the wizard
  // These modes are no longer needed

  // Render simple game for practice mode
  if (gameMode === 'simple' || gameSettings.gameMode === 'practice') {
    // Use settings from wizard for practice mode
    const practiceGameData = {
      id: 'practice-mode',
      name: 'Practice Mode',
      type: 'blur-detective',
      players: { player1: { id: user?.id || 'player1', name: user?.username || 'You' } },
      currentRound: 1,
      totalRounds: gameSettings.rounds,
      prize: 0,
      viewers: 0,
      status: 'live' as const,
      betAmount: 0,
      settings: { 
        category: gameSettings.category, 
        maxPlayers: 1, 
        rounds: gameSettings.rounds, 
        timeLimit: gameSettings.timeLimit 
      }
    };

    const practicePlayerData = [
      { 
        id: user?.id || 'player1', 
        name: `${user?.username || 'You'} (Practice)`, 
        score: 0, 
        isReady: true, 
        guessesLeft: 3, 
        hintsUsed: 0, 
        isHost: true 
      }
    ];

    const practiceGameState = {
      status: 'playing' as const,
      currentRound: 1,
      totalRounds: gameSettings.rounds,
      blurLevel: 40,
      timeRemaining: gameSettings.timeLimit,
      correctGuesses: 0,
      currentImage: '/games/bd/images/celebrity1.jpg'
    };

    return (
      <BlurDetectiveGame
        gameId="practice-mode"
        playerId="player1"
        playerName="You"
        isHost={true}
        onLeaveGame={() => onBack ? onBack() : navigate('/games')}
        players={practicePlayerData}
        gameState={practiceGameState}
        chatMessages={[]}
        onSendMessage={() => {}}
        onStartGame={() => {}}
        onMakeGuess={() => {}}
        onUseHint={() => {}}
        betAmount={0}
        category="mixed"
        maxPlayers={1}
        rounds={3}
        timeLimit={60}
      />
    );
  }

  // Render multiplayer game
  if (gameMode === 'multiplayer' && gameData) {
    const currentPlayer = players.find(p => p.id === user?.id);
    const isHost = currentPlayer?.isHost || false;

    return (
      <BlurDetectiveGame
        gameId={gameData.id}
        playerId={user?.id || ''}
        playerName={user?.username || ''}
        isHost={isHost}
        onLeaveGame={handleLeaveGame}
        players={players}
        gameState={gameState}
        chatMessages={chatMessages}
        onSendMessage={handleSendMessage}
        onStartGame={handleStartGame}
        onMakeGuess={handleMakeGuess}
        onUseHint={handleUseHint}
        betAmount={gameData.betAmount}
        category={gameData.settings?.category || 'mixed'}
        maxPlayers={gameData.settings?.maxPlayers || 4}
        rounds={gameData.settings?.rounds || 5}
        timeLimit={gameData.settings?.timeLimit || 60}
      />
    );
  }

  return null;
}