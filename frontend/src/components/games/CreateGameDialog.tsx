import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { gameInstancesService } from '@/services/gameInstances';
import useAuth from '@/hooks/useAuth';
import { DollarSign, Users, Loader2 } from 'lucide-react';

interface CreateGameDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  gameType: string;
  gameName: string;
  onGameCreated: (gameId: string) => void;
}

const CreateGameDialog: React.FC<CreateGameDialogProps> = ({
  open,
  onOpenChange,
  gameType,
  gameName,
  onGameCreated
}) => {
  const [stakeAmount, setStakeAmount] = useState('10');
  const [maxPlayers, setMaxPlayers] = useState('2');
  const [isCreating, setIsCreating] = useState(false);
  const [error, setError] = useState('');
  const { user } = useAuth();

  // Debug logging
  React.useEffect(() => {
    console.log('🎯 CreateGameDialog state changed:');
    console.log('  - open:', open);
    console.log('  - gameType:', gameType);
    console.log('  - gameName:', gameName);
    console.log('  - user:', user);
  }, [open, gameType, gameName, user]);

  const handleCreate = async () => {
    if (!user) {
      setError('You must be logged in to create a game');
      return;
    }

    const stake = parseFloat(stakeAmount);
    if (isNaN(stake) || stake <= 0) {
      setError('Please enter a valid stake amount');
      return;
    }

    try {
      setIsCreating(true);
      setError('');

      const game = await gameInstancesService.createGame({
        game_type: gameType.replace(/-/g, '_'),
        wager_amount: stake,
        max_players: parseInt(maxPlayers)
      });

      onGameCreated(game.id);
      onOpenChange(false);

      // Reset form
      setStakeAmount('10');
      setMaxPlayers('2');
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Failed to create game');
    } finally {
      setIsCreating(false);
    }
  };

  const getMaxPlayersOptions = () => {
    // Different games have different player limits
    switch (gameType) {
      case 'chess':
      case 'checkers':
      case 'rock-paper-scissors':
      case 'highlight-hero':
        return ['2'];
      case 'word-jumble':
      case 'quiz-arena':
        return ['2', '3', '4', '6', '8'];
      default:
        return ['2'];
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px] bg-slate-900 text-white border-slate-800">
        <DialogHeader>
          <DialogTitle>Create {gameName} Game</DialogTitle>
        </DialogHeader>

        <div className="space-y-4 py-4">
          {/* Stake Amount */}
          <div className="space-y-2">
            <Label htmlFor="stake">Stake Amount ($)</Label>
            <div className="relative">
              <DollarSign className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-slate-400" />
              <Input
                id="stake"
                type="number"
                value={stakeAmount}
                onChange={(e) => setStakeAmount(e.target.value)}
                className="pl-10 bg-slate-800 border-slate-700"
                placeholder="10"
                min="1"
                step="0.01"
              />
            </div>
            <p className="text-xs text-slate-400">Minimum stake: $1</p>
          </div>

          {/* Max Players (for multiplayer games) */}
          {getMaxPlayersOptions().length > 1 && (
            <div className="space-y-2">
              <Label htmlFor="maxPlayers">Max Players</Label>
              <Select value={maxPlayers} onValueChange={setMaxPlayers}>
                <SelectTrigger className="bg-slate-800 border-slate-700">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {getMaxPlayersOptions().map(option => (
                    <SelectItem key={option} value={option}>
                      <div className="flex items-center">
                        <Users className="h-4 w-4 mr-2" />
                        {option} Players
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}

          {/* Error Message */}
          {error && (
            <div className="p-3 bg-red-500/10 border border-red-500/50 rounded text-sm text-red-400">
              {error}
            </div>
          )}
        </div>

        <div className="flex justify-end space-x-2">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isCreating}
            className="border-slate-700"
          >
            Cancel
          </Button>
          <Button
            onClick={handleCreate}
            disabled={isCreating}
            className="bg-gradient-to-r from-green-500 to-cyan-500"
          >
            {isCreating ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Creating...
              </>
            ) : (
              'Create Game'
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default CreateGameDialog;