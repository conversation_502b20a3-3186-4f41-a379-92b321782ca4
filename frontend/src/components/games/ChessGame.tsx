import React, { useState, useEffect, useRef, useCallback } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import useGameStore from '@/stores/gameStore';
import useAuth from '@/hooks/useAuth';
import { useGameSocket } from '@/services/gameSocket';
import {
  Clock,
  MessageSquare,
  Users,
  Crown,
  AlertCircle,
  RotateCcw,
  ChevronDown,
  X,
  Send,
  ArrowLeft
} from 'lucide-react';

// Chess constants
const FILES = ['a', 'b', 'c', 'd', 'e', 'f', 'g', 'h'];
const RANKS = ['8', '7', '6', '5', '4', '3', '2', '1'];

// Initial board setup
const INITIAL_BOARD = [
  ['r', 'n', 'b', 'q', 'k', 'b', 'n', 'r'], // Black pieces
  ['p', 'p', 'p', 'p', 'p', 'p', 'p', 'p'], // Black pawns
  ['.', '.', '.', '.', '.', '.', '.', '.'], // Empty squares
  ['.', '.', '.', '.', '.', '.', '.', '.'],
  ['.', '.', '.', '.', '.', '.', '.', '.'],
  ['.', '.', '.', '.', '.', '.', '.', '.'],
  ['P', 'P', 'P', 'P', 'P', 'P', 'P', 'P'], // White pawns
  ['R', 'N', 'B', 'Q', 'K', 'B', 'N', 'R'], // White pieces
];

// Time controls
const TIME_CONTROLS = {
  Bullet: { initial: 60, increment: 1 },
  Blitz: { initial: 180, increment: 2 },
  Rapid: { initial: 600, increment: 10 },
  Classical: { initial: 1800, increment: 30 },
};

// Piece images mapping
const PIECE_IMAGES = {
  'K': '/images/chess/white-king.png',
  'Q': '/images/chess/white-queen.png',
  'R': '/images/chess/white-rook.png',
  'B': '/images/chess/white-bishop.png',
  'N': '/images/chess/white-knight.png',
  'P': '/images/chess/white-pawn.png',
  'k': '/images/chess/black-king.png',
  'q': '/images/chess/black-queen.png',
  'r': '/images/chess/black-rook.png',
  'b': '/images/chess/black-bishop.png',
  'n': '/images/chess/black-knight.png',
  'p': '/images/chess/black-pawn.png',
};

interface ChessGameProps {
  gameId: string;
  isTimedGame?: boolean;
  timeLimit?: number; // in seconds
  wagerAmount?: number;
  onGameEnd?: () => void;
  onBack?: () => void;
}

interface ChessMove {
  from: number;
  to: number;
  piece: string;
  captured?: string;
  promotion?: string;
  isCheck?: boolean;
  isCheckmate?: boolean;
  isStalemate?: boolean;
  notation?: string;
}

const ChessGame: React.FC<ChessGameProps> = ({
  gameId,
  isTimedGame = false,
  timeLimit = 600, // 10 minutes default
  wagerAmount = 100,
  onGameEnd,
  onBack
}) => {
  const { user } = useAuth();
  const { currentGame } = useGameStore();
  const game = currentGame;

  // State variables
  const [selectedSquare, setSelectedSquare] = useState<number | null>(null);
  const [validMoves, setValidMoves] = useState<number[]>([]);
  const [boardState, setBoardState] = useState(INITIAL_BOARD);
  const [moveHistory, setMoveHistory] = useState<ChessMove[]>([]);
  const [isWhitePlayer, setIsWhitePlayer] = useState(true);
  const [currentTurn, setCurrentTurn] = useState<'white' | 'black'>('white');
  const [gameStatus, setGameStatus] = useState<'playing' | 'check' | 'checkmate' | 'stalemate' | 'draw'>('playing');
  const [capturedPieces, setCapturedPieces] = useState<{ white: string[], black: string[] }>({ white: [], black: [] });
  const [castlingRights, setCastlingRights] = useState({
    whiteKing: true, whiteQueen: true,
    blackKing: true, blackQueen: true
  });
  const [enPassantSquare, setEnPassantSquare] = useState<number | null>(null);
  const [isPromoting, setIsPromoting] = useState(false);
  const [promotionSquare, setPromotionSquare] = useState<number | null>(null);
  const [whiteTime, setWhiteTime] = useState(timeLimit);
  const [blackTime, setBlackTime] = useState(timeLimit);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const [gameTime, setGameTime] = useState(0);
  const [showChat, setShowChat] = useState(true);
  const [showRules, setShowRules] = useState(false);

  // Chat state
  const [messages, setMessages] = useState<any[]>([
    { text: "Game started! White player goes first.", type: "system" },
    { text: "Welcome to Chess Arena!", type: "system" }
  ]);
  const [messageInput, setMessageInput] = useState('');

  // Mock spectator data
  const [spectators] = useState([
    { id: 1, name: 'JohnDoe', avatar: '👤' },
    { id: 2, name: 'SarahM', avatar: '👧' },
    { id: 3, name: 'BetMaster', avatar: '🎲' }
  ]);

  // Mock side bets
  const [sideBets] = useState([
    { user: 'CryptoKing', amount: 25, side: 'white' },
    { user: 'GamingPro', amount: 50, side: 'black' },
    { user: 'RiskTaker', amount: 30, side: 'white' }
  ]);

  // Initialize game timer
  useEffect(() => {
    const interval = setInterval(() => {
      setGameTime(prev => prev + 1);

      // Update player timers for timed games
      if (isTimedGame && gameStatus === 'playing') {
        if (currentTurn === 'white' && whiteTime > 0) {
          setWhiteTime(prev => Math.max(0, prev - 1));
        } else if (currentTurn === 'black' && blackTime > 0) {
          setBlackTime(prev => Math.max(0, prev - 1));
        }
      }
    }, 1000);

    intervalRef.current = interval;
    return () => {
      if (intervalRef.current) clearInterval(intervalRef.current);
    };
  }, [currentTurn, isTimedGame, gameStatus, whiteTime, blackTime]);

  // Format time display
  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds < 10 ? '0' : ''}${remainingSeconds}`;
  };

  // Add message to chat
  const addMessage = (text: string, type: 'user' | 'system' = 'system') => {
    setMessages(prev => [...prev, { text, type }]);
  };

  // Send chat message
  const sendMessage = () => {
    if (messageInput.trim()) {
      addMessage(messageInput, 'user');
      setMessageInput('');
    }
  };

  // Handle incoming moves from other players
  const handleGameUpdate = useCallback((data: any) => {
    if (data.type === 'move' && data.playerId !== user?.id) {
      // Update game state with opponent's move
      const move = data.move as ChessMove;
      makeMove(move.from, move.to, move.promotion);
    } else if (data.type === 'game_state') {
      // Full game state update
      setBoardState(data.board);
      setCurrentTurn(data.currentTurn);
      setGameStatus(data.gameStatus);
    }
  }, [user?.id]);

  // Socket connection for real-time game updates
  const { sendMove } = useGameSocket(gameId, handleGameUpdate);

  // Utility function to check if a piece is white
  const isWhitePiece = (piece: string): boolean => {
    return piece === piece.toUpperCase() && piece !== '.';
  };

  // Get the piece at a given index
  const getPiece = (board: string[][], index: number): string => {
    const rank = Math.floor(index / 8);
    const file = index % 8;
    return board[rank]?.[file] || '.';
  };

  const getPieceFromBoard = (board: string[][], index: number): string => {
    const rank = Math.floor(index / 8);
    const file = index % 8;
    return board[rank][file];
  };

  // Board-aware move functions that don't check for checks
  const getPawnMovesWithBoard = (board: string[][], rank: number, file: number, isWhite: boolean): number[] => {
    const moves: number[] = [];
    const direction = isWhite ? -1 : 1;
    const startRank = isWhite ? 6 : 1;

    // One square forward
    const oneStep = (rank + direction) * 8 + file;
    if (isValidSquare(rank + direction, file) && getPieceFromBoard(board, oneStep) === '.') {
      moves.push(oneStep);

      // Two squares forward from starting position
      if (rank === startRank) {
        const twoStep = (rank + 2 * direction) * 8 + file;
        if (getPieceFromBoard(board, twoStep) === '.') {
          moves.push(twoStep);
        }
      }
    }

    // Captures
    for (const fileOffset of [-1, 1]) {
      const captureFile = file + fileOffset;
      if (isValidSquare(rank + direction, captureFile)) {
        const captureIndex = (rank + direction) * 8 + captureFile;
        const targetPiece = getPieceFromBoard(board, captureIndex);
        if (targetPiece !== '.' && isWhitePiece(targetPiece) !== isWhite) {
          moves.push(captureIndex);
        }
      }
    }

    return moves;
  };

  const getKnightMovesWithBoard = (board: string[][], rank: number, file: number): number[] => {
    const moves: number[] = [];
    const knightMoves = [
      [-2, -1], [-2, 1], [-1, -2], [-1, 2],
      [1, -2], [1, 2], [2, -1], [2, 1]
    ];

    for (const [dr, df] of knightMoves) {
      const newRank = rank + dr;
      const newFile = file + df;
      if (isValidSquare(newRank, newFile)) {
        const targetIndex = newRank * 8 + newFile;
        const targetPiece = getPieceFromBoard(board, targetIndex);
        const movingPiece = board[rank][file];
        if (targetPiece === '.' || isWhitePiece(targetPiece) !== isWhitePiece(movingPiece)) {
          moves.push(targetIndex);
        }
      }
    }

    return moves;
  };

  const getBishopMovesWithBoard = (board: string[][], rank: number, file: number): number[] => {
    const moves: number[] = [];
    const directions = [[-1, -1], [-1, 1], [1, -1], [1, 1]];
    const movingPiece = board[rank][file];

    for (const [dr, df] of directions) {
      let currentRank = rank + dr;
      let currentFile = file + df;

      while (isValidSquare(currentRank, currentFile)) {
        const targetIndex = currentRank * 8 + currentFile;
        const targetPiece = getPieceFromBoard(board, targetIndex);

        if (targetPiece === '.') {
          moves.push(targetIndex);
        } else {
          if (isWhitePiece(targetPiece) !== isWhitePiece(movingPiece)) {
            moves.push(targetIndex);
          }
          break;
        }

        currentRank += dr;
        currentFile += df;
      }
    }

    return moves;
  };

  const getRookMovesWithBoard = (board: string[][], rank: number, file: number): number[] => {
    const moves: number[] = [];
    const directions = [[-1, 0], [1, 0], [0, -1], [0, 1]];
    const movingPiece = board[rank][file];

    for (const [dr, df] of directions) {
      let currentRank = rank + dr;
      let currentFile = file + df;

      while (isValidSquare(currentRank, currentFile)) {
        const targetIndex = currentRank * 8 + currentFile;
        const targetPiece = getPieceFromBoard(board, targetIndex);

        if (targetPiece === '.') {
          moves.push(targetIndex);
        } else {
          if (isWhitePiece(targetPiece) !== isWhitePiece(movingPiece)) {
            moves.push(targetIndex);
          }
          break;
        }

        currentRank += dr;
        currentFile += df;
      }
    }

    return moves;
  };

  const getQueenMovesWithBoard = (board: string[][], rank: number, file: number): number[] => {
    return [...getBishopMovesWithBoard(board, rank, file), ...getRookMovesWithBoard(board, rank, file)];
  };

  const getBasicKingMoves = (rank: number, file: number): number[] => {
    const moves: number[] = [];
    const directions = [
      [-1, -1], [-1, 0], [-1, 1],
      [0, -1],           [0, 1],
      [1, -1],  [1, 0],  [1, 1]
    ];

    for (const [dr, df] of directions) {
      const newRank = rank + dr;
      const newFile = file + df;
      if (isValidSquare(newRank, newFile)) {
        moves.push(newRank * 8 + newFile);
      }
    }

    return moves;
  };

  const getKingMoves = (rank: number, file: number, isWhite: boolean): number[] => {
    const moves: number[] = [];
    const directions = [
      [-1, -1], [-1, 0], [-1, 1],
      [0, -1], [0, 1],
      [1, -1], [1, 0], [1, 1]
    ];

    for (const [dr, df] of directions) {
      const newRank = rank + dr;
      const newFile = file + df;
      if (isValidSquare(newRank, newFile)) {
        const targetIndex = newRank * 8 + newFile;
        const targetPiece = getPiece(boardState, targetIndex);
        if (targetPiece === '.' || isWhitePiece(targetPiece) !== isWhite) {
          moves.push(targetIndex);
        }
      }
    }

    // Castling
    const kingStartFile = 4;
    if (file === kingStartFile && rank === (isWhite ? 7 : 0)) {
      // King-side castling
      if ((isWhite && castlingRights.whiteKing) || (!isWhite && castlingRights.blackKing)) {
        if (getPiece(boardState, rank * 8 + 5) === '.' &&
            getPiece(boardState, rank * 8 + 6) === '.' &&
            getPiece(boardState, rank * 8 + 7) === (isWhite ? 'R' : 'r')) {
          if (!isSquareAttacked(rank * 8 + 4, !isWhite) &&
              !isSquareAttacked(rank * 8 + 5, !isWhite) &&
              !isSquareAttacked(rank * 8 + 6, !isWhite)) {
            moves.push(rank * 8 + 6);
          }
        }
      }

      // Queen-side castling
      if ((isWhite && castlingRights.whiteQueen) || (!isWhite && castlingRights.blackQueen)) {
        if (getPiece(boardState, rank * 8 + 3) === '.' &&
            getPiece(boardState, rank * 8 + 2) === '.' &&
            getPiece(boardState, rank * 8 + 1) === '.' &&
            getPiece(boardState, rank * 8 + 0) === (isWhite ? 'R' : 'r')) {
          if (!isSquareAttacked(rank * 8 + 4, !isWhite) &&
              !isSquareAttacked(rank * 8 + 3, !isWhite) &&
              !isSquareAttacked(rank * 8 + 2, !isWhite)) {
            moves.push(rank * 8 + 2);
          }
        }
      }
    }

    return moves;
  };

  const isValidSquare = (rank: number, file: number): boolean => {
    return rank >= 0 && rank < 8 && file >= 0 && file < 8;
  };

  const isSquareAttacked = (squareIndex: number, byWhite: boolean): boolean => {
    for (let i = 0; i < 64; i++) {
      const piece = getPiece(boardState, i);
      if (piece !== '.' && isWhitePiece(piece) === byWhite) {
        const attackerRank = Math.floor(i / 8);
        const attackerFile = i % 8;
        const pieceType = piece.toUpperCase();

        let possibleMoves: number[] = [];
        switch (pieceType) {
          case 'P':
            const direction = byWhite ? -1 : 1;
            const targetRank = Math.floor(squareIndex / 8);
            const targetFile = squareIndex % 8;
            if (targetRank === attackerRank + direction && Math.abs(targetFile - attackerFile) === 1) {
              return true;
            }
            break;
          case 'N':
            possibleMoves = getKnightMoves(attackerRank, attackerFile);
            break;
          case 'B':
            possibleMoves = getBishopMoves(attackerRank, attackerFile);
            break;
          case 'R':
            possibleMoves = getRookMoves(attackerRank, attackerFile);
            break;
          case 'Q':
            possibleMoves = getQueenMoves(attackerRank, attackerFile);
            break;
          case 'K':
            const kingDirections = [
              [-1, -1], [-1, 0], [-1, 1],
              [0, -1], [0, 1],
              [1, -1], [1, 0], [1, 1]
            ];
            for (const [dr, df] of kingDirections) {
              const newRank = attackerRank + dr;
              const newFile = attackerFile + df;
              if (isValidSquare(newRank, newFile)) {
                const index = newRank * 8 + newFile;
                if (index === squareIndex) return true;
              }
            }
            break;
        }

        if (possibleMoves.includes(squareIndex)) {
          return true;
        }
      }
    }

    return false;
  };

  const isInCheck = (board: string[][], isWhite: boolean): boolean => {
    // Find the king
    let kingIndex = -1;
    for (let i = 0; i < 64; i++) {
      const piece = getPiece(board, i);
      if (piece === (isWhite ? 'K' : 'k')) {
        kingIndex = i;
        break;
      }
    }

    if (kingIndex === -1) return false;

    // Check if any opposing piece can attack the king
    for (let i = 0; i < 64; i++) {
      const piece = getPiece(board, i);
      if (piece !== '.' && isWhitePiece(piece) !== isWhite) {
        const moves = getMovesForPiece(board, i, piece);
        if (moves.includes(kingIndex)) {
          return true;
        }
      }
    }

    return false;
  };

  const getMovesForPiece = (board: string[][], index: number, piece: string): number[] => {
    const rank = Math.floor(index / 8);
    const file = index % 8;
    const pieceType = piece.toUpperCase();

    let moves: number[] = [];
    switch (pieceType) {
      case 'P':
        moves = getPawnMovesWithBoard(board, rank, file, isWhitePiece(piece));
        break;
      case 'N':
        moves = getKnightMovesWithBoard(board, rank, file);
        break;
      case 'B':
        moves = getBishopMovesWithBoard(board, rank, file);
        break;
      case 'R':
        moves = getRookMovesWithBoard(board, rank, file);
        break;
      case 'Q':
        moves = getQueenMovesWithBoard(board, rank, file);
        break;
      case 'K':
        moves = getBasicKingMoves(rank, file);
        break;
    }

    return moves;
  };

  const wouldBeInCheck = (from: number, to: number, board: string[][]): boolean => {
    // Create a copy of the board
    const newBoard = board.map(row => [...row]);

    // Make the move
    const fromRank = Math.floor(from / 8);
    const fromFile = from % 8;
    const toRank = Math.floor(to / 8);
    const toFile = to % 8;

    const piece = newBoard[fromRank][fromFile];
    newBoard[toRank][toFile] = piece;
    newBoard[fromRank][fromFile] = '.';

    // Check if the king is in check
    return isInCheck(newBoard, isWhitePiece(piece));
  };

  const makeMove = (from: number, to: number, promotionPiece?: string) => {
    const fromRank = Math.floor(from / 8);
    const fromFile = from % 8;
    const toRank = Math.floor(to / 8);
    const toFile = to % 8;

    // console.log('Making move:', { from, to, fromRank, fromFile, toRank, toFile });

    const piece = boardState[fromRank][fromFile];
    const capturedPiece = boardState[toRank][toFile];

    // console.log('Moving piece:', piece, 'to capture:', capturedPiece);

    // Create new board state
    const newBoard = boardState.map(row => [...row]);
    newBoard[toRank][toFile] = promotionPiece ? (isWhitePiece(piece) ? promotionPiece : promotionPiece.toLowerCase()) : piece;
    newBoard[fromRank][fromFile] = '.';

    // console.log('New board state created');

    // Handle castling
    if (piece.toUpperCase() === 'K' && Math.abs(fromFile - toFile) === 2) {
      if (toFile === 6) { // King-side castling
        newBoard[toRank][5] = newBoard[toRank][7];
        newBoard[toRank][7] = '.';
      } else if (toFile === 2) { // Queen-side castling
        newBoard[toRank][3] = newBoard[toRank][0];
        newBoard[toRank][0] = '.';
      }
    }

    // Handle en passant
    if (piece.toUpperCase() === 'P' && fromFile !== toFile && capturedPiece === '.') {
      if (to === enPassantSquare) {
        const capturedPawnRank = isWhitePiece(piece) ? toRank + 1 : toRank - 1;
        newBoard[capturedPawnRank][toFile] = '.';
      }
    }

    // Update castling rights
    const newCastlingRights = { ...castlingRights };
    if (piece === 'K') {
      newCastlingRights.whiteKing = false;
      newCastlingRights.whiteQueen = false;
    } else if (piece === 'k') {
      newCastlingRights.blackKing = false;
      newCastlingRights.blackQueen = false;
    } else if (piece === 'R') {
      if (from === 56) newCastlingRights.whiteQueen = false;
      if (from === 63) newCastlingRights.whiteKing = false;
    } else if (piece === 'r') {
      if (from === 0) newCastlingRights.blackQueen = false;
      if (from === 7) newCastlingRights.blackKing = false;
    }

    // Update en passant square
    let newEnPassant = null;
    if (piece.toUpperCase() === 'P' && Math.abs(fromRank - toRank) === 2) {
      newEnPassant = (fromRank + toRank) / 2 * 8 + fromFile;
    }

    // Update game state
    // console.log('Updating board state');
    setBoardState(newBoard);
    setCastlingRights(newCastlingRights);
    setEnPassantSquare(newEnPassant);

    // Update captured pieces
    if (capturedPiece !== '.') {
      const newCaptured = { ...capturedPieces };
      if (isWhitePiece(capturedPiece)) {
        newCaptured.white = [...newCaptured.white, capturedPiece];
      } else {
        newCaptured.black = [...newCaptured.black, capturedPiece];
      }
      setCapturedPieces(newCaptured);
    }

    // Switch turn
    // console.log('Switching turn');
    setCurrentTurn(currentTurn === 'white' ? 'black' : 'white');

    // Log the move
    // console.log('Move completed');
    setMoveHistory([...moveHistory, {
      from, to, piece, captured: capturedPiece,
      promotion: promotionPiece
    }]);

    // Clear selection
    setSelectedSquare(null);
    setValidMoves([]);

    // Check game status
    const isCheck = isInCheck(newBoard, currentTurn === 'white' ? false : true);
    if (isCheck) {
      setGameStatus('check');
      // console.log('Check!');
    } else {
      setGameStatus('playing');
    }

    // Send move via WebSocket
    const moveData: ChessMove = {
      from,
      to,
      piece,
      captured: capturedPiece,
      promotion: promotionPiece
    };

    sendMove({
      from,
      to,
      promotion: promotionPiece,
      gameState: {
        board: newBoard,
        currentTurn: currentTurn === 'white' ? 'black' : 'white',
        castlingRights: newCastlingRights,
        enPassantSquare: newEnPassant,
        capturedPieces,
        moveHistory: [...moveHistory, moveData],
        gameStatus: isCheck ? 'check' : 'playing'
      }
    });
  };

  const handlePromotion = (pieceType: string) => {
    if (promotionSquare !== null) {
      makeMove(selectedSquare!, promotionSquare, pieceType);
      setIsPromoting(false);
      setPromotionSquare(null);
    }
  };

  const handleSquareClick = (index: number) => {
    // console.log('Clicked square:', index);

    const piece = getPiece(boardState, index);
    const rank = Math.floor(index / 8);
    const file = index % 8;

    if (selectedSquare === null) {
      // Select a piece
      if (piece !== '.' &&
          ((currentTurn === 'white' && isWhitePiece(piece)) ||
           (currentTurn === 'black' && !isWhitePiece(piece)))) {
        // console.log('Selecting piece:', piece, 'at', index);
        setSelectedSquare(index);

        // Calculate valid moves
        const moves = calculateValidMoves(rank, file);
        // console.log('Valid moves:', moves);
        setValidMoves(moves);
      }
    } else {
      // Move selected piece
      if (validMoves.includes(index)) {
        // console.log('Moving to:', index);
        // Check if this is a pawn promotion
        const piece = boardState[Math.floor(selectedSquare / 8)][selectedSquare % 8];
        const toRank = Math.floor(index / 8);

        if (piece.toUpperCase() === 'P' && (toRank === 0 || toRank === 7)) {
          setPromotionSquare(index);
          setIsPromoting(true);
        } else {
          makeMove(selectedSquare, index);
        }
      } else if (index === selectedSquare) {
        // Deselect
        // console.log('Deselecting piece');
        setSelectedSquare(null);
        setValidMoves([]);
      } else if (piece !== '.' &&
                ((currentTurn === 'white' && isWhitePiece(piece)) ||
                 (currentTurn === 'black' && !isWhitePiece(piece)))) {
        // Select a different piece
        // console.log('Selecting different piece:', piece, 'at', index);
        setSelectedSquare(index);
        const moves = calculateValidMoves(rank, file);
        setValidMoves(moves);
      }
    }
  };

  const calculateValidMoves = (rank: number, file: number): number[] => {
    const piece = boardState[rank][file];
    const isWhite = isWhitePiece(piece);
    const pieceType = piece.toUpperCase();

    let moves: number[] = [];

    switch (pieceType) {
      case 'P':
        moves = getPawnMoves(rank, file, isWhite);
        break;
      case 'N':
        moves = getKnightMoves(rank, file);
        break;
      case 'B':
        moves = getBishopMoves(rank, file);
        break;
      case 'R':
        moves = getRookMoves(rank, file);
        break;
      case 'Q':
        moves = getQueenMoves(rank, file);
        break;
      case 'K':
        moves = getKingMoves(rank, file, isWhite);
        break;
    }

    // Filter out moves that would leave king in check
    return moves.filter(move => !wouldBeInCheck(rank * 8 + file, move, boardState));
  };

  const getPawnMoves = (rank: number, file: number, isWhite: boolean): number[] => {
    const moves: number[] = [];
    const direction = isWhite ? -1 : 1;
    const startRank = isWhite ? 6 : 1;

    // One square forward
    const oneStep = (rank + direction) * 8 + file;
    if (isValidSquare(rank + direction, file) && getPiece(boardState, oneStep) === '.') {
      moves.push(oneStep);

      // Two squares forward from starting position
      if (rank === startRank) {
        const twoStep = (rank + 2 * direction) * 8 + file;
        if (getPiece(boardState, twoStep) === '.') {
          moves.push(twoStep);
        }
      }
    }

    // Captures
    for (const fileOffset of [-1, 1]) {
      const captureFile = file + fileOffset;
      if (isValidSquare(rank + direction, captureFile)) {
        const captureIndex = (rank + direction) * 8 + captureFile;
        const targetPiece = getPiece(boardState, captureIndex);
        if (targetPiece !== '.' && isWhitePiece(targetPiece) !== isWhite) {
          moves.push(captureIndex);
        }
      }
    }

    // En passant
    if (enPassantSquare !== null) {
      const epRank = Math.floor(enPassantSquare / 8);
      const epFile = enPassantSquare % 8;
      if (epRank === rank + direction && Math.abs(epFile - file) === 1) {
        moves.push(enPassantSquare);
      }
    }

    return moves;
  };

  const getKnightMoves = (rank: number, file: number): number[] => {
    const moves: number[] = [];
    const knightMoves = [
      [-2, -1], [-2, 1], [-1, -2], [-1, 2],
      [1, -2], [1, 2], [2, -1], [2, 1]
    ];

    for (const [dr, df] of knightMoves) {
      const newRank = rank + dr;
      const newFile = file + df;
      if (isValidSquare(newRank, newFile)) {
        const targetIndex = newRank * 8 + newFile;
        const targetPiece = getPiece(boardState, targetIndex);
        const movingPiece = boardState[rank][file];
        if (targetPiece === '.' || isWhitePiece(targetPiece) !== isWhitePiece(movingPiece)) {
          moves.push(targetIndex);
        }
      }
    }

    return moves;
  };

  const getBishopMoves = (rank: number, file: number): number[] => {
    const moves: number[] = [];
    const directions = [[-1, -1], [-1, 1], [1, -1], [1, 1]];

    for (const [dr, df] of directions) {
      let currentRank = rank + dr;
      let currentFile = file + df;

      while (isValidSquare(currentRank, currentFile)) {
        const targetIndex = currentRank * 8 + currentFile;
        const targetPiece = getPiece(boardState, targetIndex);

        if (targetPiece === '.') {
          moves.push(targetIndex);
        } else {
          if (isWhitePiece(targetPiece) !== isWhitePiece(boardState[rank][file])) {
            moves.push(targetIndex);
          }
          break;
        }

        currentRank += dr;
        currentFile += df;
      }
    }

    return moves;
  };

  const getRookMoves = (rank: number, file: number): number[] => {
    const moves: number[] = [];
    const directions = [[-1, 0], [1, 0], [0, -1], [0, 1]];

    for (const [dr, df] of directions) {
      let currentRank = rank + dr;
      let currentFile = file + df;

      while (isValidSquare(currentRank, currentFile)) {
        const targetIndex = currentRank * 8 + currentFile;
        const targetPiece = getPiece(boardState, targetIndex);

        if (targetPiece === '.') {
          moves.push(targetIndex);
        } else {
          if (isWhitePiece(targetPiece) !== isWhitePiece(boardState[rank][file])) {
            moves.push(targetIndex);
          }
          break;
        }

        currentRank += dr;
        currentFile += df;
      }
    }

    return moves;
  };

  const getQueenMoves = (rank: number, file: number): number[] => {
    return [...getBishopMoves(rank, file), ...getRookMoves(rank, file)];
  };

  const resetGame = () => {
    setBoardState(INITIAL_BOARD);
    setSelectedSquare(null);
    setValidMoves([]);
    setMoveHistory([]);
    setCurrentTurn('white');
    setGameStatus('playing');
    setCapturedPieces({ white: [], black: [] });
    setEnPassantSquare(null);
    setIsPromoting(false);
    setPromotionSquare(null);
    setCastlingRights({
      whiteKing: true, whiteQueen: true,
      blackKing: true, blackQueen: true
    });
    setWhiteTime(timeLimit);
    setBlackTime(timeLimit);
    setGameTime(0);
    addMessage('Game has been reset!', 'system');
  };

  const renderSquare = (index: number) => {
    const piece = getPiece(boardState, index);
    const rank = Math.floor(index / 8);
    const file = index % 8;
    const isLight = (rank + file) % 2 === 0;
    const isSelected = selectedSquare === index;
    const isValidMove = validMoves.includes(index);
    const isLastMove = moveHistory.length > 0 &&
      (moveHistory[moveHistory.length - 1].from === index ||
       moveHistory[moveHistory.length - 1].to === index);

    // Adjust square index for board orientation
    const displayIndex = isWhitePlayer ? index : 63 - index;

    return (
      <div
        key={displayIndex}
        onClick={() => handleSquareClick(index)}
        className={`
          relative flex items-center justify-center cursor-pointer aspect-square
          ${isLight ? 'bg-amber-100' : 'bg-amber-900'}
          ${isSelected ? 'ring-4 ring-yellow-400' : ''}
          ${isLastMove ? 'bg-yellow-400' : ''}
          ${isValidMove ? 'after:absolute after:w-4 after:h-4 after:bg-green-500 after:rounded-full after:opacity-60' : ''}
          ${validMoves.includes(index) && piece !== '.' ? 'ring-2 ring-red-500' : ''}
          hover:brightness-110 transition-all
        `}
      >
        {piece !== '.' && (
          <img
            src={PIECE_IMAGES[piece as keyof typeof PIECE_IMAGES]}
            alt={piece}
            className="w-[70%] h-[70%] object-contain select-none absolute"
          />
        )}

        {/* Square coordinates */}
        {file === 0 && (
          <span className="absolute left-1 top-1 text-xs font-semibold text-gray-700">
            {isWhitePlayer ? RANKS[rank] : RANKS[7 - rank]}
          </span>
        )}
        {rank === 7 && (
          <span className="absolute right-1 bottom-1 text-xs font-semibold text-gray-700">
            {isWhitePlayer ? FILES[file] : FILES[7 - file]}
          </span>
        )}
      </div>
    );
  };

  // Render the game board
  const renderBoard = () => {
    return (
      <div className="aspect-square bg-gray-800 border-2 border-gray-600 rounded-lg">
        <div className="grid grid-cols-8 gap-0 w-full h-full">
          {Array.from({ length: 64 }).map((_, index) => renderSquare(index))}
        </div>
      </div>
    );
  };

  return (
    <div className="w-full min-h-screen bg-slate-950 flex flex-col pt-16">
      <style>{`
        /* Custom scrollbar styles */
        .custom-scrollbar::-webkit-scrollbar {
          width: 6px;
        }
        .custom-scrollbar::-webkit-scrollbar-track {
          background: rgba(51, 65, 85, 0.3);
          border-radius: 3px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
          background: rgba(100, 116, 139, 0.5);
          border-radius: 3px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
          background: rgba(100, 116, 139, 0.7);
        }
      `}</style>

      {/* Main content area with padding to ensure visibility above footer */}
      <div className="flex-1 p-2 pb-16 overflow-hidden">
        <div className="grid grid-cols-12 gap-2 h-full">
          {/* Left Panel - Game Info */}
          <div className="col-span-3 flex flex-col gap-2 h-full overflow-hidden">
            <div className="flex-1 overflow-y-auto custom-scrollbar pr-1 space-y-2">
              {/* Game Status */}
              <div className="bg-slate-900 border border-slate-800 rounded-sm p-3">
                <div className="flex justify-between items-center mb-3">
                  <h3 className="text-sm font-semibold text-white">Chess Match</h3>
                  <Badge className="bg-green-500 text-white text-xs">LIVE</Badge>
                </div>

                <div className="space-y-2">
                  <div className="bg-slate-800 p-2 rounded">
                    <div className="text-xs text-slate-400">Current Turn</div>
                    <div className={`text-sm font-bold ${currentTurn === 'white' ? 'text-white' : 'text-gray-400'}`}>
                      {currentTurn === 'white' ? 'White' : 'Black'} to Move
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-2">
                    <div className="bg-slate-800 p-2 rounded">
                      <div className="text-xs text-slate-400">Wager</div>
                      <div className="text-sm font-bold text-white">${wagerAmount}</div>
                    </div>
                    <div className="bg-slate-800 p-2 rounded">
                      <div className="text-xs text-slate-400">Time</div>
                      <div className="text-sm font-bold text-white">{formatTime(gameTime)}</div>
                    </div>
                  </div>

                  {gameStatus === 'check' && (
                    <div className="bg-red-900/50 border border-red-700 p-2 rounded">
                      <div className="text-sm font-bold text-red-400">CHECK!</div>
                    </div>
                  )}
                </div>
              </div>

              {/* Player Info */}
              <div className="bg-slate-900 border border-slate-800 rounded-sm p-3">
                <h3 className="text-sm font-semibold text-white mb-2">Players</h3>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-white rounded-full"></div>
                      <span className="text-sm text-white">WhiteKnight</span>
                    </div>
                    <span className="text-xs text-green-500">$50</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-gray-800 rounded-full"></div>
                      <span className="text-sm text-white">BlackBishop</span>
                    </div>
                    <span className="text-xs text-green-500">$50</span>
                  </div>
                </div>
              </div>

              {/* Timer (for timed games) */}
              {isTimedGame && (
                <div className="bg-slate-900 border border-slate-800 rounded-sm p-3">
                  <h3 className="text-sm font-semibold text-white mb-2">Time Remaining</h3>
                  <div className="space-y-2">
                    <div>
                      <div className="flex justify-between text-xs mb-1">
                        <span className="text-white">White</span>
                        <span className="text-white">{formatTime(whiteTime)}</span>
                      </div>
                      <Progress value={(whiteTime / timeLimit) * 100} className="h-1.5" />
                    </div>
                    <div>
                      <div className="flex justify-between text-xs mb-1">
                        <span className="text-gray-400">Black</span>
                        <span className="text-white">{formatTime(blackTime)}</span>
                      </div>
                      <Progress value={(blackTime / timeLimit) * 100} className="h-1.5" />
                    </div>
                  </div>
                </div>
              )}

              {/* Captured Pieces */}
              <div className="bg-slate-900 border border-slate-800 rounded-sm p-3">
                <h3 className="text-sm font-semibold text-white mb-2">Captured</h3>
                <div className="space-y-2">
                  <div>
                    <span className="text-xs text-white">White Pieces:</span>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {capturedPieces.white.map((piece, i) => (
                        <span key={i} className="text-lg">{PIECE_UNICODE[piece as keyof typeof PIECE_UNICODE]}</span>
                      ))}
                    </div>
                  </div>
                  <div>
                    <span className="text-xs text-gray-400">Black Pieces:</span>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {capturedPieces.black.map((piece, i) => (
                        <span key={i} className="text-lg">{PIECE_UNICODE[piece as keyof typeof PIECE_UNICODE]}</span>
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              {/* Side Bets */}
              <div className="bg-slate-900 border border-slate-800 rounded-sm p-3">
                <h3 className="text-sm font-semibold text-white mb-2">Side Bets</h3>
                <div className="space-y-1 overflow-y-auto max-h-32 custom-scrollbar pr-1">
                  {sideBets.map((bet, i) => (
                    <div key={i} className="flex justify-between text-xs">
                      <span className="text-slate-300">{bet.user}</span>
                      <span className={`font-bold ${bet.side === 'white' ? 'text-white' : 'text-gray-400'}`}>
                        ${bet.amount} on {bet.side}
                      </span>
                    </div>
                  ))}
                </div>
                <div className="mt-2 pt-2 border-t border-slate-700">
                  <div className="flex justify-between text-xs">
                    <span className="text-slate-400">Total Pool:</span>
                    <span className="text-green-500 font-bold">$205</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Center - Game Board */}
          <div className="col-span-6 flex flex-col items-center justify-center p-4 overflow-y-auto custom-scrollbar">
            <div className="w-full max-w-xl">
              {renderBoard()}
            </div>

            {/* Game Controls */}
            <div className="flex gap-3 mt-4">
              {onBack && (
                <Button onClick={onBack} size="sm" className="bg-blue-600 hover:bg-blue-700 text-xs">
                  <ChevronDown className="h-3 w-3 mr-1 rotate-90" />
                  Back
                </Button>
              )}
              <Button onClick={resetGame} size="sm" className="bg-green-600 hover:bg-green-700 text-xs">
                <RotateCcw className="h-3 w-3 mr-1" />
                Reset
              </Button>
              <Button
                onClick={() => setShowRules(!showRules)}
                size="sm"
                variant="outline"
                className="border-slate-700 text-xs"
              >
                <AlertCircle className="h-3 w-3 mr-1" />
                Rules
              </Button>
            </div>

            {/* Rules Panel */}
            {showRules && (
              <div className="mt-3 bg-slate-900 border border-slate-800 rounded-sm p-3 max-w-md">
                <div className="flex justify-between items-center mb-2">
                  <h3 className="text-sm font-semibold text-white">Quick Rules</h3>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => setShowRules(false)}
                    className="h-6 w-6 p-0"
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </div>
                <div className="text-xs text-slate-300 space-y-1">
                  <p>• Click a piece to select, click square to move</p>
                  <p>• Each piece has specific movement patterns</p>
                  <p>• Capture by moving to opponent's square</p>
                  <p>• Protect your King - Check means it's under attack</p>
                  <p>• Castle by moving King two squares toward Rook</p>
                  <p>• En Passant: special pawn capture move</p>
                </div>
              </div>
            )}
          </div>

          {/* Right Panel - Chat & Spectators */}
          <div className="col-span-3 flex flex-col gap-2 h-full overflow-hidden">
            {/* Fixed spectators section */}
            <div className="flex flex-col gap-2">
              {/* Spectators */}
              <div className="bg-slate-900 border border-slate-800 rounded-sm p-3">
                <div className="flex justify-between items-center mb-2">
                  <h3 className="text-sm font-semibold text-white">Spectators</h3>
                  <div className="flex items-center gap-1">
                    <Users className="h-3 w-3 text-green-500" />
                    <span className="text-xs text-green-500">{spectators.length}</span>
                  </div>
                </div>
                <div className="flex -space-x-2">
                  {spectators.map((spectator) => (
                    <div
                      key={spectator.id}
                      className="w-8 h-8 rounded-full bg-slate-700 border-2 border-slate-900 flex items-center justify-center text-xs"
                      title={spectator.name}
                    >
                      {spectator.avatar}
                    </div>
                  ))}
                  <div className="w-8 h-8 rounded-full bg-slate-800 border-2 border-slate-900 flex items-center justify-center text-xs text-slate-400">
                    +{Math.max(0, spectators.length - 3)}
                  </div>
                </div>
              </div>
            </div>

            {/* Chat - takes remaining space */}
            <div className="bg-slate-900 border border-slate-800 rounded-sm flex-1 flex flex-col overflow-hidden min-h-0 relative">
              <div className="p-3 border-b border-slate-800 flex justify-between items-center shrink-0">
                <h3 className="text-sm font-semibold text-white">Chat</h3>
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => setShowChat(!showChat)}
                  className="h-6 w-6 p-0"
                >
                  {showChat ? <ChevronDown className="h-3 w-3" /> : <MessageSquare className="h-3 w-3" />}
                </Button>
              </div>

              {showChat && (
                <>
                  <div className="flex-1 overflow-y-auto p-3 space-y-1 custom-scrollbar min-h-0 pb-16">
                    {messages.map((msg, i) => (
                      <div key={i} className={`text-xs ${msg.type === 'system' ? 'text-slate-400 italic' : 'text-white'}`}>
                        {msg.text}
                      </div>
                    ))}
                  </div>

                  <div className="absolute bottom-0 left-0 right-0 p-2 bg-slate-900 border-t border-slate-800">
                    <div className="flex gap-1">
                      <input
                        type="text"
                        value={messageInput}
                        onChange={(e) => setMessageInput(e.target.value)}
                        onKeyPress={(e) => e.key === 'Enter' && sendMessage()}
                        placeholder="Type a message..."
                        className="flex-1 bg-slate-800 text-white text-xs rounded px-2 py-1 focus:outline-none focus:ring-1 focus:ring-purple-500"
                      />
                      <Button
                        onClick={sendMessage}
                        size="sm"
                        className="bg-purple-600 hover:bg-purple-700 h-7 px-2"
                      >
                        <Send className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                </>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Promotion Modal */}
      {isPromoting && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-slate-900 border border-slate-800 rounded-lg p-6">
            <h3 className="text-xl font-bold text-white mb-4">Choose Promotion</h3>
            <div className="grid grid-cols-4 gap-4">
              {['Q', 'R', 'B', 'N'].map((piece) => (
                <button
                  key={piece}
                  onClick={() => handlePromotion(piece)}
                  className="w-20 h-20 bg-slate-800 hover:bg-slate-700 border border-slate-700 rounded flex items-center justify-center text-4xl"
                >
                  <img
                    src={PIECE_IMAGES[currentTurn === 'white' ? piece : piece.toLowerCase() as keyof typeof PIECE_IMAGES]}
                    alt={piece}
                    className="w-16 h-16 object-contain"
                  />
                </button>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// Define PIECE_UNICODE for captured pieces display
const PIECE_UNICODE = {
  'K': '♔',
  'Q': '♕',
  'R': '♖',
  'B': '♗',
  'N': '♘',
  'P': '♙',
  'k': '♚',
  'q': '♛',
  'r': '♜',
  'b': '♝',
  'n': '♞',
  'p': '♟',
};

export default ChessGame;