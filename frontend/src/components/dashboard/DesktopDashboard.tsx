import { useState, useEffect } from 'react';
import {
  Wallet,
  BarChart4,
  Trophy,
  Users,
  Flame,
  TrendingUp,
  LineChart,
  Activity,
  Zap,
  ChevronUp,
  ChevronDown,
  Gamepad2,
  Brain,
  Target,
  Calendar,
  MessageSquare,
  Home,
  Store,
  Swords,
  DollarSign,
  ChevronRight
} from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import api from '@/services/api';
import useWalletStore from '@/stores/walletStore';
import useUserStore from '@/stores/userStore';

const DesktopDashboard = () => {
  const navigate = useNavigate();
  const [activeWidget, setActiveWidget] = useState('overview');
  const { balance } = useWalletStore();
  const { user } = useUserStore();

  const [leaderboardData, setLeaderboardData] = useState({ entries: [] });
  const [userStats, setUserStats] = useState(null);
  const [liveEvents, setLiveEvents] = useState([]);
  const [marketTrends, setMarketTrends] = useState([]);
  const [recentActivity, setRecentActivity] = useState([]);
  const [upcomingEvents, setUpcomingEvents] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      const [leaderboardRes, sportsRes] = await Promise.all([
        api.get('/api/leaderboard', { params: { limit: 7 } }),
        api.get('/api/sports/events', { params: { status: 'live', limit: 2 } })
      ]);

      setLeaderboardData(leaderboardRes.data);
      setLiveEvents(sportsRes.data);

      // Load user stats if authenticated
      if (user?.id) {
        const statsRes = await api.get(`/api/leaderboard/stats`, { params: { user_id: user.id } });
        setUserStats(statsRes.data);
      }
    } catch (error) {
      // console.error('Error loading dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  // Control widgets for different modules
  const ControlWidget = ({ type }: { type: string }) => {
    switch (type) {
      case 'overview':
        return (
          <div className="bg-slate-800 rounded-lg p-2">
            <h3 className="text-sm font-semibold mb-2">Dashboard Overview</h3>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm text-slate-400">Active Bets</span>
                <span className="text-sm font-medium">12</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-slate-400">Pending Challenges</span>
                <span className="text-sm font-medium">3</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-slate-400">Live Games</span>
                <span className="text-sm font-medium">2</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-slate-400">Win Rate</span>
                <span className="text-sm font-medium text-green-400">67%</span>
              </div>
            </div>
          </div>
        );

      case 'marketplace':
        return (
          <div className="bg-slate-800 rounded-lg p-2">
            <h3 className="text-sm font-semibold mb-2">Marketplace Controls</h3>
            <div className="space-y-2">
              <button
                onClick={() => navigate('/marketplace/create')}
                className="w-full py-1 bg-green-600 hover:bg-green-700 rounded-md text-xs font-medium"
              >
                Create New Bet
              </button>
              <div className="grid grid-cols-2 gap-1">
                <button className="py-1 px-2 bg-slate-700 hover:bg-slate-600 rounded text-xs">
                  My Bets
                </button>
                <button className="py-1 px-2 bg-slate-700 hover:bg-slate-600 rounded text-xs">
                  Watchlist
                </button>
              </div>
              <div className="text-xs text-slate-400">
                <div className="flex justify-between mb-1">
                  <span>Market Volume (24h)</span>
                  <span className="text-white">$1.2M</span>
                </div>
                <div className="flex justify-between">
                  <span>Active Markets</span>
                  <span className="text-white">847</span>
                </div>
              </div>
            </div>
          </div>
        );

      case 'games':
        return (
          <div className="bg-slate-800 rounded-lg p-2">
            <h3 className="text-sm font-semibold mb-2">Game Center</h3>
            <div className="space-y-2">
              <button
                onClick={() => navigate('/games/create')}
                className="w-full py-1 bg-purple-600 hover:bg-purple-700 rounded-md text-xs font-medium"
              >
                Start New Game
              </button>
              <div className="space-y-1">
                <button
                  onClick={() => navigate('/games/chess')}
                  className="w-full py-1 bg-slate-700 hover:bg-slate-600 rounded text-xs flex items-center justify-between"
                >
                  <span>Chess</span>
                  <span className="text-xs text-green-400">3 waiting</span>
                </button>
                <button
                  onClick={() => navigate('/games/checkers')}
                  className="w-full py-1 bg-slate-700 hover:bg-slate-600 rounded text-xs flex items-center justify-between"
                >
                  <span>Checkers</span>
                  <span className="text-xs text-green-400">5 waiting</span>
                </button>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-slate-950 text-white pt-16">
      {/* Main Content Area */}
      <div className="flex h-[calc(100vh-64px)]">
        {/* Left Sidebar - Control Widgets */}
        <div className="w-64 bg-slate-900 border-r border-slate-800 p-2 overflow-y-auto">
          <div className="space-y-2">
            {/* Widget Selector */}
            <div className="bg-slate-800 rounded-lg p-2">
              <label className="text-sm text-slate-400 mb-2 block">Active Module</label>
              <select
                value={activeWidget}
                onChange={(e) => setActiveWidget(e.target.value)}
                className="w-full bg-slate-700 rounded px-3 py-2 text-sm"
              >
                <option value="overview">Dashboard Overview</option>
                <option value="marketplace">Marketplace</option>
                <option value="games">Game Center</option>
              </select>
            </div>

            {/* Active Control Widget */}
            <ControlWidget type={activeWidget} />

            {/* Quick Stats */}
            <div className="bg-slate-800 rounded-lg p-2">
              <h3 className="text-sm font-medium mb-2">Quick Stats</h3>
              <div className="space-y-1">
                <div className="flex justify-between text-sm">
                  <span className="text-slate-400">Win Rate</span>
                  <span className="font-medium">{userStats?.win_rate || 0}%</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-slate-400">Current Streak</span>
                  <span className="font-medium text-green-500">{userStats?.current_streak || 0}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-slate-400">This Month</span>
                  <span className="font-medium text-green-500">+${userStats?.monthly_winnings || 0}</span>
                </div>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="bg-slate-800 rounded-lg p-2">
              <h3 className="text-sm font-medium mb-2">Quick Actions</h3>
              <div className="space-y-1">
                <button
                  onClick={() => navigate('/games/create')}
                  className="w-full py-1 bg-purple-600 hover:bg-purple-700 rounded text-xs flex items-center justify-center"
                >
                  <Gamepad2 className="h-4 w-4 mr-2" />
                  New Game
                </button>
                <button
                  onClick={() => navigate('/marketplace')}
                  className="w-full py-1 bg-green-600 hover:bg-green-700 rounded text-xs flex items-center justify-center"
                >
                  <Store className="h-4 w-4 mr-2" />
                  Browse Bets
                </button>
                <button
                  onClick={() => navigate('/wallet')}
                  className="w-full py-1 bg-blue-600 hover:bg-blue-700 rounded text-xs flex items-center justify-center"
                >
                  <Wallet className="h-4 w-4 mr-2" />
                  Deposit
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content Grid */}
        <div className="flex-1 p-2 overflow-y-auto">
          <div className="grid grid-cols-12 gap-2">
            {/* Stats Row */}
            <div className="col-span-12 grid grid-cols-4 gap-2">
              <div className="bg-slate-900 rounded-lg p-3 border border-slate-800">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-slate-400">Total Balance</p>
                    <p className="text-xl font-bold">${balance.toLocaleString()}</p>
                    <p className="text-xs text-green-500">+12.5% this month</p>
                  </div>
                  <Wallet className="h-6 w-6 text-green-400" />
                </div>
              </div>

              <div className="bg-slate-900 rounded-lg p-3 border border-slate-800">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-slate-400">Total Wins</p>
                    <p className="text-xl font-bold">{userStats?.total_wins || 0}</p>
                    <p className="text-xs text-slate-500">Win rate: {userStats?.win_rate || 0}%</p>
                  </div>
                  <Trophy className="h-6 w-6 text-yellow-400" />
                </div>
              </div>

              <div className="bg-slate-900 rounded-lg p-3 border border-slate-800">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-slate-400">Current Streak</p>
                    <p className="text-xl font-bold">{userStats?.current_streak || 0}</p>
                    <p className="text-xs text-slate-500">Best: {userStats?.best_streak || 0}</p>
                  </div>
                  <Flame className="h-6 w-6 text-orange-400" />
                </div>
              </div>

              <div className="bg-slate-900 rounded-lg p-3 border border-slate-800">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-slate-400">This Week</p>
                    <p className="text-xl font-bold">${userStats?.weekly_winnings || 0}</p>
                    <p className="text-xs text-green-500">+23.1% vs last week</p>
                  </div>
                  <BarChart4 className="h-6 w-6 text-blue-400" />
                </div>
              </div>
            </div>

            {/* Recent Activity & Live Markets */}
            <div className="col-span-8 row-span-2">
              <div className="bg-slate-900 rounded-lg border border-slate-800 p-3 h-full">
                <div className="flex items-center justify-between mb-2">
                  <h2 className="text-base font-semibold">Live Markets & Activity</h2>
                  <button
                    onClick={() => navigate('/sports')}
                    className="text-sm text-blue-400 hover:text-blue-300"
                  >
                    View All
                  </button>
                </div>

                {/* Live Markets Grid */}
                <div className="grid grid-cols-2 gap-2 mb-3">
                  {liveEvents.map(event => (
                    <div key={event.id} className="bg-slate-800 rounded-lg p-2 relative overflow-hidden">
                      <div className="absolute -right-4 -top-4 w-16 h-16 bg-red-500/10 rounded-full animate-pulse"></div>

                      <div className="flex items-center justify-between mb-2">
                        <span className="text-xs bg-red-600 text-white px-2 py-0.5 rounded-full">LIVE</span>
                        <span className="text-xs text-slate-400">{event.minute}</span>
                      </div>

                      <div className="mb-3">
                        <div className="flex justify-between items-center">
                          <span className="font-medium">{event.home_team?.name}</span>
                          <span className="font-bold text-xl mx-3">{event.score || '0-0'}</span>
                          <span className="font-medium">{event.away_team?.name}</span>
                        </div>
                      </div>

                      <div className="flex items-center justify-between">
                        <span className="text-xs text-slate-400">{event.viewers || 0} watching</span>
                        <button
                          onClick={() => navigate('/sports')}
                          className="text-xs bg-blue-600 hover:bg-blue-700 px-3 py-1 rounded"
                        >
                          Place Bet
                        </button>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Recent Activity */}
                <div>
                  <h3 className="text-sm font-medium mb-2">Recent Activity</h3>
                  <div className="space-y-2">
                    {recentActivity.length === 0 ? (
                      <p className="text-xs text-slate-400">No recent activity</p>
                    ) : (
                      recentActivity.map(activity => (
                        <div key={activity.id} className="flex items-center justify-between bg-slate-800 rounded-lg p-2">
                          <div className="flex items-center space-x-3">
                            <div className={`h-8 w-8 rounded-full flex items-center justify-center ${
                              activity.type === 'win' ? 'bg-green-500/20' :
                              activity.type === 'bet' ? 'bg-blue-500/20' :
                              'bg-purple-500/20'
                            }`}>
                              {activity.type === 'win' ? <Trophy className="h-4 w-4 text-green-500" /> :
                               activity.type === 'bet' ? <DollarSign className="h-4 w-4 text-blue-500" /> :
                               <Swords className="h-4 w-4 text-purple-500" />}
                            </div>
                            <div>
                              <p className="text-sm font-medium">{activity.title}</p>
                              <p className="text-xs text-slate-400">{activity.time}</p>
                            </div>
                          </div>
                          <div className="text-right">
                            <p className={`text-sm font-medium ${
                              activity.type === 'win' ? 'text-green-500' : ''
                            }`}>
                              {activity.type === 'win' ? '+' : '-'}${activity.amount}
                            </p>
                            {activity.opponent && (
                              <p className="text-xs text-slate-400">vs {activity.opponent}</p>
                            )}
                          </div>
                        </div>
                      ))
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Leaderboard */}
            <div className="col-span-4 row-span-2">
              <div className="bg-slate-900 rounded-lg border border-slate-800 p-3 h-full flex flex-col">
                <div className="flex items-center justify-between mb-2">
                  <h2 className="text-base font-semibold">Leaderboard</h2>
                  <select className="bg-slate-800 rounded px-2 py-1 text-sm">
                    <option>All Time</option>
                    <option>This Month</option>
                    <option>This Week</option>
                  </select>
                </div>

                <div className="space-y-1 flex-1 overflow-y-auto">
                  {leaderboardData.entries.map((player, index) => (
                    <div key={player.id} className="flex items-center justify-between py-1">
                      <div className="flex items-center space-x-2">
                        <div className={`h-6 w-6 rounded-full flex items-center justify-center text-xs font-bold ${
                          index === 0 ? 'bg-yellow-500 text-black' :
                          index === 1 ? 'bg-gray-300 text-black' :
                          index === 2 ? 'bg-amber-600 text-white' :
                          'bg-slate-700'
                        }`}>
                          {index + 1}
                        </div>
                        <div>
                          <p className="text-sm font-medium">{player.username}</p>
                          <p className="text-xs text-slate-400">{player.preferred_category}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-medium text-green-500">${(player.winnings || player.total_winnings || 0).toLocaleString()}</p>
                        <p className="text-xs text-slate-400">{player.win_rate || 0}% win</p>
                      </div>
                    </div>
                  ))}
                </div>

                <div className="border-t border-slate-700 mt-2 pt-2">
                  <div className="bg-slate-800 rounded-lg p-2">
                    <p className="text-xs text-slate-400 mb-1">Your Position</p>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <div className="h-6 w-6 rounded-full bg-blue-600 flex items-center justify-center text-xs font-bold">
                          {leaderboardData.user_position || 'N/A'}
                        </div>
                        <div>
                          <p className="text-sm font-medium">You</p>
                          <p className="text-xs text-slate-400">Mixed Games</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-medium text-green-500">${balance.toLocaleString()}</p>
                        <p className="text-xs text-slate-400">{userStats?.win_rate || 0}% win</p>
                      </div>
                    </div>
                  </div>
                </div>

                <button
                  onClick={() => navigate('/leaderboard')}
                  className="w-full mt-2 py-1 bg-slate-800 hover:bg-slate-700 rounded-md text-xs"
                >
                  View Full Rankings
                </button>
              </div>
            </div>

            {/* Upcoming Events */}
            <div className="col-span-6">
              <div className="bg-slate-900 rounded-lg border border-slate-800 p-3">
                <div className="flex items-center justify-between mb-2">
                  <h2 className="text-base font-semibold">Upcoming Events</h2>
                  <button
                    onClick={() => navigate('/sports')}
                    className="text-sm text-blue-400 hover:text-blue-300"
                  >
                    View Calendar
                  </button>
                </div>

                <div className="space-y-2">
                  {upcomingEvents.length === 0 ? (
                    <p className="text-xs text-slate-400">No upcoming events</p>
                  ) : (
                    upcomingEvents.map(event => (
                      <div key={event.id} className="bg-slate-800 rounded-lg p-2">
                        <div className="flex items-center justify-between mb-2">
                          <h3 className="font-medium">{event.title}</h3>
                          <span className="text-sm bg-blue-600 text-white px-2 py-1 rounded">
                            ${event.prize} Prize
                          </span>
                        </div>
                        <div className="flex items-center justify-between text-sm text-slate-400">
                          <div className="flex items-center space-x-4">
                            <span className="flex items-center">
                              <Calendar className="h-4 w-4 mr-1" />
                              {event.time}
                            </span>
                            <span className="flex items-center">
                              <Users className="h-4 w-4 mr-1" />
                              {event.participants} joined
                            </span>
                          </div>
                          <button className="text-blue-400 hover:text-blue-300">
                            Register →
                          </button>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </div>
            </div>

            {/* Market Trends */}
            <div className="col-span-6">
              <div className="bg-slate-900 rounded-lg border border-slate-800 p-3">
                <div className="flex items-center justify-between mb-2">
                  <h2 className="text-base font-semibold">Market Trends</h2>
                  <button
                    onClick={() => navigate('/marketplace')}
                    className="text-sm text-blue-400 hover:text-blue-300"
                  >
                    View Markets
                  </button>
                </div>

                <div className="space-y-2">
                  {marketTrends.length === 0 ? (
                    <p className="text-xs text-slate-400">No market data available</p>
                  ) : (
                    marketTrends.map(trend => (
                      <div key={trend.id} className="flex items-center justify-between bg-slate-800 rounded-lg p-2">
                        <div>
                          <p className="font-medium">{trend.name}</p>
                          <p className="text-sm text-slate-400">Volume: {trend.betVolume}</p>
                        </div>
                        <div className="flex items-center space-x-2">
                          <span className={`text-sm font-medium ${
                            trend.direction === 'up' ? 'text-green-500' : 'text-red-500'
                          }`}>
                            {trend.change}
                          </span>
                          {trend.direction === 'up' ?
                            <ChevronUp className="h-4 w-4 text-green-500" /> :
                            <ChevronDown className="h-4 w-4 text-red-500" />
                          }
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DesktopDashboard;