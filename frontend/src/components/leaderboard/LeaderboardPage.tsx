import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Avatar } from '@/components/ui/avatar';
import { Slider } from '@/components/ui/slider';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import {
  Search,
  Star,
  Crown,
  Trophy,
  Gamepad2,
  TrendingUp,
  Clock,
  Flame,
  Link,
  Zap,
  DollarSign,
  Award,
  Briefcase,
  Shield,
  Target,
  Flag,
  ChevronDown,
  ChevronRight,
  ArrowRight,
  ArrowUp,
  ArrowDown,
  Clock8,
  User,
  Eye,
  MapPin,
  Plus,
  X,
  CheckSquare,
  Calendar,
  BarChart2,
  Video,
  MessageSquare,
  Share2,
  Bookmark,
  Info,
  Heart,
  ThumbsUp,
  Bell,
  Shuffle,
  Filter,
  SlidersHorizontal,
  Play,
  Users,
  Sword,
  Dices
} from 'lucide-react';
import leaderboardService, { type Player, type MatchPot } from '@/services/leaderboard';
import { useToast } from '@/components/ui/use-toast';


const LeaderboardPage = () => {
  const { toast } = useToast();
  const [selectedGame, setSelectedGame] = useState('all');
  const [selectedTimeframe, setSelectedTimeframe] = useState<'alltime' | 'monthly' | 'weekly' | 'daily'>('alltime');
  const [selectedLeague, setSelectedLeague] = useState<'all' | 'titan' | 'masters' | 'diamond'>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [players, setPlayers] = useState<Player[]>([]);
  const [matchPots, setMatchPots] = useState<MatchPot[]>([]);
  const [watchlist, setWatchlist] = useState<string[]>([]);
  const [selectedTab, setSelectedTab] = useState('leaderboard');
  const [selectedCountry, setSelectedCountry] = useState('all');
  const [showOnlineOnly, setShowOnlineOnly] = useState(false);
  const [sortBy, setSortBy] = useState('rank');
  const [selectedPlayer, setSelectedPlayer] = useState<Player | null>(null);
  const [showPlayerDialog, setShowPlayerDialog] = useState(false);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [isLoading, setIsLoading] = useState(true);
  const [stats, setStats] = useState({
    totalPlayers: 0,
    prizePool: 0,
    activeGames: 0,
    avgBet: 0
  });

  // Fetch leaderboard data
  const fetchLeaderboardData = async () => {
    try {
      const response = await leaderboardService.getLeaderboard({
        game: selectedGame !== 'all' ? selectedGame : undefined,
        timeframe: selectedTimeframe,
        league: selectedLeague,
        limit: 50
      });

      setPlayers(response.entries);
      setMatchPots(response.matchPots);
      setStats(response.stats);
    } catch (error) {
      console.error('Failed to fetch leaderboard:', error);
      toast({
        title: "Error",
        description: "Failed to load leaderboard data",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch user's watchlist
  const fetchWatchlist = async () => {
    try {
      const response = await leaderboardService.getWatchlist();
      setWatchlist(response.watchlist);
    } catch (error) {
      console.error('Failed to fetch watchlist:', error);
      // If not authenticated, just keep empty watchlist
      setWatchlist([]);
    }
  };

  useEffect(() => {
    fetchLeaderboardData();
    fetchWatchlist();
  }, [selectedGame, selectedTimeframe, selectedLeague]);

  useEffect(() => {
    if (autoRefresh) {
      const interval = setInterval(() => {
        fetchLeaderboardData();
        fetchWatchlist();
      }, 30000); // Refresh every 30 seconds

      return () => clearInterval(interval);
    }
  }, [autoRefresh, selectedGame, selectedTimeframe, selectedLeague]);

  // Handle watchlist toggle with API persistence
  const handleWatchlistToggle = async (playerId: string) => {
    try {
      if (watchlist.includes(playerId)) {
        await leaderboardService.removeFromWatchlist(playerId);
        setWatchlist(prev => prev.filter(id => id !== playerId));
        toast({
          title: "Removed from watchlist",
          description: "Player removed from your watchlist",
        });
      } else {
        await leaderboardService.addToWatchlist(playerId);
        setWatchlist(prev => [...prev, playerId]);
        toast({
          title: "Added to watchlist",
          description: "Player added to your watchlist",
        });
      }
    } catch (error: any) {
      console.error('Failed to update watchlist:', error);
      toast({
        title: "Error",
        description: error.response?.data?.detail || "Failed to update watchlist",
        variant: "destructive"
      });
    }
  };

  // Filter and sort players
  const filteredPlayers = players.filter(player => {
    const matchesSearch = player.username.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCountry = selectedCountry === 'all' || player.country === selectedCountry;
    const matchesOnline = !showOnlineOnly || player.isOnline;
    const matchesGame = selectedGame === 'all' || player.favoriteGame === selectedGame;
    const matchesLeague = selectedLeague === 'all' ||
      (selectedLeague === 'titan' && player.rank <= 10) ||
      (selectedLeague === 'masters' && player.rank > 10 && player.rank <= 50) ||
      (selectedLeague === 'diamond' && player.rank > 50 && player.rank <= 100);

    return matchesSearch && matchesCountry && matchesOnline && matchesGame && matchesLeague;
  }).sort((a, b) => {
    switch (sortBy) {
      case 'rank': return a.rank - b.rank;
      case 'earnings': return b.earnings - a.earnings;
      case 'winrate': return b.winrate - a.winrate;
      case 'streak': return b.streak - a.streak;
      case 'level': return b.level - a.level;
      default: return 0;
    }
  });

  // Helper functions
  const getBadgeInfo = (badge: string) => {
    const badges: Record<string, any> = {
      'titan': { icon: <Trophy className="h-3.5 w-3.5" />, color: 'bg-gradient-to-r from-amber-600 to-yellow-500', text: 'Titan League' },
      'millionaire': { icon: <DollarSign className="h-3.5 w-3.5" />, color: 'bg-gradient-to-r from-green-600 to-emerald-500', text: 'Millionaire' },
      'undefeated-10': { icon: <Flame className="h-3.5 w-3.5" />, color: 'bg-gradient-to-r from-red-600 to-orange-500', text: 'Undefeated 10+' },
      'streaker': { icon: <Zap className="h-3.5 w-3.5" />, color: 'bg-gradient-to-r from-yellow-600 to-amber-500', text: 'Streaker' },
      'comeback-king': { icon: <Crown className="h-3.5 w-3.5" />, color: 'bg-gradient-to-r from-purple-600 to-pink-500', text: 'Comeback King' }
    };
    return badges[badge] || { icon: <Star className="h-3.5 w-3.5" />, color: 'bg-slate-600', text: badge.replace(/-/g, ' ') };
  };

  const getRankIcon = (rank: number) => {
    if (rank === 1) return <Crown className="h-4 w-4 text-yellow-500" />;
    if (rank === 2) return <Crown className="h-4 w-4 text-slate-400" />;
    if (rank === 3) return <Crown className="h-4 w-4 text-amber-600" />;
    return null;
  };

  const getLeagueInfo = (rank: number) => {
    if (rank <= 10) return { name: 'Titan', color: 'text-yellow-500', bgColor: 'bg-yellow-500/10', icon: <Crown className="h-4 w-4" /> };
    if (rank <= 50) return { name: 'Masters', color: 'text-purple-500', bgColor: 'bg-purple-500/10', icon: <Shield className="h-4 w-4" /> };
    if (rank <= 100) return { name: 'Diamond', color: 'text-blue-500', bgColor: 'bg-blue-500/10', icon: <Target className="h-4 w-4" /> };
    return { name: 'Gold', color: 'text-amber-500', bgColor: 'bg-amber-500/10', icon: <Award className="h-4 w-4" /> };
  };

  return (
    <div className="min-h-screen bg-slate-950 text-white pt-16">
      {/* Main Grid Layout - 12 column system */}
      <div className="grid grid-cols-12 gap-1 p-1 h-[calc(100vh-64px)]">

        {/* Left Sidebar - Filters & Navigation (2 cols) */}
        <div className="col-span-2 flex flex-col gap-1 overflow-hidden">
          {/* Game Categories */}
          <div className="bg-slate-900 border border-slate-800 rounded-sm p-3">
            <h3 className="text-xs font-medium text-white mb-2">Game Categories</h3>
            <div className="space-y-1">
              <Button
                variant="ghost"
                size="sm"
                className={`w-full justify-start text-xs h-8 ${selectedGame === 'all' ? 'bg-slate-800' : ''}`}
                onClick={() => setSelectedGame('all')}
              >
                <Gamepad2 className="h-3 w-3 mr-2" />
                All Games
                <span className="ml-auto text-slate-400">1,234</span>
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className={`w-full justify-start text-xs h-8 ${selectedGame === 'Chess 1v1' ? 'bg-slate-800' : ''}`}
                onClick={() => setSelectedGame('Chess 1v1')}
              >
                <Sword className="h-3 w-3 mr-2" />
                Chess 1v1
                <span className="ml-auto text-slate-400">342</span>
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className={`w-full justify-start text-xs h-8 ${selectedGame === 'RPS Arena' ? 'bg-slate-800' : ''}`}
                onClick={() => setSelectedGame('RPS Arena')}
              >
                <Dices className="h-3 w-3 mr-2" />
                RPS Arena
                <span className="ml-auto text-slate-400">286</span>
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className={`w-full justify-start text-xs h-8 ${selectedGame === 'Trivia Master' ? 'bg-slate-800' : ''}`}
                onClick={() => setSelectedGame('Trivia Master')}
              >
                <BarChart2 className="h-3 w-3 mr-2" />
                Trivia Master
                <span className="ml-auto text-slate-400">198</span>
              </Button>
            </div>
          </div>

          {/* League Filter */}
          <div className="bg-slate-900 border border-slate-800 rounded-sm p-3">
            <h3 className="text-xs font-medium text-white mb-2">Leagues</h3>
            <div className="space-y-1">
              <Button
                variant="ghost"
                size="sm"
                className={`w-full justify-start text-xs h-8 ${selectedLeague === 'all' ? 'bg-slate-800' : ''}`}
                onClick={() => setSelectedLeague('all')}
              >
                <Trophy className="h-3 w-3 mr-2" />
                All Leagues
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className={`w-full justify-start text-xs h-8 ${selectedLeague === 'titan' ? 'bg-slate-800' : ''}`}
                onClick={() => setSelectedLeague('titan')}
              >
                <Crown className="h-3 w-3 mr-2 text-yellow-500" />
                Titan League
                <Badge className="ml-auto h-4 bg-yellow-500/20 text-yellow-300">Top 10</Badge>
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className={`w-full justify-start text-xs h-8 ${selectedLeague === 'masters' ? 'bg-slate-800' : ''}`}
                onClick={() => setSelectedLeague('masters')}
              >
                <Shield className="h-3 w-3 mr-2 text-purple-500" />
                Masters League
                <Badge className="ml-auto h-4 bg-purple-500/20 text-purple-300">11-50</Badge>
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className={`w-full justify-start text-xs h-8 ${selectedLeague === 'diamond' ? 'bg-slate-800' : ''}`}
                onClick={() => setSelectedLeague('diamond')}
              >
                <Target className="h-3 w-3 mr-2 text-blue-500" />
                Diamond League
                <Badge className="ml-auto h-4 bg-blue-500/20 text-blue-300">51-100</Badge>
              </Button>
            </div>
          </div>

          {/* Timeframe Filter */}
          <div className="bg-slate-900 border border-slate-800 rounded-sm p-3">
            <h3 className="text-xs font-medium text-white mb-2">Timeframe</h3>
            <div className="space-y-1">
              <Button
                variant="ghost"
                size="sm"
                className={`w-full justify-start text-xs h-8 ${selectedTimeframe === 'daily' ? 'bg-slate-800' : ''}`}
                onClick={() => setSelectedTimeframe('daily')}
              >
                <Clock className="h-3 w-3 mr-2" />
                Today
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className={`w-full justify-start text-xs h-8 ${selectedTimeframe === 'weekly' ? 'bg-slate-800' : ''}`}
                onClick={() => setSelectedTimeframe('weekly')}
              >
                <Calendar className="h-3 w-3 mr-2" />
                This Week
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className={`w-full justify-start text-xs h-8 ${selectedTimeframe === 'monthly' ? 'bg-slate-800' : ''}`}
                onClick={() => setSelectedTimeframe('monthly')}
              >
                <Calendar className="h-3 w-3 mr-2" />
                This Month
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className={`w-full justify-start text-xs h-8 ${selectedTimeframe === 'alltime' ? 'bg-slate-800' : ''}`}
                onClick={() => setSelectedTimeframe('alltime')}
              >
                <Trophy className="h-3 w-3 mr-2" />
                All Time
              </Button>
            </div>
          </div>

          {/* Additional Filters */}
          <div className="bg-slate-900 border border-slate-800 rounded-sm p-3">
            <h3 className="text-xs font-medium text-white mb-2">Filters</h3>
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Switch
                  id="online-only"
                  checked={showOnlineOnly}
                  onCheckedChange={setShowOnlineOnly}
                />
                <label htmlFor="online-only" className="text-xs text-slate-300">
                  Online Only
                </label>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="auto-refresh"
                  checked={autoRefresh}
                  onCheckedChange={setAutoRefresh}
                />
                <label htmlFor="auto-refresh" className="text-xs text-slate-300">
                  Auto Refresh
                </label>
              </div>
            </div>
          </div>
        </div>

        {/* Center Main Content (7 cols) */}
        <div className="col-span-7 bg-slate-900 border border-slate-800 rounded-sm p-3 overflow-hidden flex flex-col">
          {/* Header */}
          <div className="flex items-center justify-between mb-4">
            <div>
              <h1 className="text-2xl font-bold">Leaderboard</h1>
              <p className="text-sm text-slate-400">Top players across all games</p>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" className="text-xs">
                <Share2 className="h-3 w-3 mr-1" />
                Export
              </Button>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm" className="text-xs">
                    <SlidersHorizontal className="h-3 w-3 mr-1" />
                    View
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuItem>Compact View</DropdownMenuItem>
                  <DropdownMenuItem>Detailed View</DropdownMenuItem>
                  <DropdownMenuItem>Card View</DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>

          {/* Search and Sort Bar */}
          <div className="flex items-center gap-2 mb-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-slate-400" />
              <Input
                placeholder="Search players..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-9 bg-slate-800 border-slate-700 text-sm h-9"
              />
            </div>
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger className="w-[140px] bg-slate-800 border-slate-700 text-sm h-9">
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="rank">Rank</SelectItem>
                <SelectItem value="earnings">Earnings</SelectItem>
                <SelectItem value="winrate">Win Rate</SelectItem>
                <SelectItem value="streak">Streak</SelectItem>
                <SelectItem value="level">Level</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Player Table */}
          <div className="flex-1 overflow-y-auto">
            {isLoading ? (
              <div className="flex items-center justify-center py-8">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
                  <p className="text-sm text-slate-400">Loading leaderboard...</p>
                </div>
              </div>
            ) : (
            <div className="grid grid-cols-1 gap-1">
              {filteredPlayers.slice(0, 20).map((player) => (
                <div
                  key={player.id}
                  className="bg-slate-800 rounded-sm p-3 hover:bg-slate-700 transition-colors cursor-pointer"
                  onClick={() => {
                    setSelectedPlayer(player);
                    setShowPlayerDialog(true);
                  }}
                >
                  <div className="flex items-center gap-3">
                    {/* Rank */}
                    <div className="w-12 text-center">
                      <div className="flex items-center justify-center gap-1">
                        {getRankIcon(player.rank)}
                        <span className="font-bold text-sm">{player.rank}</span>
                      </div>
                    </div>

                    {/* Player Info */}
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <span className="font-medium">{player.username}</span>
                        {player.isOnline && (
                          <div className="h-2 w-2 bg-green-500 rounded-full animate-pulse" />
                        )}
                        <div className={`px-2 py-0.5 rounded-full text-xs font-medium ${getLeagueInfo(player.rank).bgColor} ${getLeagueInfo(player.rank).color}`}>
                          {getLeagueInfo(player.rank).name}
                        </div>
                      </div>
                      <div className="flex items-center gap-4 mt-1 text-xs text-slate-400">
                        <span>{player.country}</span>
                        <span>{player.favoriteGame}</span>
                        <span>Level {player.level}</span>
                      </div>
                    </div>

                    {/* Stats */}
                    <div className="grid grid-cols-3 gap-4 text-center">
                      <div>
                        <p className="text-xs text-slate-400">Win Rate</p>
                        <p className="font-bold text-green-400">{player.winrate}%</p>
                      </div>
                      <div>
                        <p className="text-xs text-slate-400">Earnings</p>
                        <p className="font-bold text-yellow-400">${player.earnings.toLocaleString()}</p>
                      </div>
                      <div>
                        <p className="text-xs text-slate-400">Streak</p>
                        <p className="font-bold text-orange-400">{player.streak} 🔥</p>
                      </div>
                    </div>

                    {/* Badges */}
                    <div className="flex gap-1">
                      {player.badges.slice(0, 3).map((badge, idx) => {
                        const badgeInfo = getBadgeInfo(badge);
                        return (
                          <TooltipProvider key={idx}>
                            <Tooltip>
                              <TooltipTrigger>
                                <div className={`p-1.5 rounded-full ${badgeInfo.color}`}>
                                  {badgeInfo.icon}
                                </div>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>{badgeInfo.text}</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        );
                      })}
                    </div>

                    {/* Actions */}
                    <div className="flex items-center gap-2">
                      <Button
                        size="sm"
                        variant={watchlist.includes(player.id) ? "secondary" : "outline"}
                        className="h-7 text-xs"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleWatchlistToggle(player.id);
                        }}
                      >
                        {watchlist.includes(player.id) ? (
                          <>
                            <CheckSquare className="h-3 w-3 mr-1" />
                            Watching
                          </>
                        ) : (
                          <>
                            <Eye className="h-3 w-3 mr-1" />
                            Watch
                          </>
                        )}
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
            )}
          </div>
        </div>

        {/* Right Sidebar (3 cols) */}
        <div className="col-span-3 flex flex-col gap-1 overflow-hidden">
          {/* Match Pots */}
          <div className="bg-slate-900 border border-slate-800 rounded-sm p-3 flex-1 overflow-hidden">
            <div className="flex items-center justify-between mb-3">
              <h3 className="text-sm font-medium">Live Match Pots</h3>
              <Button variant="ghost" size="sm" className="h-6 text-xs">
                View All
              </Button>
            </div>
            <div className="space-y-2 overflow-y-auto max-h-[300px]">
              {matchPots.slice(0, 5).map((pot) => (
                <Card key={pot.id} className="bg-slate-800 border-slate-700">
                  <CardContent className="p-3">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-2">
                        <Badge variant={pot.status === 'waiting' ? 'secondary' : 'default'} className="text-xs">
                          {pot.status}
                        </Badge>
                        <span className="text-sm font-medium">{pot.game}</span>
                      </div>
                      <span className="text-sm font-bold text-green-400">${pot.totalPot}</span>
                    </div>
                    <div className="grid grid-cols-2 gap-2 text-xs text-slate-400">
                      <div>
                        <p>Players: {pot.currentPlayers.length}/{pot.players}</p>
                        <p>Entry: ${pot.entryFee}</p>
                      </div>
                      <div>
                        <p>Format: {pot.format}</p>
                        <p>Starts: {new Date(pot.startTime).toLocaleTimeString()}</p>
                      </div>
                    </div>
                    <Progress value={(pot.currentPlayers.length / pot.players) * 100} className="h-1 mt-2" />
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Watchlist */}
          <div className="bg-slate-900 border border-slate-800 rounded-sm p-3 flex-1 overflow-hidden">
            <div className="flex items-center justify-between mb-3">
              <h3 className="text-sm font-medium">Your Watchlist</h3>
              <Badge variant="secondary" className="text-xs">{watchlist.length}</Badge>
            </div>
            <div className="space-y-2 overflow-y-auto max-h-[300px]">
              {watchlist.length === 0 ? (
                <p className="text-xs text-slate-400 text-center py-4">
                  No players in watchlist
                </p>
              ) : (
                players
                  .filter(p => watchlist.includes(p.id))
                  .map((player) => (
                    <div key={player.id} className="bg-slate-800 rounded-sm p-2">
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="flex items-center gap-2">
                            <span className="text-sm font-medium">{player.username}</span>
                            {player.isOnline && (
                              <div className="h-2 w-2 bg-green-500 rounded-full" />
                            )}
                          </div>
                          <p className="text-xs text-slate-400">#{player.rank} • {player.favoriteGame}</p>
                        </div>
                        <div className="text-right">
                          <p className="text-sm font-bold text-green-400">{player.winrate}%</p>
                          <p className="text-xs text-slate-400">Win Rate</p>
                        </div>
                      </div>
                    </div>
                  ))
              )}
            </div>
          </div>

          {/* Stats Summary */}
          <div className="bg-slate-900 border border-slate-800 rounded-sm p-3">
            <h3 className="text-sm font-medium mb-3">Quick Stats</h3>
            <div className="grid grid-cols-2 gap-3">
              <div className="bg-slate-800 rounded-sm p-2">
                <p className="text-xs text-slate-400">Total Players</p>
                <p className="text-lg font-bold">{stats.totalPlayers.toLocaleString()}</p>
                <p className="text-xs text-green-400">Active users</p>
              </div>
              <div className="bg-slate-800 rounded-sm p-2">
                <p className="text-xs text-slate-400">Prize Pool</p>
                <p className="text-lg font-bold">${(stats.prizePool / 1000).toFixed(1)}K</p>
                <p className="text-xs text-yellow-400">Total pot</p>
              </div>
              <div className="bg-slate-800 rounded-sm p-2">
                <p className="text-xs text-slate-400">Active Games</p>
                <p className="text-lg font-bold">{stats.activeGames.toLocaleString()}</p>
                <p className="text-xs text-blue-400">Live now</p>
              </div>
              <div className="bg-slate-800 rounded-sm p-2">
                <p className="text-xs text-slate-400">Avg. Bet</p>
                <p className="text-lg font-bold">${stats.avgBet.toFixed(0)}</p>
                <p className="text-xs text-purple-400">Per game</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Player Profile Dialog */}
      <Dialog open={showPlayerDialog} onOpenChange={setShowPlayerDialog}>
        <DialogContent className="max-w-2xl bg-slate-900 border-slate-800">
          {selectedPlayer && (
            <>
              <DialogHeader>
                <DialogTitle className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <span>{selectedPlayer.username}</span>
                    {selectedPlayer.isOnline && (
                      <div className="h-3 w-3 bg-green-500 rounded-full animate-pulse" />
                    )}
                  </div>
                  <div className="flex items-center gap-2">
                    {getRankIcon(selectedPlayer.rank)}
                    <span>#{selectedPlayer.rank}</span>
                  </div>
                </DialogTitle>
                <DialogDescription>
                  {selectedPlayer.country} • Level {selectedPlayer.level} • {selectedPlayer.favoriteGame} Specialist
                </DialogDescription>
              </DialogHeader>

              <div className="grid grid-cols-2 gap-4 mt-4">
                <Card className="bg-slate-800 border-slate-700">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm">Performance Stats</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-sm text-slate-400">Win Rate</span>
                        <span className="font-bold text-green-400">{selectedPlayer.winrate}%</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-slate-400">Total Earnings</span>
                        <span className="font-bold text-yellow-400">${selectedPlayer.earnings.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-slate-400">Current Streak</span>
                        <span className="font-bold text-orange-400">{selectedPlayer.streak} 🔥</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-slate-400">Games Played</span>
                        <span className="font-bold">{selectedPlayer.totalGamesPlayed}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-slate-800 border-slate-700">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm">Recent Performance</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-slate-400">Daily P/L</span>
                        <span className={`font-bold ${selectedPlayer.dailyProfit >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                          {selectedPlayer.dailyProfit >= 0 ? '+' : ''}{selectedPlayer.dailyProfit.toFixed(2)}
                        </span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-slate-400">Weekly P/L</span>
                        <span className={`font-bold ${selectedPlayer.weeklyProfit >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                          {selectedPlayer.weeklyProfit >= 0 ? '+' : ''}{selectedPlayer.weeklyProfit.toFixed(2)}
                        </span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-slate-400">Monthly P/L</span>
                        <span className={`font-bold ${selectedPlayer.monthlyProfit >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                          {selectedPlayer.monthlyProfit >= 0 ? '+' : ''}{selectedPlayer.monthlyProfit.toFixed(2)}
                        </span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-slate-400">Trend</span>
                        <Badge variant={
                          selectedPlayer.recentPerformance === 'improving' ? 'default' :
                          selectedPlayer.recentPerformance === 'declining' ? 'destructive' :
                          'secondary'
                        }>
                          {selectedPlayer.recentPerformance}
                        </Badge>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              <div className="mt-4">
                <h4 className="text-sm font-medium mb-2">Achievements</h4>
                <div className="flex gap-2 flex-wrap">
                  {selectedPlayer.badges.map((badge, idx) => {
                    const badgeInfo = getBadgeInfo(badge);
                    return (
                      <div key={idx} className={`px-3 py-1.5 rounded-full flex items-center gap-2 ${badgeInfo.color}`}>
                        {badgeInfo.icon}
                        <span className="text-xs font-medium">{badgeInfo.text}</span>
                      </div>
                    );
                  })}
                </div>
              </div>

              <DialogFooter className="mt-4">
                <Button variant="outline" onClick={() => setShowPlayerDialog(false)}>
                  Close
                </Button>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Challenge Player
                </Button>
              </DialogFooter>
            </>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default LeaderboardPage;