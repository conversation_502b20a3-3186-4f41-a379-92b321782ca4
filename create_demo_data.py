#!/usr/bin/env python3
"""
Create demo data for BetBet platform.
This script creates sample users, games, and wallet balances for testing.
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy.orm import Session
from app.database.db import SessionLocal, engine, Base
from app.models import *
from app.auth.jwt import get_password_hash
import uuid
from datetime import datetime, timedelta
import random

def create_demo_users(db: Session):
    """Create demo users with wallets."""
    print("👥 Creating demo users...")

    demo_users = [
        {
            "username": "alice_gamer",
            "email": "<EMAIL>",
            "full_name": "<PERSON>",
            "balance": 1000.0
        },
        {
            "username": "bob_player",
            "email": "<EMAIL>",
            "full_name": "<PERSON>",
            "balance": 750.0
        },
        {
            "username": "charlie_pro",
            "email": "<EMAIL>",
            "full_name": "<PERSON>",
            "balance": 1500.0
        },
        {
            "username": "diana_chess",
            "email": "<EMAIL>",
            "full_name": "<PERSON>",
            "balance": 2000.0
        },
        {
            "username": "eve_spectator",
            "email": "<EMAIL>",
            "full_name": "<PERSON> <PERSON>",
            "balance": 500.0
        }
    ]

    created_users = []

    for user_data in demo_users:
        # Check if user already exists
        existing_user = db.query(User).filter(User.username == user_data["username"]).first()
        if existing_user:
            print(f"  ⚠️  User {user_data['username']} already exists")
            created_users.append(existing_user)
            continue

        # Create user
        user = User(
            id=str(uuid.uuid4()),
            username=user_data["username"],
            email=user_data["email"],
            real_name=user_data["full_name"],
            hashed_password=get_password_hash("demo123"),  # All demo users have password "demo123"
            is_active=True,
            is_verified=True
        )

        db.add(user)
        db.flush()  # Get the user ID

        # Create wallet
        wallet = Wallet(
            user_id=user.id,
            balance=user_data["balance"]
        )

        db.add(wallet)
        created_users.append(user)
        print(f"  ✅ Created user: {user.username} (${user_data['balance']})")

    db.commit()
    return created_users

def create_demo_games(db: Session, users: list):
    """Create demo games in various states."""
    print("🎮 Creating demo games...")

    games_data = [
        {
            "type": GameType.ROCK_PAPER_SCISSORS,
            "status": GameStatus.WAITING,
            "player1": users[0],
            "player2": None,
            "wager": 50.0,
            "settings": {"rounds": 3}
        },
        {
            "type": GameType.ROCK_PAPER_SCISSORS,
            "status": GameStatus.IN_PROGRESS,
            "player1": users[1],
            "player2": users[2],
            "wager": 100.0,
            "settings": {"rounds": 5},
            "state": {
                "current_round": 2,
                "scores": {users[1].id: 1, users[2].id: 0},
                "moves": {
                    "1": {users[1].id: "rock", users[2].id: "scissors"}
                }
            }
        },
        {
            "type": GameType.CHESS,
            "status": GameStatus.WAITING,
            "player1": users[3],
            "player2": None,
            "wager": 200.0,
            "settings": {"time_limit": 600}
        },
        {
            "type": GameType.ROCK_PAPER_SCISSORS,
            "status": GameStatus.COMPLETED,
            "player1": users[0],
            "player2": users[4],
            "wager": 75.0,
            "winner": users[0],
            "settings": {"rounds": 3},
            "state": {
                "current_round": 3,
                "scores": {users[0].id: 2, users[4].id: 1},
                "final": True
            }
        }
    ]

    created_games = []

    for game_data in games_data:
        game = Game(
            id=str(uuid.uuid4()),
            game_type=game_data["type"],
            status=game_data["status"],
            player1_id=game_data["player1"].id,
            player2_id=game_data["player2"].id if game_data["player2"] else None,
            current_player_id=game_data["player1"].id if game_data["status"] == GameStatus.IN_PROGRESS else None,
            winner_id=game_data.get("winner").id if game_data.get("winner") else None,
            wager_amount=game_data["wager"],
            total_pot=game_data["wager"] * 2 if game_data["player2"] else game_data["wager"],
            settings=game_data["settings"],
            state=game_data.get("state", {}),
            created_at=datetime.utcnow() - timedelta(minutes=random.randint(5, 120)),
            started_at=datetime.utcnow() - timedelta(minutes=random.randint(1, 60)) if game_data["status"] != GameStatus.WAITING else None,
            completed_at=datetime.utcnow() - timedelta(minutes=random.randint(1, 30)) if game_data["status"] == GameStatus.COMPLETED else None
        )

        db.add(game)
        created_games.append(game)

        status_emoji = {"waiting": "⏳", "in_progress": "🎮", "completed": "✅"}
        print(f"  {status_emoji.get(game_data['status'].value, '🎯')} {game_data['type'].value} - {game_data['status'].value} (${game_data['wager']})")

    db.commit()
    return created_games

def create_demo_spectators(db: Session, games: list, users: list):
    """Add spectators to ongoing games."""
    print("👀 Adding spectators...")

    # Add spectators to in-progress games
    in_progress_games = [g for g in games if g.status == GameStatus.IN_PROGRESS]

    for game in in_progress_games:
        # Add 1-3 random spectators (excluding players)
        available_spectators = [u for u in users if u.id not in [game.player1_id, game.player2_id]]
        num_spectators = random.randint(1, min(3, len(available_spectators)))

        selected_spectators = random.sample(available_spectators, num_spectators)

        for spectator in selected_spectators:
            game_spectator = GameSpectator(
                id=str(uuid.uuid4()),
                game_id=game.id,
                user_id=spectator.id,
                joined_at=datetime.utcnow() - timedelta(minutes=random.randint(1, 30))
            )
            db.add(game_spectator)
            print(f"  👁️  {spectator.username} spectating {game.game_type.value}")

    db.commit()

def main():
    """Main function to create all demo data."""
    print("🚀 Creating BetBet Demo Data")
    print("=" * 40)

    # Create database tables
    Base.metadata.create_all(bind=engine)

    # Create database session
    db = SessionLocal()

    try:
        # Create demo data
        users = create_demo_users(db)
        games = create_demo_games(db, users)
        create_demo_spectators(db, games, users)

        print("\n✅ Demo data created successfully!")
        print("\n📊 Summary:")
        print(f"  👥 Users: {len(users)}")
        print(f"  🎮 Games: {len(games)}")
        print(f"  💰 Total pot value: ${sum(g.total_pot for g in games)}")

        print("\n🔑 Demo Login Credentials:")
        print("  Username: alice_gamer | Password: demo123")
        print("  Username: bob_player  | Password: demo123")
        print("  Username: charlie_pro | Password: demo123")
        print("  Username: diana_chess | Password: demo123")
        print("  Username: eve_spectator | Password: demo123")

    except Exception as e:
        print(f"❌ Error creating demo data: {e}")
        db.rollback()
        raise
    finally:
        db.close()

if __name__ == "__main__":
    main()
