#!/usr/bin/env python3
"""Test the leaderboard API endpoint"""

import requests
import json
import sys

def test_leaderboard_api():
    base_url = "http://localhost:8000"
    
    # Test basic leaderboard endpoint
    print("Testing GET /api/leaderboard...")
    try:
        response = requests.get(
            f"{base_url}/api/leaderboard",
            params={
                "timeframe": "alltime",
                "limit": 5
            },
            timeout=10
        )
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("\nResponse:")
            print(json.dumps(data, indent=2))
            
            # Validate response structure
            assert "entries" in data, "Missing 'entries' in response"
            assert "matchPots" in data, "Missing 'matchPots' in response"
            assert "stats" in data, "Missing 'stats' in response"
            
            print("\n✅ Leaderboard API test passed!")
        else:
            print(f"\n❌ Error: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("\n❌ Error: Could not connect to server. Is it running?")
        sys.exit(1)
    except requests.exceptions.Timeout:
        print("\n❌ Error: Request timed out")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Error: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    test_leaderboard_api()