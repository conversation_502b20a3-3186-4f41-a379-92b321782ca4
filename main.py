from fastapi import <PERSON>AP<PERSON>, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.exceptions import RequestValidationError
from contextlib import asynccontextmanager
from slowapi import _rate_limit_exceeded_handler
from slowapi.errors import RateLimitExceeded
from sqlalchemy.orm import Session
from datetime import datetime
import uuid

from app.database.db import engine, Base, get_db
from app.core.config import settings
from app.core.rate_limit import limiter, RateLimitMiddleware
from app.core.exceptions import (
    BetBetException,
    betbet_exception_handler,
    validation_exception_handler,
    generic_exception_handler,
    RateLimitError
)
from app.auth.jwt import get_password_hash

# Import WebSocket components (temporarily disabled due to Redis conflict)
# from app.core.websocket import sio
# from app.api import websocket_events  # Import to register event handlers

# Import all routers
from app.api import auth, users, wallet, kyc, admin, payment_gateway, games, sports, marketplace, leaderboard, expert_picks, game_instances, notifications, game_catalog_api, enhanced_game_management

# Import models for startup operations
from app.models.user import User

# Create database tables
Base.metadata.create_all(bind=engine)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Handle application startup and shutdown."""
    # Startup
    print("Starting up BetBet API...")

    # Create first superuser if needed
    db = next(get_db())
    user = db.query(User).filter(User.email == settings.FIRST_SUPERUSER_EMAIL).first()

    if not user:
        # Create superuser
        superuser = User(
            id=str(uuid.uuid4()),
            username=settings.FIRST_SUPERUSER_USERNAME,
            email=settings.FIRST_SUPERUSER_EMAIL,
            hashed_password=get_password_hash(settings.FIRST_SUPERUSER_PASSWORD),
            real_name="Admin User",
            is_active=True,
            is_verified=True,
            is_superuser=True,
            created_at=datetime.utcnow(),
            status="Admin",
            kyc_status="approved",
            level=99,
            xp=999999,
            next_level_xp=999999,
            open_to_challenges=False
        )
        db.add(superuser)
        db.commit()
        print(f"Created superuser: {superuser.email}")

    yield

    # Shutdown
    print("Shutting down BetBet API...")

app = FastAPI(
    title=settings.PROJECT_NAME,
    version="1.0.0",
    lifespan=lifespan
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add rate limiting middleware
app.add_middleware(RateLimitMiddleware)

# Add rate limiter to app state
app.state.limiter = limiter

# Add exception handlers
app.add_exception_handler(BetBetException, betbet_exception_handler)
app.add_exception_handler(RequestValidationError, validation_exception_handler)
app.add_exception_handler(RateLimitExceeded, _rate_limit_exceeded_handler)
app.add_exception_handler(Exception, generic_exception_handler)

# Mount static files for uploads
app.mount("/uploads", StaticFiles(directory="uploads"), name="uploads")

# Include all routers with consistent naming
app.include_router(auth.router, prefix=f"{settings.API_V1_STR}/auth", tags=["Authentication"])
app.include_router(users.router, prefix=f"{settings.API_V1_STR}/users", tags=["Users"])
app.include_router(wallet.router, prefix=f"{settings.API_V1_STR}/wallet", tags=["Wallet"])
app.include_router(kyc.router, prefix=f"{settings.API_V1_STR}/kyc", tags=["KYC"])
app.include_router(admin.router, prefix=f"{settings.API_V1_STR}/admin", tags=["Admin"])
app.include_router(payment_gateway.router, prefix=f"{settings.API_V1_STR}/payments", tags=["Payments"])
app.include_router(marketplace.router, prefix=f"{settings.API_V1_STR}/bets", tags=["Marketplace"])
app.include_router(games.router, prefix=f"{settings.API_V1_STR}/games-old", tags=["Games"])
app.include_router(sports.router, prefix=f"{settings.API_V1_STR}/sports", tags=["Sports"])
app.include_router(leaderboard.router, prefix=f"{settings.API_V1_STR}/leaderboard", tags=["Leaderboard"])
app.include_router(expert_picks.router, prefix=f"{settings.API_V1_STR}", tags=["Expert Picks"])
app.include_router(game_instances.router, prefix=f"{settings.API_V1_STR}/game-instances", tags=["Game Instances"])
app.include_router(game_catalog_api.router, prefix=f"{settings.API_V1_STR}/games", tags=["Game Catalog"])
app.include_router(enhanced_game_management.router, prefix=f"{settings.API_V1_STR}/game-sessions", tags=["Game Sessions"])
app.include_router(notifications.router, prefix=f"{settings.API_V1_STR}/notifications", tags=["Notifications"])

# Add WebSocket endpoint
from app.api.websocket import websocket_router
app.include_router(websocket_router)

@app.get("/")
async def root():
    return {
        "message": "Welcome to BetBet API",
        "version": "1.0.0",
        "docs": "/docs",
        "redoc": "/redoc"
    }

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "environment": settings.ENVIRONMENT,
        "timestamp": datetime.utcnow().isoformat()
    }

@app.exception_handler(RateLimitError)
async def rate_limit_error_handler(request, exc):
    """Handle custom rate limit errors."""
    raise HTTPException(
        status_code=429,
        detail={
            "error": "Rate limit exceeded",
            "limit": exc.limit,
            "window": exc.window,
            "retry_after": exc.retry_after
        },
        headers={"Retry-After": str(exc.retry_after)}
    )

# Create Socket.IO app wrapper
import socketio
socket_app = socketio.ASGIApp(sio, app)

if __name__ == "__main__":
    import uvicorn
    # Run with Socket.IO enabled
    uvicorn.run(
        "main:socket_app",  # Use Socket.IO app
        host="0.0.0.0",
        port=8000,
        reload=True if settings.ENVIRONMENT == "development" else False
    )